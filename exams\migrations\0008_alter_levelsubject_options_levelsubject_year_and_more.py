# Generated by Django 4.2.4 on 2023-08-12 19:01

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('school', '0028_alter_related_names_for_level_and_generic_level_fields'),
        ('exams', '0007_alter_subject_code'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='levelsubject',
            options={'ordering': ['order'], 'verbose_name': 'matière par niveau', 'verbose_name_plural': 'matières par niveau'},
        ),
        migrations.AddField(
            model_name='levelsubject',
            name='year',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, to='school.year'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='subject',
            name='abbreviation',
            field=models.CharField(help_text='8 caractères au maximum', max_length=10, verbose_name='abbréviation'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='subject',
            name='name',
            field=models.CharField(max_length=255, unique=True, verbose_name='nom de la matière'),
        ),
    ]
