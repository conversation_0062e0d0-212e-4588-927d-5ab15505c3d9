tinyMCE.addI18n("cs.grappelli_contextmenu",{
grappelli_contextmenu_insertpbefore_desc:"<PERSON><PERSON>ž odstavec PŘED aktuální element",
grappelli_contextmenu_insertpafter_desc:"V<PERSON>ž odstavec ZA aktuální element",
grappelli_contextmenu_insertpbeforeroot_desc:"V<PERSON>ž odstavec PŘED aktuální KOŘENOVÝ element",
grappelli_contextmenu_insertpafterroot_desc:"<PERSON><PERSON><PERSON> odstavec ZA aktuální KOŘENOVÝ element",
grappelli_contextmenu_delete_desc:"Smazat aktuální element",
grappelli_contextmenu_deleteroot_desc:"Smazat aktuální KOŘENOVÝ element",
grappelli_contextmenu_moveup_desc:"Posunout element NAHORU",
grappelli_contextmenu_moveuproot_desc:"Posunout aktuální kořenový element NAHORU",

P_grappelli_contextmenu_insertpbefore_desc:"<PERSON><PERSON><PERSON> odstavec PŘED aktuální element",
P_grappelli_contextmenu_insertpafter_desc:"Vlož odstavec ZA aktuální element",
P_grappelli_contextmenu_insertpbeforeroot_desc:"Vlož odstavec PŘED aktuální KOŘENOVÝ element",
P_grappelli_contextmenu_insertpafterroot_desc:"Vlož odstavec ZA aktuální KOŘENOVÝ element",
P_grappelli_contextmenu_delete_desc:"Smazat aktuální element",
P_grappelli_contextmenu_deleteroot_desc:"Smazat aktuální KOŘENOVÝ element",
P_grappelli_contextmenu_moveup_desc:"Posunout element NAHORU",
P_grappelli_contextmenu_moveuproot_desc:"Posunout aktuální kořenový element NAHORU",
});
