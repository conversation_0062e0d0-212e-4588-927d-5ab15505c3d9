from django.contrib.auth import get_user_model
from django.contrib.auth.models import Permission
from django.test import RequestFactory, TestCase
from django.urls import reverse
from model_bakery import baker
from .. import models, views
from main import utils

from django.conf import settings as django_settings
from django.test import TestCase as TestCase
from importlib import import_module


class SessionEnabledTestCase(TestCase):

    def get_session(self):
        if self.client.session:
            session = self.client.session
        else:
            engine = import_module(django_settings.SESSION_ENGINE)
            session = engine.SessionStore()
        return session

    def set_session_cookies(self, session):
        # Set the cookie to represent the session
        session_cookie = django_settings.SESSION_COOKIE_NAME
        self.client.cookies[session_cookie] = session.session_key
        cookie_data = {
            'max-age': None,
            'path': '/',
            'domain': django_settings.SESSION_COOKIE_DOMAIN,
            'secure': django_settings.SESSION_COOKIE_SECURE or None,
            'expires': None}
        self.client.cookies[session_cookie].update(cookie_data)

htmx_headers = {'HTTP_HX_Request':'true'}


class HomePageViewTests(SessionEnabledTestCase):
    def setUp(self):
        self.url = reverse('school:home')
        self.user = baker.make(get_user_model())
        self.year = baker.make(models.Year, active=True)
        self.school = baker.make(models.School, name='OUR SCHOOL')
        session = self.get_session()
        session[f'{self.user.id}'] = self.year.name
        session.save()
        self.set_session_cookies(session)
    
    def test_home_page_for_anonymous_users(self):
        resp = self.client.get(self.url)
        self.assertEqual(resp.status_code, 302)
        self.assertRedirects(resp, f"{reverse('login')}?next=/")
    
    def test_home_page_for_authenticated_users(self):
        self.client.force_login(self.user)
        resp = self.client.get(self.url)
        self.assertEqual(resp.status_code, 200)
        self.assertTemplateUsed(resp, 'index.html')
    
    def test_context_data(self):
        self.client.force_login(self.user)
        resp = self.client.get(self.url)
        context = resp.context
        self.assertEqual(context['ROLE_ACCOUNTANT'], utils.ROLE_ACCOUNTANT)
        self.assertEqual(context['ROLE_FOUNDER'], utils.ROLE_FOUNDER)

    def test_data_returned_for_different_user_roles(self):
        accountant1 = baker.make(
            get_user_model(), 
            role=utils.ROLE_ACCOUNTANT,
            school=self.school)
        accountant2 = baker.make(
            get_user_model(), 
            role=utils.ROLE_ACCOUNTANT, 
            school=self.school)
        
        school = baker.make(models.School, name='OTHER SCHOOL')
        accountant3 = baker.make(
            get_user_model(), 
            role=utils.ROLE_ACCOUNTANT, 
            school=school)
        founder = baker.make(get_user_model(), role=utils.ROLE_FOUNDER,
            school=self.school)
        
        # Payment by accountant 1
        enrollment = baker.make(
            models.Enrollment, school=self.school, year=self.year,
            student=baker.make(models.Student, first_name='STUDENT 1', 
                               last_name='LAST NAME'))
        payment = baker.make(models.Payment, enrollment=enrollment, 
            amount=1000, agent=accountant1)
        
        # Payment by accountant 2
        enrollment = baker.make(
            models.Enrollment, school=self.school, year=self.year,
            student=baker.make(models.Student, first_name='STUDENT 2', 
                               last_name='LAST NAME'))
        payment = baker.make(models.Payment, enrollment=enrollment, 
            amount=3000, agent=accountant2)
        
        # Payment by accountant 3 from another school
        student = baker.make(models.Student, last_name='ABDOULAYE', 
                  first_name='SABA')
        enrollment = baker.make(
            models.Enrollment, student=student, year=self.year, 
            school=school)
        payment = baker.make(models.Payment, enrollment=enrollment, 
            amount=5000, agent=accountant3)

        # Login as accountant 1
        self.client.force_login(accountant1)
        resp = self.client.get(reverse('school:home'))
        self.assertEqual(resp.context['data']['total_paid'], 1000)
        
        # Login as accountant 2
        self.client.force_login(accountant2)
        resp = self.client.get(reverse('school:home'))
        self.assertEqual(resp.context['data']['total_paid'], 3000)
        
        # Login as accountant 3
        self.client.force_login(accountant3)
        resp = self.client.get(reverse('school:home'))
        self.assertEqual(resp.context['data']['total_paid'], 5000)
        
        # Login as founder
        self.client.force_login(founder)
        resp = self.client.get(reverse('school:home'))
        self.assertEqual(resp.context['data']['total_paid'], 4000)


class StudentsListViewTests(SessionEnabledTestCase):
    def setUp(self):
        self.url = reverse('school:students')
        self.school = baker.make(models.School)
        self.username = 'username'
        self.password = 'password'
        self.user = baker.make(get_user_model(), school=self.school, 
                    username=self.username, password=self.password)
        self.user.user_permissions.add(
            Permission.objects.get(codename='view_payment'),
        )
        self.year = baker.make(models.Year, active=True)
        session = self.get_session()
        session[f'{self.user.id}'] = self.year.name
        session.save()
        self.set_session_cookies(session)
        self.client.force_login(self.user)

    def test_session_data(self):
        session = self.client.session
        self.assertEqual(session[f'{self.user.id}'], self.year.name)

    def test_status_code_and_template_for_non_htmx(self):
        resp = self.client.get(self.url)
        self.assertEqual(resp.status_code, 200)
        self.assertTemplateUsed(resp, 'full_template.html')
        self.assertTemplateUsed(resp, 'partials/student/students_list.html')
    
    def test_status_code_and_template_for_htmx(self):
        resp = self.client.get(self.url, **htmx_headers)
        self.assertEqual(resp.status_code, 200)
        self.assertTemplateUsed(resp, 'partials/student/students_list.html')
        self.assertTemplateNotUsed(resp, 'full_template.html')

    def test_querysets_returned(self):
        student = baker.make(
            models.Student, birth_day=3, birth_month=10, birth_year=2000)
        level = baker.make(models.GenericLevel)
        enrollments = baker.make(models.Enrollment,
                       student=student, agent=self.user, 
                       school=self.school, year=self.year, 
                       generic_level_fr=level)
        enrollments2 = baker.make(models.Enrollment, 
                       student=baker.make(models.Student,
                                          birth_day=2, birth_year=2000))
        resp = self.client.get(self.url)
        self.assertEqual(resp.context['enrollments'].count(), 1)
        self.assertEqual(resp.context['levels'].count(), 1)


class StudentDetailViewTests(SessionEnabledTestCase):
    def setUp(self):
        self.year = baker.make(models.Year, active=True)
        self.school = baker.make(models.School, name='EcolePro')
        self.user = baker.make(get_user_model(), school=self.school)
        self.student = baker.make(models.Student, 
                       birth_day=1, birth_month=1, birth_year=2000,
                       school=self.school)
        self.enrollment = baker.make(models.Enrollment, year=self.year, 
            school=self.school, student=self.student)

        # Create session obj
        session = self.get_session()
        session[f'{self.user.id}'] = self.year.name
        session.save()
        self.set_session_cookies(session)
        self.url = reverse('school:student_detail', args=[self.enrollment.id])

    def test_unauthorized_users(self):
        self.client.force_login(self.user)
        resp = self.client.get(self.url)
        self.assertEqual(resp.status_code, 403)
    
    def test_status_and_template(self):
        self.user.user_permissions.add(
            Permission.objects.get(codename='view_student')
        )
        self.client.force_login(self.user)
        resp = self.client.get(self.url)
        self.assertEqual(resp.status_code, 200)
        self.assertTemplateUsed(resp, 'partials/student/student_detail.html')
        self.assertIn('enrollment', resp.context)


class StudentStatusChangeViewTests(SessionEnabledTestCase):
    def setUp(self):
        self.year = baker.make(models.Year, active=True)
        self.school = baker.make(models.School, name='EcolePro')
        self.user = baker.make(get_user_model(), school=self.school)
        self.student = baker.make(models.Student, 
                       birth_day=1, birth_month=1, birth_year=2000,
                       school=self.school)
        self.enrollment = baker.make(models.Enrollment, year=self.year, 
            school=self.school, student=self.student)
        self.initial_status = self.enrollment.active

        # Create session obj
        session = self.get_session()
        session[f'{self.user.id}'] = self.year.name
        session.save()
        self.set_session_cookies(session)
        self.url = reverse('school:student_status_change', args=[self.enrollment.id])

    def test_unauthorized_users(self):
        self.client.force_login(self.user)
        resp = self.client.get(self.url)
        self.assertEqual(resp.status_code, 403)
    
    def test_status_and_template(self):
        self.user.user_permissions.add(
            Permission.objects.get(codename='add_payment')
        )
        self.client.force_login(self.user)
        resp = self.client.get(self.url, **htmx_headers)
        self.assertEqual(resp.status_code, 200)
        self.assertTemplateUsed(resp, 'partials/confirm.html')
        self.assertIn('enrollment', resp.context)

    def test_post_request(self):
        self.user.user_permissions.add(
            Permission.objects.get(codename='add_payment')
        )
        self.client.force_login(self.user)
        resp = self.client.post(self.url, **htmx_headers)
        self.assertEqual(resp.status_code, 204)
        self.enrollment = models.Enrollment.objects.get(pk=self.enrollment.id)
        self.assertNotEqual(self.enrollment.active, self.initial_status)