<form class="tile" id="tile" method="get" action="{% url 'school:levels_stats_export' %}">
    {% if user.school.cycle == CYCLE_BOTH %}
    <div class="form-group">
        <label for="cycle_filter">Filtrer par cycle (Primaire/Secondaire)</label>
        <select name="cycle_filter" style="height: 32px;" id="cycle_filter" class="form-control col-md-6 col-lg-3" hx-get="{{ request.path }}?education={{ request.GET.education }}" hx-target="#app-content">
            <option value="">-------------------</option>
            <option value="{{ CYCLE_PRIMARY }}" {% if request.GET.cycle_filter == CYCLE_PRIMARY %} selected="selected" {% endif %}>Primaire</option>
            <option value="{{ CYCLE_SECONDARY }}"  {% if request.GET.cycle_filter == CYCLE_SECONDARY %} selected="selected" {% endif %}>Secondaire</option>
        </select>
    </div>
    {% endif %}
    <div class="table-responsive">
        <table class="table table-striped table-sm table-bordered table-hover checkbox-table" id="datatable">
            <thead class="bg-primary text-white">
            <tr>
                <th><input type="checkbox" name="select-all" id="select-all"></th>
                <th>Niveau</th>
                <th>Classe</th>
                <th class="text-center">Garçons</th>
                <th class="text-center">Filles</th>
                <th class="text-center">Total</th>
            </tr>
            </thead>
            <tbody>
            {% for level in levels %}
            <tr>
                <td class="align-middle"><input type="checkbox" name="check-{{ level.id }}" id="check-{{ level.id }}"></td>
                <td class="align-middle">{{ level.generic_level }}</td>
                <td class="align-middle">{{ level }}</td>
                <td class="align-middle text-center">{{ level.boys }}</td>
                <td class="align-middle text-center">{{ level.girls }}</td>
                <td class="align-middle text-center">{{ level.students }}</td>
            </tr>
            {% endfor %}
            </tbody>
            <tfoot class="bg-success text-white font-weight-bold">
                <tr>
                    <td class="align-middle" colspan="3">EFFECTIF TOTAL</td>
                    <td class="align-middle text-center">{{ aggregate_result.total_boys }}</td>
                    <td class="align-middle text-center">{{ aggregate_result.total_girls }}</td>
                    <td class="align-middle text-center">{{ aggregate_result.total_students }}</td>
                </tr>
            </tfoot>
        </table>
    </div>

    {% include 'partials/level/stats_actions.html' %}
</form>

<script>
$(document).ready(function(){
  $(".checkbox-table").simpleCheckboxTable();
});
</script>