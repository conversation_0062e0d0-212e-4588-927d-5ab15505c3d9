<div class="tile" id="tile" hx-get="{% url 'school:payment_options' %}" hx-trigger="saved from:body" hx-target="#app-content">
    <div class="tile-title-w-btn">
        <div class="btn-group">
            <a class="btn btn-success" href="" 
               hx-get="{% url 'school:payment_option_add' %}" 
               hx-target="#dialog">+ Ajouter une rubrique</a>
        </div>
    </div>
    <div class="table-responsive">
        <table class="table table-striped table-sm" id="datatable">
            <thead class="bg-primary text-white">
            <tr>
                <th>Rubrique</th>
                <th>Taux</th>
                <th>Montant par défaut</th>
                <th>Opération</th>
                <th>Applicable à</th>
                <th>Actif?</th>
                <th>Actions</th>
            </tr>
            </thead>
            <tbody>
                {% for payment_option in payment_options %}
                <tr>
                    <td class="align-middle">{{ payment_option.name }}</td>
                    <td class="align-middle">{{ payment_option.rate|default:'N/A' }}</td>
                    <td class="align-middle">{{ payment_option.amount|default:'N/A' }}</td>
                    <td class="align-middle">{{ payment_option.get_operation_display }}</td>
                    <td class="align-middle">{{ payment_option.get_option_display }}</td>
                    {% if payment_option.active %}
                    <td class="align-middle text-success">
                        <a href="" title="Cliquer pour changer" class="text-success">
                            <span data-feather="check" class="align-middle"></span>
                            OUI
                        </a>
                    </td>
                    {% else %}
                    <td class="align-middle text-danger">
                        <a href="" title="Cliquer pour changer" class="text-danger">
                        <span data-feather="x" class="align-middle"></span>
                        NON
                        </a>
                    </td>
                    {% endif %}
                    <td class="align-middle">
                        <a href="" hx-get="{% url 'school:payment_option_edit' payment_option.pk %}" 
                            hx-target="#dialog"
                            class="btn btn-sm btn-primary">
                            <span data-feather="edit" class="feather-16"></span>
                        </a>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>