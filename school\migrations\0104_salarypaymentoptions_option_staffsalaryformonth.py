# Generated by Django 5.0.8 on 2024-10-26 01:09

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('school', '0103_salarypaymentoptions_operation'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='salarypaymentoptions',
            name='option',
            field=models.CharField(choices=[('E', 'Enseignants uniquement'), ('O', 'Personnel Non-enseignant'), ('A', 'Tout le Personnel')], default='A', max_length=1, verbose_name='option pour'),
        ),
        migrations.CreateModel(
            name='StaffSalaryForMonth',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('salary', models.PositiveIntegerField(verbose_name='salaire')),
                ('month', models.PositiveSmallIntegerField(choices=[(1, '1- Janvier'), (2, '2- Février'), (3, '3- <PERSON>'), (4, '4- Avril'), (5, '5- Mai'), (6, '6- Juin'), (7, '7- Juillet'), (8, '8- Août'), (9, '9- Septembre'), (10, '10- Octobre'), (11, '11- Novembre'), (12, '12- Décembre')], verbose_name='mois')),
                ('payment_date', models.DateField(verbose_name='date de paiement')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='date de création')),
                ('agent', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('staff', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='school.staff')),
                ('year', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='school.year')),
            ],
            options={
                'verbose_name': 'Paiement de salaire',
                'verbose_name_plural': 'Paiements de salaire',
            },
        ),
    ]
