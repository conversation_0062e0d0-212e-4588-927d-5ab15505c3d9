# Generated by Django 5.0.2 on 2024-06-25 08:55

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('school', '0074_levelpricing_annexe_alter_student_birth_month_and_more'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='student',
            name='identifier',
            field=models.Char<PERSON>ield(db_index=True, max_length=10, null=True, unique=True, verbose_name='identifiant'),
        ),
        migrations.AlterField(
            model_name='student',
            name='student_id',
            field=models.Char<PERSON>ield(blank=True, db_index=True, max_length=9, null=True, verbose_name='matricule'),
        ),
    ]
