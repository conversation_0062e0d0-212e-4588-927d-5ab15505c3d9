{% include "partials/breadcrumb.html" with title="Versements" icon="dollar-sign" subtitle=subtitle|default:'Liste des versements' %}
{% load humanize %}

{% include 'partials/tabs.html' with nav_items=nav_items active_nav=active_nav %}
<div class="row" hx-get="{% url 'school:payments' %}" hx-trigger="saved from:body" hx-target="#app-content">
    <div class="col">
        <div class="tile">
            <div class="tile-title-w-btn">
                {% if active_nav == 'day_payments' %}
                <div class="dropdown">
                    <button class="btn btn-sm btn-warning dropdown-toggle" type="button" data-toggle="dropdown" aria-expanded="false">
                      Imprimer la liste
                    </button>
                    <div class="dropdown-menu">
                      <a class="dropdown-item" href="{% url 'school:payments_list_pdf' %}?report_type=I">Inscription</a>
                      <a class="dropdown-item" href="{% url 'school:payments_list_pdf' %}?report_type=S">Scolarité</a>
                      <a class="dropdown-item" href="{% url 'school:payments_list_pdf' %}?report_type=A">Annexe</a>
                    </div>
                  </div>
                {% endif %}
            </div>
            <div class="table-responsive">
                <table class="table table-striped table-sm" id="datatable">
                    <thead class="bg-primary text-white">
                    <tr>
                        <th>Matricule</th>
                        <th style="min-width: 120px;">Nom et Prénoms</th>
                        <th>Classe</th>
                        <th>Montant versé</th>
                        <th>Date</th>
                        <th>Actions</th>
                    </tr>
                    </thead>
                    <tbody>
                    {% for payment in payments %}
                    <tr>
                        <td class="align-middle">{{ payment.enrollment.student.student_id|default_if_none:'' }}</td>
                        <td class="align-middle">{{ payment.enrollment }}</td>
                        <td class="align-middle">{{ payment.enrollment.level_fr }}</td>
                        <td class="align-middle">{{ payment.get_total|intcomma }}</td>
                        <td class="align-middle">{{ payment.created_at|date:'d/m/Y H:i' }}</td>
                        <td class="align-middle d-flex w-auto" hx-target="#dialog">
                              <a href="" hx-get="{% url 'school:payment_edit' payment.id %}" class="btn btn-sm btn-warning" title="Modifier">
                                <span data-feather="edit" class="feather-16"></span>
                              </a>
                              <a href="{% url 'school:student_payments_pdf' payment.enrollment.id %}?template=2" target="_blank"
                                  class="btn btn-sm btn-secondary ml-1" title="Imprimé relevé de paiement">
                                <span data-feather="file-text" class="feather-16"></span>
                              </a>
                              <a href="" hx-get="{% url 'school:payment_delete' payment.id %}" class="btn btn-sm btn-danger ml-2" title="Supprimer">
                                <span data-feather="trash" class="feather-16"></span>
                              </a>
                        </td>
                    </tr>
                    {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>