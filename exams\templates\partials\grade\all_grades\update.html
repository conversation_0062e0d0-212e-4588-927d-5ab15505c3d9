{% load static %}
{% load widget_tweaks %}
<div class="tile">
    <form action="" method="post" hx-post="{{ request.path }}?lang={{ lang }}" hx-target="#progress_container"
    hx-on="htmx:beforeRequest: if(event.detail.target.id == 'progress_container') $('#submit-btn').prop('disabled', true);" hx-include="[name=term]">
        {% csrf_token %}
        <div class="form-row">
            <div class="col-sm-6 col-md-6 form-group">
                <label for="">Cycle</label>
                {% if lang == 'F' %}
                    {% render_field form.cycle class='form-control' hx-get='/examens/cycles/periodes/?lang=F' hx-target='#term_container' %}
                {% else %}
                    {% render_field form.cycle class='form-control' hx-get='/examens/cycles/periodes/?lang=A' hx-target='#term_container' %}
                {% endif %}
            </div>
            <div class="col-sm-6 col-md-6 form-group" id="term_container" hx-swap='outerHTML'>
                <label for="">Période</label>
                {% render_field form.term class='form-control' %}
            </div>
        </div>

        <div id="progress_container">

        </div>
        
        <input type="submit" value="Actualiser les Moyennes" class="btn btn-success" id="submit-btn">
    </form>
</div>