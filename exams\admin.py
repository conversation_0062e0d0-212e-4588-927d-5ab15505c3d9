from typing import Any
from django.contrib import admin, messages
from django.db.models.aggregates import Count
from django.db.models import Q, F
from import_export import resources
from import_export.admin import ImportExportModelAdmin, ExportActionModelAdmin
from django.contrib.admin import ModelAdmin
from main import utils
from . import models
from exams.grades_utils import update_level_term_stats

admin.site.site_title = 'ECOLE PRO'
admin.site.site_header = 'ECOLE PRO'
admin.site.index_title = "EcolePro >> Espace d'Administration"

ModelAdmin.list_per_page = 25

@admin.register(models.Term)
class TermAdmin(ModelAdmin):
    list_display = ['name', 'cycle', 'education', 'code', 'order']
    list_filter = ['cycle', 'education']


@admin.register(models.SchoolTerm)
class SchoolTermAdmin(ModelAdmin):
    list_display = [
        'year', 'cycle', 'school', 
        'term_education', 'term_name', 
        'level', 'max', 'coefficient', 'active'
    ]
    search_fields = ['level__name', 'level__short_name']
    list_select_related = ['school', 'term', 'level', 'year', 'school']
    list_filter = ['school', 'year', 'level', 'term']
    actions = ['enable_marking_for_all']
    
    def term_education(self, schoolterm):
        return schoolterm.term.get_education_display()
    
    def term_name(self, schoolterm):
        return str(schoolterm.term)

    @admin.action(description='Autoriser la notation')
    def enable_marking_for_all(self, request, queryset):
        queryset.update(allow_marking=True)



class SubjectRessource(resources.ModelResource):
    class Meta:
        model = models.Subject


@admin.register(models.Subject)
class SubjectAdmin(ImportExportModelAdmin, ExportActionModelAdmin):
    list_display = [
        'school', 'name', 'translation', 
        'abbreviation', 'education', 
        'category', 'code', 'level_subjects'
    ] 
    list_select_related = ['school']
    list_editable = ['category']
    list_filter = ['education', 'category', 'school']
    resource_classes = [SubjectRessource]
    actions = ['set_code']

    @admin.action(description='Set code field')
    def set_code(self, request, queryset):
        qs = queryset.filter(code__isnull=True)
        for subject in qs:
            subject_name = (subject.name if subject.education == utils.EDUCATION_FRENCH else subject.translation)
            subject_code = subject_name
            number = 1
            while models.Subject.objects.filter(code=subject_code).exists():
                number += 1
                subject_code = f'{subject_name}{number}'
            subject.code = subject_code
            subject.save(update_fields=['code'])
        messages.success(request, 'Code field has been set for subjects without code')

    def level_subjects(self, subject):
        return subject.level_subjects_count
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('school').annotate(
            level_subjects_count=Count('levelsubject', distinct=True)
        )


class LevelSubjectResource(resources.ModelResource):
    class Meta:
        model = models.LevelSubject


@admin.register(models.LevelSubject)
class LevelSubjectAdmin(ImportExportModelAdmin, ExportActionModelAdmin):
    list_display = [
        'year', 'school', 'level', 'order', 'name', 'translation', 
        'abbreviation', 'education', 'max', 'coefficient'
    ]
    list_filter = ['year', 'school', 'subject__education', 'subject', 'level__cycle']
    list_select_related = ['subject', 'level', 'year', 'school']
    search_fields = ['name', 'abbreviation']
    resource_classes = [LevelSubjectResource]    
    actions = ['set_max_to_10', 'set_max_to_20', 'set_max_to_30', 'set_max_to_40', 'set_max_to_50', 'set_max_to_60', 'reset_arabic_coefs']

    def get_queryset(self, request):
        return super().get_queryset(request) \
            .order_by('year', 'level', 'order') \
            .only('year__full_name', 'year__name', 'school__name',
                  'level__short_name', 'subject__name',
                  'subject__translation', 'subject__abbreviation', 
                  'subject__education', 'max', 'coefficient', 'order'
                )

    def name(self, level_subject):
        return level_subject.subject.name
    
    def translation(self, level_subject):
        return level_subject.subject.translation
    
    def abbreviation(self, level_subject):
        return level_subject.subject.abbreviation
    
    def education(self, level_subject):
        return level_subject.subject.education
    
    @admin.action(description='Définir sur 10')
    def set_max_to_10(self, request, queryset):
        queryset.update(max=10)
    
    @admin.action(description='Reinit. coefficients arabes')
    def reset_arabic_coefs(self, request, queryset):
        queryset.filter(subject__education=utils.EDUCATION_ARABIC).update(coefficient=1)
    
    @admin.action(description='Définir sur 20')
    def set_max_to_20(self, request, queryset):
        queryset.update(max=20)

    @admin.action(description='Définir sur 30')
    def set_max_to_30(self, request, queryset):
        queryset.update(max=30)
    
    @admin.action(description='Définir sur 40')
    def set_max_to_40(self, request, queryset):
        queryset.update(max=40)

    @admin.action(description='Définir sur 50')
    def set_max_to_50(self, request, queryset):
        queryset.update(max=50)
        queryset.update(max=40)

    @admin.action(description='Définir sur 60')
    def set_max_to_60(self, request, queryset):
        queryset.update(max=60)
    

@admin.register(models.Grade)
class GradeAdmin(ModelAdmin):
    list_display = [
        'school_term', 'subject', 'student_id', 
        'get_student', 'grade', 'updated_by'
    ]

    list_select_related = [
        'enrollment__student', 'subject', 
        'school_term__term', 'updated_by',
        'enrollment__school', 'subject__subject'
    ]
    autocomplete_fields = ['school_term', 'subject', 'enrollment']
    list_filter = ['school_term__term', 'enrollment__school']
    actions = ['delete_wrong_grades', 'mark_none_as_zeros']
    
    def student_id(self, grade):
        return grade.enrollment.student.student_id

    @admin.display(ordering='enrollment__student', description='élève')
    def get_student(self, grade):
        return grade.enrollment
    
    def get_queryset(self, request):
        return super().get_queryset(request) \
            .only(
                'enrollment__school__name',
                'school_term__term__name', 
                'school_term__term__order', 
                'school_term__term__education', 
                'subject__subject__code',
                'subject__subject__abbreviation',
                'enrollment__student__student_id',
                'enrollment__student__last_name',
                'enrollment__student__first_name',
                'grade',
                'updated_by__last_name',
                'updated_by__first_name',
            )

    # Create an admin to delete grades that have different scubject education and school term education
    @admin.action(description='Supprimer les notes érronées')
    def delete_wrong_grades(self, request, queryset):
        queryset = queryset.annotate(
            is_clean=Q(subject__subject__education=F('school_term__term__education'))
        )
        queryset = queryset.filter(is_clean=False)
        queryset.delete()

    @admin.action(description='Rendre les notes nulls en sur 0')
    def mark_none_as_zeros(self, request, queryset):
        queryset = queryset.filter(grade=None)
        queryset.update(grade=0)
        messages.success(request, 'Les notes vides ont bien été défini sur 0')

@admin.register(models.StudentSubjectGroupAverage)
class StudentSubjectGroupSummaryAdmin(ModelAdmin):
    list_display = [
        'year', 'term', 'level', 'enrollment', 
        'group', 'average', 'rank'
    ]
    list_select_related = [
        'enrollment__year', 'enrollment__school', 
        'enrollment__student', 'term',
        'enrollment__level_ar',
        'enrollment__level_fr'
    ]
    list_filter = [
        'group', 'enrollment__year', 'term__term',
        'enrollment__school', 'enrollment__level_ar__generic_level',
        'enrollment__level_fr__generic_level'
    ]
    autocomplete_fields = ['term', 'enrollment']

    def year(self, obj):
        return obj.enrollment.year
    
    def level(self, obj):
        if obj.term.education == utils.EDUCATION_FRENCH:
            return obj.enrollment.level_fr
        return obj.enrollment.level_ar


@admin.register(models.TermResult)
class TermResultAdmin(ModelAdmin):
    list_display = [
        'year', 'school', 
        'enrollment', 'level_fr', 'level_ar',
        'total', 'school_term', 'education',
        'average', 'rank', 'is_ex'
    ]
    list_select_related = [
        'enrollment__student', 'school_term__term', 
        'enrollment__school', 'enrollment__year',
        'enrollment__generic_level_fr',
        'enrollment__generic_level_ar'
    ]
    list_filter = [
        'enrollment__year', 'enrollment__school', 
        'school_term__term', 'enrollment__active',
        'enrollment__generic_level_fr',
        'enrollment__generic_level_ar',
        'enrollment__student__gender',
        'enrollment__generic_level_fr__cycle',
        'school_term__term__education'
    ]
    autocomplete_fields = ['school_term', 'enrollment']
    actions = ['remove_other_term_results', 'mark_zeros_as_none']

    def year(self, termresult):
        return termresult.enrollment.year
    
    @admin.display(description='Classe française')
    def level_fr(self, termresult):
        return termresult.enrollment.generic_level_fr
    
    @admin.display(description='Classe arabe')
    def level_ar(self, termresult):
        return termresult.enrollment.generic_level_ar
    
    def school(self, termresult):
        return termresult.enrollment.school
    
    def education(self, termresult):
        return termresult.school_term.term.education
    
    @admin.action(description='Rendre nul les résultats avec average=0')
    def mark_zeros_as_none(self, request, queryset):
        queryset = queryset.filter(average=0)
        queryset.update(average=None)
        messages.success(request, 'Les 0 ont bien été défini None')

    @admin.action(description='Annuler les résultats périodiques érronnées')
    def remove_other_term_results(self, request, queryset):
        education = queryset.first().school_term.education
        field_name = 'enrollment__generic_level_fr'
        if education == utils.EDUCATION_ARABIC:
            field_name = 'enrollment__generic_level_ar'
        queryset = queryset.annotate(
            is_clean=Q(school_term__school=F('enrollment__school')) & \
                     Q(school_term__level=F(field_name))
        )
        queryset = queryset.filter(is_clean=False)
        queryset.delete()

    def get_queryset(self, request):
        return super().get_queryset(request).only(
            'enrollment__year__name',
            'enrollment__year__full_name',
            'enrollment__student__last_name',
            'enrollment__student__first_name',
            'enrollment__school__name',
            'enrollment__generic_level_fr__short_name',
            'enrollment__generic_level_ar__short_name',
            'school_term__term__name',
            'school_term__term__education',
            'school_term__term__order',
            'school_term__term__code',
            'average', 'rank', 'is_ex', 'total'
        )


@admin.register(models.LevelStatistics)
class LevelResultsAdmin(ModelAdmin):
    list_display = [
        'year', 'school', 'level', 'term', 'boys_present', 
        'boys_admitted', 'boys_perc', 'girls_present',
        'girls_admitted', 'girls_perc', 'level_perc',
        'min_average', 'max_average', 'level_average'
    ]
    list_select_related = [
        'term', 'level', 'level__year', 'level__school',
        'term__term'
    ]

    list_filter = [
        'level__year', 'level__school', 
    ]
 
    actions = ['update_statistics', 'remove_other_statistics']
    paginate_by = 50

    def school(self, obj):
        return obj.level.school
    
    def year(self, obj):
        return obj.level.year

    def get_queryset(self, request):
        return super().get_queryset(request).annotate(
            boys=Count('level__enrollment', 
                filter=Q(level__enrollment__student__gender=utils.GENDER_MALE),
                distinct=True),
            girls=Count('level__enrollment', 
                filter=Q(level__enrollment__student__gender=utils.GENDER_FEMALE), 
                distinct=True)
        )

    @admin.action(description='Actualiser les statistiques')
    def update_statistics(self, request, queryset):
        for obj in queryset:
            update_level_term_stats(level=obj.level, term=obj.term)

    @admin.action(description='Annuler les statistiques érronnées')
    def remove_other_statistics(self, request, queryset):
        queryset = queryset.annotate(
            is_clean=Q(term__school=F('level__school')) & \
                     Q(term__year=F('level__year')) &
                     Q(term__education=F('level__education')) &
                     Q(term__level=F('level__generic_level'))
        )
        queryset = queryset.filter(is_clean=False)
        queryset.delete()
    
    def level_perc(self, obj):
        return obj.get_level_perc()


@admin.register(models.EducationYearResult)
class EducationYearResultAdmin(ModelAdmin):
    list_display = [
        'get_school',
        'education', 'enrollment', 'classe', 'total', 
        'class_average', 'admission_average',
        'average', 'rank', 'decision'
    ]
    list_filter = [
        'enrollment__school', 'education', 'enrollment__school', 
        'enrollment__year', 'enrollment__level_fr'
    ]
    autocomplete_fields = ['enrollment']

    @admin.display(description='école')
    def get_school(self, result):
        return result.enrollment.school
    
    def classe(self, result):
        return result.enrollment.level_fr
    
    def get_queryset(self, request):
        return super().get_queryset(request) \
            .select_related(
                'enrollment__school', 
                'enrollment__student', 
                'enrollment__level_fr__generic_level') \
            .only(
                'education', 'total', 'class_average', 'admission_average', 
                'average', 'rank', 'decision',
                'enrollment__student__last_name', 'enrollment__student__first_name',
                'enrollment__level_fr__generic_level__short_name',
                'enrollment__level_fr__generic_level__order',
                'enrollment__level_fr__number',
                'enrollment__school__name',
            )
    

@admin.register(models.CombinedYearResult)
class CombinedYearResultAdmin(ModelAdmin):
    list_display = ['enrollment', 'total', 'average', 'rank']
    list_filter = ['enrollment__school', 'enrollment__year']


@admin.register(models.SubjectKit)
class SubjectKitAdmin(ModelAdmin):
    list_display = ['name', 'levels_list', 'subjects_list', 'education']
    list_filter = ['education']
    list_prefetch_related = ['subjects', 'levels']
    def get_form(self, request: Any, obj: Any | None = ..., change: bool = ..., **kwargs):
        form = super().get_form(request, obj, change, **kwargs)
        print(form.base_fields)
        form.base_fields['subjects'].queryset = \
            models.LevelSubject.objects.filter(school__isnull=True)
        return form

    def levels_list(self, subject_kit):
        return ', '.join(level.short_name for level in subject_kit.levels.all())
     
    def subjects_list(self, subject_kit):
        return ', '.join(subject.subject.name for subject in subject_kit.subjects.distinct())


@admin.register(models.CompleteCycleSubjectKit)
class CompleteSubjectKitAdmin(ModelAdmin):
    list_display = ['name', 'cycle', 'get_subject_kits', 'education']
    list_filter = ['education', 'cycle']

    def get_subject_kits(self, completekit):
        result = ''
        for kit in completekit.subject_kits.all():
            result += ', '.join(subject.subject.name for subject in kit.subjects.all())
        return result


@admin.register(models.TermKit)
class TermKitAdmin(ModelAdmin):
    list_display = ['name', 'terms_list', 'levels_list', 'education']
    list_filter = ['education']

    def levels_list(self, kit):
        return ', '.join(level.short_name for level in kit.levels.all())
    
    def terms_list(self, kit):
        return ', '.join(f'{term.term.abbreviation} ({term.max} x {term.coefficient})' for term in kit.terms.all())


@admin.register(models.CompleteTermKit)
class CompleteTermKitAdmin(ModelAdmin):
    list_display = ['name', 'cycle', 'get_term_kits', 'education']
    list_filter = ['education', 'cycle']

    def get_term_kits(self, kit):
        return ', '.join(str(term_kit) for term_kit in kit.term_kits.all())
    

# Import export
