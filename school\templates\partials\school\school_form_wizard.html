{% extends 'partials/modal.html' %}
{% load i18n %}
{% load widget_tweaks %}

{% block modal_title %}
<div class="d-flex align-items-center">
  <i data-feather="layers" class="mr-2"></i>
  <span>Assistant de création d'une école dans EcolePro: Étape {{ wizard.steps.step1 }} / {{ wizard.steps.count }}</span>
</div>
{% endblock %}

{% block modal_body %}
  {{ wizard.management_form }}
  {% if wizard.form.forms %}
    {{ wizard.form.management_form }}
    {% for form in wizard.form.forms %}
      {{ form }}
    {% endfor %}
  {% else %}
  <div class="stepwizard mb-2">
    <div class="stepwizard-row setup-panel">
        <div class="stepwizard-step col-xs-2 text-center"> 
            <button name="wizard_goto_step" type="submit" value="0" 
              class="btn {% if wizard.steps.step1 == 1 %} btn-success {% else %} btn-outline-secondary {% if wizard.steps.step1 > 1 %}bg-light text-success border-success{% endif %} {% endif %} btn-circle wizard-step-icon"
              formnovalidate="formnovalidate">
                <i data-feather="map-pin" class="{% if wizard.steps.step1 == 1 %}text-white{% endif %}"></i>
            </button>
            <p class="mt-2"><small>Localisation</small></p>
        </div>
        <div class="stepwizard-step col-xs-2 text-center"> 
            <button name="wizard_goto_step" type="submit" value="1" class="btn {% if wizard.steps.step1 == 2 %} btn-success {% else %} btn-outline-secondary {% if wizard.steps.step1 > 2 %}bg-light text-success border-success{% endif %} {% endif %} btn-circle wizard-step-icon">
                <i data-feather="home" class="{% if wizard.steps.step1 == 2 %}text-white{% endif %}"></i>
            </button>
            <p class="mt-2"><small>Dénomination</small></p>
        </div>
        <div class="stepwizard-step col-xs-2 text-center"> 
            <button name="wizard_goto_step" type="submit" value="2" class="btn {% if wizard.steps.step1 == 3 %} btn-success {% else %} btn-outline-secondary {% if wizard.steps.step1 > 3 %}bg-light text-success border-success{% endif %} {% endif %} btn-circle wizard-step-icon">
                <i data-feather="award" class="{% if wizard.steps.step1 == 3 %}text-white{% endif %}"></i>
            </button>
            <p class="mt-2"><small>Abonnement</small></p>
        </div>
        <div class="stepwizard-step col-xs-2 text-center"> 
            <button name="wizard_goto_step" type="submit" value="3" class="btn {% if wizard.steps.step1 == 4 %} btn-success {% else %} btn-outline-secondary {% if wizard.steps.step1 > 4 %}bg-light text-success border-success{% endif %} {% endif %} btn-circle wizard-step-icon">
                <i data-feather="users" class="{% if wizard.steps.step1 == 4 %}text-white{% endif %}"></i>
            </button>
            <p class="mt-2"><small>Utilisateurs</small></p>
        </div>
        <div class="stepwizard-step col-xs-2 text-center"> 
            <button name="wizard_goto_step" type="submit" value="4" class="btn {% if wizard.steps.step1 == 5 %} btn-success {% else %} btn-outline-secondary {% endif %} btn-circle wizard-step-icon">
                <i data-feather="check-square" class="{% if wizard.steps.step1 == 5 %}text-white{% endif %}"></i>
            </button>
            <p class="mt-2"><small>Récapitulatif</small></p>
        </div>
    </div>
  </div>

  <div id="wizard-content" class="fade-in">
    {% if wizard.steps.step1 == 1 %}
    <div class="alert alert-info slide-in">
      <div class="d-flex">
        <i data-feather="info" class="mr-2 align-middle"></i>
        <span>
          Informations générales sur votre établissement
        </span>
      </div>
    </div>
    {% elif wizard.steps.step1 == 2 %}
    <div class="alert alert-info slide-in">
      <div class="d-flex">
        <i data-feather="info" class="mr-2 align-middle"></i>
        <span>Nom et contacts de votre établissement</span>
      </div>
    </div>
    {% elif wizard.steps.step1 == 3 %}
    <div class="alert alert-info slide-in">
      <div class="d-flex">
        <i data-feather="info" class="mr-2 align-middle"></i>
        <span>Choisissez votre type d'abonnement</span>
      </div>
    </div>
    {% elif wizard.steps.step1 == 4 %}
    <div class="alert alert-info slide-in">
      <div class="d-flex">
        <i data-feather="info" class="mr-2 align-middle"></i>
        <span>Informations pour la création des comptes utilisateurs</span>
      </div>
    </div>
    {% elif wizard.steps.step1 == 5 %}
    <div class="alert alert-success slide-in">
      <div class="d-flex align-items-center">
        <i data-feather="check-circle" class="mr-2 align-middle"></i>
        <div>
          <h5 class="mb-0">Récapitulatif de la création</h5>
          <small>Vérifiez les informations avant de finaliser</small>
        </div>
      </div>
    </div>
    {% endif %}
      
    <div class="form-row wizard-form-content">
      {% if is_summary_step %}
        <!-- Enhanced Summary Step Template -->
        <div class="col-12 fade-in-up">
          <div class="card mb-4 shadow-sm">
            <div class="card-header bg-light text-white py-2 text-dark">
              <div class="d-flex align-items-center">
                <i data-feather="map-pin" class="feather-20 mr-2"></i>
                <p class="mb-0 font-weight-bold">Localisation & Informations générales</p>
              </div>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-6">
                  <div class="d-flex mb-3">
                    <div class="text-muted" style="width: 140px;">Localisation:</div>
                    <div class="font-weight-bold">{{ summary_data.school_data.location.name }}</div>
                  </div>
                  <div class="d-flex mb-3">
                    <div class="text-muted" style="width: 140px;">Statut:</div>
                    <div class="font-weight-bold">
                      {% if summary_data.school_data.status == 'PR' %}
                        <span class="badge badge-pill badge-info">Privé</span>
                      {% elif summary_data.school_data.status == 'PB' %}
                        <span class="badge badge-pill badge-secondary">Public</span>
                      {% endif %}
                    </div>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="d-flex mb-3">
                    <div class="text-muted" style="width: 140px;">Cycle:</div>
                    <div class="font-weight-bold">
                      {% if summary_data.school_data.cycle == 'P' %}
                        <span class="badge badge-pill badge-primary">Primaire</span>
                      {% elif summary_data.school_data.cycle == 'S' %}
                        <span class="badge badge-pill badge-success">Secondaire</span>
                      {% elif summary_data.school_data.cycle == 'B' %}
                        <span class="badge badge-pill badge-warning">Primaire et Secondaire</span>
                      {% endif %}
                    </div>
                  </div>
                  <div class="d-flex mb-3">
                    <div class="text-muted" style="width: 140px;">Éducation:</div>
                    <div class="font-weight-bold">
                      {% if summary_data.school_data.education == 'F' %}
                        <span class="badge badge-pill badge-primary">Français</span>
                      {% elif summary_data.school_data.education == 'A' %}
                        <span class="badge badge-pill badge-success">Arabe</span>
                      {% endif %}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="card mb-4 shadow-sm">
            <div class="card-header bg-light text-white py-2 text-dark">
              <div class="d-flex align-items-center">
                <i data-feather="home" class="feather-20 mr-2"></i>
                <p class="mb-0 font-weight-bold">Coordonnées de l'établissement</p>
              </div>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-6">
                  <div class="d-flex mb-3">
                    <div class="text-muted" style="width: 140px;">Nom:</div>
                    <div class="font-weight-bold">{{ summary_data.contact_data.name }}</div>
                  </div>
                  <div class="d-flex mb-3">
                    <div class="text-muted" style="width: 140px;">Téléphone 1:</div>
                    <div class="font-weight-bold">
                      <i data-feather="phone" class="feather-16 mr-1 text-success"></i>
                      {{ summary_data.contact_data.phone1 }}
                    </div>
                  </div>
                  {% if summary_data.contact_data.phone2 %}
                  <div class="d-flex mb-3">
                    <div class="text-muted" style="width: 140px;">Téléphone 2:</div>
                    <div class="font-weight-bold">
                      <i data-feather="phone" class="feather-16 mr-1 text-success"></i>
                      {{ summary_data.contact_data.phone2 }}
                    </div>
                  </div>
                  {% endif %}
                </div>
                <div class="col-md-6">
                  <div class="d-flex mb-3">
                    <div class="text-muted" style="width: 140px;">Adresse:</div>
                    <div class="font-weight-bold">
                      <i data-feather="map-pin" class="feather-16 mr-1 text-danger"></i>
                      {{ summary_data.contact_data.address }}
                    </div>
                  </div>
                  {% if summary_data.contact_data.email %}
                  <div class="d-flex mb-3">
                    <div class="text-muted" style="width: 140px;">Email:</div>
                    <div class="font-weight-bold">
                      <i data-feather="mail" class="feather-16 mr-1 text-primary"></i>
                      {{ summary_data.contact_data.email }}
                    </div>
                  </div>
                  {% endif %}
                </div>
              </div>
            </div>
          </div>

          <div class="card mb-4 shadow-sm">
            <div class="card-header bg-light text-white py-2 text-dark">
              <div class="d-flex align-items-center">
                <i data-feather="award" class="feather-20 mr-2"></i>
                <p class="mb-0 font-weight-bold">Abonnement EcolePro</p>
              </div>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-6">
                  <div class="d-flex mb-3">
                    <div class="text-muted" style="width: 140px;">Formule:</div>
                    <div class="font-weight-bold">
                      {% if summary_data.plan_data.plan == 'S' %}
                        <span class="badge badge-pill badge-success">Standard</span>
                        <small class="text-success ml-2">Accès à toutes les fonctionnalités</small>
                      {% elif summary_data.plan_data.plan == 'L' %}
                        <span class="badge badge-pill badge-info">Niveau unique</span>
                        <small class="text-info ml-2">Accès limité à un seul niveau</small>
                      {% endif %}
                    </div>
                  </div>
                </div>
                <div class="col-md-6">
                  {% if summary_data.plan_data.plan == 'L' and summary_data.plan_data.level %}
                  <div class="d-flex mb-3">
                    <div class="text-muted" style="width: 140px;">Niveau choisi:</div>
                    <div class="font-weight-bold">
                      <span class="badge badge-pill badge-primary">{{ summary_data.plan_data.level.name }}</span>
                    </div>
                  </div>
                  {% endif %}
                </div>
              </div>
            </div>
          </div>

          <div class="card mb-4 shadow-sm">
            <div class="card-header bg-light text-dark py-2 text-dark">
              <div class="d-flex align-items-center">
                <i data-feather="users" class="feather-20 mr-2"></i>
                <p class="mb-0 font-weight-bold">Utilisateurs & Contacts</p>
              </div>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-6">
                  <div class="d-flex mb-3">
                    <div class="text-muted" style="width: 140px;">Administrateur:</div>
                    <div class="font-weight-bold">
                      <i data-feather="user" class="feather-16 mr-1 text-primary"></i>
                      {{ summary_data.users_data.scientist }}
                    </div>
                  </div>
                  <div class="d-flex mb-3">
                    <div class="text-muted" style="width: 140px;">Téléphone admin:</div>
                    <div class="font-weight-bold">
                      <i data-feather="phone" class="feather-16 mr-1 text-success"></i>
                      {{ summary_data.users_data.scientist_phone }}
                    </div>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="d-flex mb-3">
                    <div class="text-muted" style="width: 140px;">Contact SMS:</div>
                    <div class="font-weight-bold">
                      <i data-feather="message-circle" class="feather-16 mr-1 text-info"></i>
                      {{ summary_data.users_data.sms_phone }}
                      <small class="text-muted d-block">
                        Les informations de connexion seront envoyées à ce numéro
                      </small>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Confirmation checkbox field -->
          <div class="form-group form-check mb-3 fade-in-up">
            <div class="card shadow-sm border-success">
              <div class="card-body bg-light">
                <div class="d-flex align-items-start">
                  <div class="mr-3">
                    {% render_field wizard.form.confirmation id="confirmation_checkbox" class="form-check-input" style="transform: scale(1.5);" %}
                  </div>
                  <div>
                    <label class="form-check-label font-weight-bold" for="confirmation_checkbox">
                      {{ wizard.form.confirmation.label }}
                    </label>
                    <div class="text-muted small mt-1">
                      En validant, vous acceptez les conditions générales d'utilisation du service EcolePro.
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      {% else %}
        {% for field in wizard.form %}
          {% if not selected_plan or selected_plan == 'S' or selected_plan == 'L' and field.name|lower == 'scientist' or selected_plan == 'L' and field.name|lower == 'scientist_phone' or selected_plan == 'L' and field.name|lower == 'sms_phone' %}
          <div class="form-group col-md-6 fade-in-up" style="animation-delay: {{ forloop.counter0|floatformat:1 }}s">
              <label for="{{ field.id_for_label }}" class="{% if field.field.required %}font-weight-bold{% endif %}">
                {% if field.field.required %}*{% endif %} 

                {% if selected_plan == 'L' and field.name == 'scientist' %} 
                Nom de l'Enseignant 
                {% elif field.name|lower == 'admin' and selected_status == 'PB' %} 
                Directeur des Etudes 
                {% elif field.name|lower == 'admin_phone' and selected_status == 'PB' %} 
                Contact du Directeur
                {% else %} 
                {{ field.label }} 
                {% endif %}
              </label>
              
              <div class="input-group">
                {% if field.name|lower == 'phone1' or field.name|lower == 'phone2' or field.name|lower == 'scientist_phone' or field.name|lower == 'admin_phone' or field.name|lower == 'sms_phone' %}
                  <div class="input-group-prepend">
                    <span class="input-group-text"><i data-feather="phone"></i></span>
                  </div>
                {% elif field.name|lower == 'email' %}
                  <div class="input-group-prepend">
                    <span class="input-group-text"><i data-feather="mail"></i></span>
                  </div>
                {% elif field.name|lower == 'name' or field.name|lower == 'translation' %}
                  <div class="input-group-prepend">
                    <span class="input-group-text"><i data-feather="type"></i></span>
                  </div>
                {% elif field.name|lower == 'address' %}
                  <div class="input-group-prepend">
                    <span class="input-group-text"><i data-feather="map"></i></span>
                  </div>
                {% elif field.name|lower == 'scientist' or field.name|lower == 'admin' %}
                  <div class="input-group-prepend">
                    <span class="input-group-text"><i data-feather="user"></i></span>
                  </div>
                {% endif %}
                
                {% render_field field class='form-control' %}
              </div>
              
              <span class="invalid-feedback">{{ field.errors|first }}</span>
              {% if field.help_text %}
                <small class="form-text text-muted">
                  <i data-feather="help-circle" style="width: 14px; height: 14px;"></i> 
                  {{ field.help_text }}
                </small>
              {% endif %}
          </div>
          {% else %}
          {% endif %}
        {% endfor %}
      {% endif %}
    </div>
  </div>
  <div class="row">
    <div class="step-progress-container">
      <div class="step-progress-bar"></div>
    </div>
  </div>
  {% endif %}

  <style>
    /* Step indicator animations */
    @keyframes pulse {
      0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.4);
      }
      70% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
      }
      100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
      }
    }

    .wizard-step-icon.btn-success {
      animation: pulse 2s infinite;
    }

    /* Form content animations */
    .fade-in {
      opacity: 0;
      animation: fadeIn 0.5s ease-in-out forwards;
    }

    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }

    .fade-in-up {
      opacity: 0;
      transform: translateY(20px);
      animation: fadeInUp 0.3s ease-out forwards;
    }

    @keyframes fadeInUp {
      from { 
        opacity: 0;
        transform: translateY(20px);
      }
      to { 
        opacity: 1;
        transform: translateY(0);
      }
    }

    .slide-in {
      opacity: 0;
      transform: translateX(-20px);
      animation: slideIn 0.4s ease-out forwards;
    }

    @keyframes slideIn {
      from { 
        opacity: 0;
        transform: translateX(-20px);
      }
      to { 
        opacity: 1;
        transform: translateX(0);
      }
    }

    .wizard-form-content {
      transform-origin: top center;
    }

    /* Transition effects for form steps */
    .wizard-form-exit {
      opacity: 1;
      transform: translateX(0);
      transition: opacity 0.3s ease, transform 0.3s ease;
    }
    
    .wizard-form-exit-active {
      opacity: 0;
      transform: translateX(-30px);
    }
    
    .wizard-form-enter {
      opacity: 0;
      transform: translateX(30px);
      transition: opacity 0.3s ease, transform 0.3s ease;
    }
    
    .wizard-form-enter-active {
      opacity: 1;
      transform: translateX(0);
    }

    /* Progress indicator styling */
    .step-progress-container {
      height: 4px;
      width: 100%;
      background-color: #e9ecef;
      margin-top: 10px;
      margin-bottom: 20px;
      border-radius: 4px;
      overflow: hidden;
    }
    
    .step-progress-bar {
      height: 100%;
      background: linear-gradient(90deg, #28a745, #20c997);
      width: calc({{ wizard.steps.step1 }} * 20%);
      transition: width 0.5s ease-in-out;
    }
    
    /* Additional styles for summary page */
    .feather-20 {
      width: 20px;
      height: 20px;
    }
    
    .card {
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    
    .card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
    }
    
    .badge {
      font-size: 90%;
      padding: 0.4em 0.8em;
    }
    
    /* Improve animation for summary cards */
    .fade-in-up {
      opacity: 0;
      transform: translateY(20px);
      animation: fadeInUp 0.5s ease-out forwards;
    }
    
    .card:nth-child(1) { animation-delay: 0.1s; }
    .card:nth-child(2) { animation-delay: 0.2s; }
    .card:nth-child(3) { animation-delay: 0.3s; }
    .card:nth-child(4) { animation-delay: 0.4s; }
    .card:nth-child(5) { animation-delay: 0.5s; }
  </style>
{% endblock %}

{% block modal_footer %}

<div class="d-flex justify-content-between w-100">
  <div>
    <!-- <button name="wizard_goto_step" type="submit" value="{{ wizard.steps.first }}" 
      class="btn btn-outline-primary" onclick="animateFormExit()"
      formnovalidate="formnovalidate">
      <i data-feather="chevrons-left" class="mr-1 feather-16 align-middle"></i> {% trans "Début" %}
    </button> -->
      <button type="submit" class="btn btn-success" id="submit-btn" onclick="animateFormExit()">
        {% if wizard.steps.step1 == wizard.steps.count %}
        <i data-feather="check-circle" class="mr-1 feather-16 align-middle"></i> {% trans "Finaliser" %}
        {% else %}
        {% trans "Continuer" %} <i data-feather="chevron-right" class="ml-1 feather-16 align-middle"></i>
        {% endif %}
      </button>
  </div>
  
  <div class="d-flex align-items-center">
    <div class="spinner-border text-primary d-none mr-2" role="status" id="spinner">
      <span class="sr-only">Chargement...</span>
    </div>
    {% if wizard.steps.prev %}

    <button name="wizard_goto_step" type="submit" value="{{ wizard.steps.prev }}" 
      class="btn btn-outline-info" onclick="animateFormExit()"
      formnovalidate="formnovalidate">
      <i data-feather="chevron-left" class="mr-1 feather-16 align-middle"></i> {% trans "Précédent" %}
    </button>
    {% endif %}    
  </div>
</div>

<script>
  // When the #close-modal-btn is clicked, ask for confirmation with sweetalert and redirect to login
  function goToLogin() {
    Swal.fire({
      title: 'Êtes-vous sûr?',
      text: "Vous allez annuler la création de l'école!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Oui, annuler!',
      cancelButtonText: 'Non, continuer'
    }).then((result) => {
      if (result.isConfirmed) {
        window.location.href = "{% url 'login' %}";
      }
    })
  }

  document.getElementById('close-modal-btn').addEventListener('click', function() {
    goToLogin()
  });
  
  document.getElementById('close-modal-icon').addEventListener('click', function() {
    goToLogin()
  });

  document.addEventListener('DOMContentLoaded', function() {
    feather.replace();
  })
    // Add staggered animation delay to form fields
    const formFields = document.querySelectorAll('.fade-in-up');
    formFields.forEach((field, index) => {
      field.style.animationDelay = `${0.1 + (index * 0.1)}s`;
    });

    // Track the current step for animation history
    if (!window.currentStep) {
      window.currentStep = {{ wizard.steps.step1 }};
    }
    
    // Highlight active step
    highlightCurrentStep();

  
  function highlightCurrentStep() {
    const currentStep = {{ wizard.steps.step1 }};
    const steps = document.querySelectorAll('.wizard-step-icon');
    
    steps.forEach((step, index) => {
      if (index + 1 < currentStep) {
        step.classList.add('completed');
      }
    });
  }
  
  function animateFormExit() {
    const wizardContent = document.getElementById('wizard-content');
    
    // Add exit animation
    wizardContent.style.opacity = '1';
    wizardContent.style.transition = 'opacity 0.3s ease-out';
    wizardContent.style.opacity = '0';
    
    // Show loader
    setTimeout(() => {
      document.getElementById('spinner').classList.remove('d-none');
    }, 200);
  }
  
</script>
{% endblock %}