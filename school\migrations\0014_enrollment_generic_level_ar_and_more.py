# Generated by Django 4.2.3 on 2023-07-25 14:00

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('school', '0013_define_expense_model'),
    ]

    operations = [
        migrations.AddField(
            model_name='enrollment',
            name='generic_level_ar',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.PROTECT, related_name='+', to='school.genericlevel'),
        ),
        migrations.AddField(
            model_name='enrollment',
            name='generic_level_fr',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.PROTECT, to='school.genericlevel'),
        ),
        migrations.AddField(
            model_name='enrollment',
            name='online',
            field=models.BooleanField(default=False, verbose_name='Inscription en ligne'),
        ),
        migrations.AlterField(
            model_name='enrollment',
            name='level_fr',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='school.level', verbose_name='classe'),
        ),
        migrations.AlterField(
            model_name='expense',
            name='amount',
            field=models.PositiveIntegerField(validators=[django.core.validators.MinValueValidator(100)], verbose_name='montant dépensé'),
        ),
        migrations.AlterField(
            model_name='expense',
            name='commentaire',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='commentaire'),
        ),
        migrations.AlterField(
            model_name='expense',
            name='expense_type',
            field=models.CharField(choices=[('EQU', 'Achat Matériel/Equipement'), ('CON', 'Construction de Bâtiment'), ('REN', 'Renovation/Peinture etc.'), ('SAL', 'Paiement des salaires'), ('AID', 'Aide financière'), ('OTH', 'Autre dépenses')], max_length=3, verbose_name='catégorie'),
        ),
    ]
