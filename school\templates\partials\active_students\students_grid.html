<div class="tile" hx-target="#app-content" hx-get="{{ request.path }}{% if page %}page={{ page }}{% endif %}" hx-trigger="saved from:body">
    {% if perms.school.add_enrollment %}
    <div class="tile-title-w-btn">
        <div class="btn-group show-on-pc">
            <a class="btn btn-success" href="{% url 'school:student_add' %}" 
              hx-get="{% url 'school:student_add' %}" 
              hx-target="#dialog-xl">+ Nouvelle inscription</a>
        </div>
        <div class="btn-group show-on-phone">
            <a class="btn btn-success" href="{% url 'school:student_add_wizard' %}" 
              hx-get="{% url 'school:student_add_wizard' %}" 
              hx-target="#dialog">+ Nouvelle inscription</a>
        </div>
    </div>
  {% endif %}
  <div class="row mb-2">
    <div class="col-8">
      <label for="search" class="pl-2">Rechercher:</label>
      <input type="search" name="search" id="search" 
              class="form-control btn-sm ml-2"
              value="{{ search }}"
              hx-get="{{ request.path }}">
    </div>

    <div class="col-4">
      <label for="search" class="pl-2">Afficher</label>
      <select name="per_page" id="per_page" class="form-control form-control-sm" hx-get="{{ request.path }}?page={{ page }}{% if education %}&education={{ education}}{% endif %}">
        <option value="10" {% if per_page|floatformat:'0' == '10' %} selected="selected" {% endif %}>10 élèves</option>
        <option value="25" {% if per_page|floatformat:'0' == '25' %} selected="selected" {% endif %}>25 élèves</option>
        <option value="50" {% if per_page|floatformat:'0' == '50' %} selected="selected" {% endif %}>50 élèves</option>
      </select> 
    </div>
  </div>
  <div class="row">
    {% for enrollment in enrollments %}
    <div class="col-md-6 col-xl-4">                       
      <div class="custom-card bg-light border my-1 rounded">
        <div class="custom-card-body">
          <div class="media align-items-center">
            
            
          {% if enrollment.student.photo %}
            <img data-original="{{ enrollment.student.photo.url }}" 
                alt="Photo de l'élève" 
                class="lazy avatar avatar-xl mr-3 rounded-circle border">
          {% elif enrollment.is_second_cycle_fr and enrollment.student.student_id %}
            <img data-original="{{ enrollment.student.government_photo }}" 
                alt="Photo de l'élève" 
                class="lazy avatar avatar-xl mr-3 rounded-circle border"
                id="{{ enrollment.id }}"
                onload="if (this.src.endsWith('CC')) {
                  this.src = '{{ enrollment.student.blank_photo }}'
                }">
          {% else %}
          <img data-original="{{ enrollment.student.blank_photo }}" 
                alt="Photo de l'élève" 
                class="lazy avatar avatar-xl mr-3 rounded-circle border">
          {% endif %}
            <!-- <img src="{{ enrollment.student.photo.url }}" alt="" class="avatar avatar-xl mr-3 rounded-circle"> -->
            <div class="media-body overflow-hidden">
              <h5 class="card-text mb-0">{{ enrollment }}</h5>
              <p class="card-text text-uppercase text-muted">{{ enrollment.student.student_id|default:enrollment.student.identifier }}</p>
              <p class="card-text">
                <span class="text-muted">Sexe: </span>{{ enrollment.student.gender }} | <span class="text-muted">Né(e) le: </span>{{ enrollment.student.birth_date_str }} <br>
                <span class="text-muted">Français: </span>{{ enrollment.level_fr }} | <span class="text-muted">Arabe: </span>{{ enrollment.level_ar }}
              </p>
            </div>
            <div class="d-flex flex-column align-items-center">
              <a href="" class="btn btn-sm mb-2" data-toggle="tooltip" title="Détails"><span data-feather="plus" class="feather-16"></span></a>

              <a href="#" hx-get="{% url 'school:student_edit_wizard' enrollment.id %}?page={{ page }}&{% if level_fr %}level_fr={{ level_fr }}{% elif level_ar %}level_ar={{ level_ar}}{% endif %}"
              hx-target="#dialog" class="show-on-phone mb-2" title="Modifier infos" data-toggle="tooltip">
              <span data-feather="edit" class="feather-16"></span>
              </a>

              <a href="#" hx-get="{% url 'school:student_edit' enrollment.id %}?page={{ page }}&{% if level_fr %}level_fr={{ level_fr }}{% elif level_ar %}level_ar={{ level_ar}}{% endif %}"
                  hx-target="#dialog-xl" class="show-on-pc mb-2" title="Modifier infos" data-toggle="tooltip">
                  <span data-feather="edit" class="feather-16"></span>
              </a>

              <a href="" class="btn btn-sm" hx-get="{% url 'school:student_delete' enrollment.pk %}" hx-target="#dialog"><span data-feather="trash" class="feather-16 text-danger"></span></a>
            </div>
          </div>
        </div>
      </div>
    </div>
    {% endfor %}
  </div>
  {% include 'partials/pagination.html' with include_items='[name=search], [name=per_page]' %}
</div>
<script>

  element = document.querySelector('#logout');
  if (typeof(element) !=" undefined") element.style.display = 'block';

  if (typeof(feather) != "undefined") {
    feather.replace();
    $("img.lazy").lazyload();
  }

  document.addEventListener("DOMContentLoaded", function(ev) {
    feather.replace();
    $("img.lazy").lazyload();
  })

  function checkIfImageExists(url, callback) {
    const img = new Image();
    img.src = url;

    if (img.complete) {
      callback(true);
    } else {
      img.onload = () => {
        callback(true);
      };
      
      img.onerror = () => {
        callback(false);
      };
    }
  }
</script>