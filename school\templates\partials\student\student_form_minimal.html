{% extends 'partials/modal.html' %}
{% load i18n %}
{% load widget_tweaks %}

{% block modal_title %}
Inscription
{% endblock %}

{% block modal_body %}
<div class="row">
    <!-- Student Information Column -->
    <div class="col-md-6 border-right">
        <h5>Informations sur l'élève</h5>
        <div class="form-group">
            <label for="{{ student_form.student_id.id_for_label }}">{{ student_form.student_id.label }}</label>
            {% render_field student_form.student_id onkeyup='this.value = this.value.toUpperCase();' class='form-control' %}
        </div>
        <section class="form-row">
            <div class="form-group col-xl-4">
                <label for="{{ student_form.last_name.id_for_label }}" class="font-weight-bold">*{{ student_form.last_name.label }}</label>
                {% render_field student_form.last_name class='form-control' onkeyup='this.value = this.value.toUpperCase();' %}
            </div>
            <div class="form-group col-xl-8">
                <label for="{{ student_form.first_name.id_for_label }}" class="font-weight-bold">* {{ student_form.first_name.label }}</label>
                {% render_field student_form.first_name onkeyup='this.value = this.value.toUpperCase();' class='form-control' %}
            </div>
        </section>
        <section class="form-row">
            <div class="form-group col-4">
                <label for="{{ student_form.birth_day.id_for_label }}" class="font-weight-bold">* {{ student_form.birth_day.label }}</label>
                {% render_field student_form.birth_day class='form-control' %}
            </div>
            <div class="form-group col-4">
                <label for="{{ student_form.birth_month.id_for_label }}" class="font-weight-bold">* {{ student_form.birth_month.label }}</label>
                {% render_field student_form.birth_month class='form-control' %}
            </div>
            <div class="form-group col-4">
                <label for="{{ student_form.birth_year.id_for_label }}" class="font-weight-bold">* {{ student_form.birth_year.label }}</label>
                {% render_field student_form.birth_year class='form-control' %}
            </div>
        </section>
        <section class="form-row">
            <div class="form-group col-4">
                <label for="{{ student_form.birth_place.id_for_label }}" class="font-weight-bold">* {{ student_form.birth_place.label }}</label>
                {% render_field student_form.birth_place onkeyup='this.value = this.value.toUpperCase();' class='form-control' %}
            </div>
            <div class="form-group col-4">
                <label for="{{ student_form.gender.id_for_label }}" class="font-weight-bold">* {{ student_form.gender.label }}</label>
                {% render_field student_form.gender class='form-control' %}
            </div>
            <div class="form-group col-4">
                <label for="{{ student_form.nationality.id_for_label }}">* {{ student_form.nationality.label }}</label>
                {% render_field student_form.nationality class='form-control' %}
            </div>
        </section>
        {% if user.school.education == 'A' %}
        <section class="form-row">
            <div class="form-group col-8">
                <label for="{{ student_form.full_name_ar.id_for_label }}">{{ student_form.full_name_ar.label }}</label>
                {% render_field student_form.full_name_ar class='form-control' %}
            </div>
            <div class="form-group col-4">
                <label for="{{ student_form.birth_place_ar.id_for_label }}">{{ student_form.birth_place_ar.label }}</label>
                {% render_field student_form.birth_place_ar class='form-control' %}
            </div>
        </section>
        {% endif %}
        <p style="font-style: italic;">
            Les champs précédés de (*) sont obligatoires.
        </p>
    </div>

    <!-- Parent Information Column -->
    <div class="col-md-6 border-right border-left">
        <h5>Infos Supplémentaires</h5>
        <div class="form-row">
            <div class="form-group col-6">
                <label for="{{ level_form.status.id_for_label }}">{{ level_form.status.label }}</label>
                {% render_field level_form.status|add_class:"form-control"|attr:"hx-get:/versements/frais_scolarite/"|attr:"hx-target:#id_2-year_fees" hx-swap="outerHTML" %}
            </div>
            <div class="form-group col-6">
                <label for="{{ level_form.qualite.id_for_label }}">{{ level_form.qualite.label }}</label>
                {% render_field level_form.qualite class='form-control' %}
            </div>
        </div>
        
        <div class="form-row">
            <div class="form-group col-6">
                <label for="{{ parents_form.father.id_for_label }}">{{ parents_form.father.label }}</label>
                {% render_field parents_form.father onkeyup='this.value = this.value.toUpperCase();' class='form-control' %}
            </div>
            <div class="form-group col-6">
                <label for="{{ parents_form.mother.id_for_label }}">{{ parents_form.mother.label }}</label>
                {% render_field parents_form.mother onkeyup='this.value = this.value.toUpperCase();' class='form-control' %}
            </div>
        </div>
        <div class="form-row">
            <div class="form-group col-md-6">
                <label for="{{ parents_form.father_phone.id_for_label }}">Contact du parent ou du tuteur</label>
                {% render_field parents_form.father_phone class='form-control' %}
            </div>
            
            <div class="form-group text-center col-md-6">
                {% if enrollment and enrollment.student.photo %}
                    <img alt="Photo de l'élève" height="100px" width="90px" 
                    src="{{ enrollment.student.photo.url }}" id="photo">
                {% else %}
                    <img alt="Photo de l'élève" height="100px" width="90px" 
                        src="{{ enrollment.student.blank_photo }}" id="photo">
                {% endif %}
                <div class="form-group">
                    <input type="file" class="form-control mt-1" name="3-photo" style="padding-top: 2px; padding-bottom: 2px;" 
                    id="{{ files_form.photo.id_for_label }}"
                    accept=".jpg, .png, .jpeg" onchange="
                        const selectedImage = document.getElementById('photo');
                        const reader = new FileReader();
        
                        if (this.files && this.files[0]) {
                            reader.onload = function(e) {
                                selectedImage.src = e.target.result;
                            };
                            reader.readAsDataURL(this.files[0]);
                        } else {
                            selectedImage.src = '/static/img/avatar.jpg';
                        }
                    ">    
                </div>
                <div class="form-group" style="display: none;">
                    <input type="file" class="form-control" name="3-certificate_img" 
                    id="{{ files_form.certificate_img.id_for_label }}"
                    accept=".jpg, .png, .jpeg">    
                </div>
            </div>
        </div>
    </div>
        </div>  
    </div>
{% endblock %}

{% block modal_footer %}
<input type="submit" class="btn btn-success" value="{% trans 'Valider' %}" id="submit-btn" />
{% endblock %}