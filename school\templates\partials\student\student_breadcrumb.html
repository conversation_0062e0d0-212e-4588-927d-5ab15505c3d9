{% load humanize %}
<div class="app-title pt-2 px-0">
    <div class="container-fluid p-0 mt-1">
      {% if moving_message %}
      <div style="z-index: 999999999 !important" class="bg-warning text-white">
        <marquee behavior="" direction="" class="font-weight-bold p-1"><span data-feather="info" class="align-middle feather-16"></span> Hello world</marquee>
      </div>
      {% endif %}
      <div class="container-fluid my-0 py-1 pl-2">
        <div class="mobile-back-btn" onclick="window.history.back()">
          <div class="back-arrow">
            <i data-feather="arrow-left"></i>
          </div>
          <span class="back-text">Retour</span>
        </div>
      </div>
      <div class="pb-1 pl-3 pr-5 d-flex justify-content-between">
        <div class="">
          <h4><i data-feather="{{ icon }}" class="feather-16"></i> {{ title }} {{ year }}</h4>
          <p>{{ subtitle }}</p>
        </div>
        {% if result_boys != None and result_girls != None and not hide_stats %}
        <div class="stats-grid hide-on-phone">
          <div class="stat-card">
            <div class="stat-icon blue">
              <i data-feather="users"></i>
            </div>
            <div>
              <div class="text-muted">Total</div>
              <div class="h5 mb-0">{{ result_boys|add:result_girls }}</div>
            </div>
          </div>
          <div class="stat-card show-on-pc">
            <div class="stat-icon green">
              <i data-feather="user"></i>
            </div>
            <div>
              <div class="text-muted">Garçons</div>
              <div class="h5 mb-0">{{ result_boys }}</div>
            </div>
          </div>
          <div class="stat-card show-on-pc">
            <div class="stat-icon purple">
              <i data-feather="user"></i>
            </div>
            <div>
              <div class="text-muted">Filles</div>
              <div class="h5 mb-0">{{ result_girls }}</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon orange">
              <i data-feather="dollar-sign"></i>
            </div>
            {% if perms.school.view_payment %}
            <div>
              <div class="text-muted">Aujourdh'hui</div>
              <div class="h5 mb-0">{% if total_paid_today %} {{ total_paid_today|intcomma }} FCFA {% else %} 0 {% endif %}</div>
            </div>
            {% endif %}
          </div>
        </div>
        {% endif %}
      </div>
    </div>
  </div>