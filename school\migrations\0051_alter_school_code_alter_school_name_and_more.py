# Generated by Django 4.2.7 on 2024-01-10 06:28

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('school', '0050_school_plan_alter_school_name'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='school',
            name='code',
            field=models.CharField(blank=True, help_text="Laissez vide si vous n'en avez pas.", max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='school',
            name='name',
            field=models.CharField(help_text="Dénomination de l'école", max_length=255, verbose_name="nom de l'école"),
        ),
        migrations.AlterField(
            model_name='school',
            name='plan',
            field=models.Char<PERSON>ield(choices=[('E', 'Standard - Pour gérer une école'), ('C', 'Basic - Pour gérer une classe')], default='E', max_length=1, verbose_name='Forfait choisi'),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name='school',
            name='status',
            field=models.<PERSON>r<PERSON><PERSON>(choices=[('PV', 'Privé'), ('PB', 'Publique')], default='PV', max_length=2, verbose_name="statut de l'école"),
        ),
    ]
