{% load widget_tweaks %}


<section class="tile">
    <form action="{{ request.path }}" method="post" novalidate>
        {% csrf_token %}
        <div class="p-2 alert-info">
            Vous pouvez exporter les donn<PERSON> élèves {% if request.user.school.education == 'A' %} par type d'enseignement (Arabe, Français), {% endif %} par niveau ou par classe
        </div>
        <div class="form-row">
            
            <div class="col-md-6 col-lg-3 form-group {% if user.school.education == 'F' %}d-none{% endif %}">
                <label for="generic_level">Education</label>
                <select name="education" id="education" class="form-control"
                        hx-get="{% url 'exams:sublevels' %}?lang={{lang}}"
                        hx-target="#level_container"
                        hx-include="[name=short_name]">
                    <option value="F" selected="selected">Français</option>
                    <option value="A"><PERSON><PERSON></option>
                </select>
            </div>
            <div class="col-md-6 col-lg-3 form-group">
                <label for="generic_level">Niveau</label>
                <select name="short_name" id="generic_level" class="form-control" 
                        hx-get="{% url 'exams:sublevels' %}?lang={{lang}}"
                        hx-target="#level_container"
                        hx-include="[name=education]">
                    <option value="">--------</option>
                    {% for level in generic_levels %}
                    <option value="{{ level.id }}">{{ level }}</option>
                    {% endfor %}
                </select>
            </div>

            <div class="col-md-6 col-lg-3 form-group" id="level_container">
                <label for="level">Classe</label>
                <select name="level" id="level" class="form-control"></select>
            </div>
            
        </div>
        <div class="form-row">
            <!-- <div class="btn-group ml-2">
                <div class="dropdown">
                    <button class="btn btn-primary dropdown-toggle" type="button" data-toggle="dropdown" aria-expanded="false">
                        <span data-feather="file-text" class="feather feather-16 align-middle"></span> Action
                    </button>
                    <div class="dropdown-menu">
                        <a class="dropdown-item" href="{% url 'school:students_export' %}?lang=F">Voir la liste</a>
                        <div class="dropdown-divider"></div>
                        <a class="dropdown-item" href="{% url 'school:students_export' %}?lang=F">Exporter vers Excel</a>
                    </div>
                </div>
            </div> -->
            <button type="submit" class="btn btn-primary">
                <span data-feather="file-text" class="feather feather-16 align-middle"></span>
                Exporter vers Excel
            </button>
        </div>

        {% if no_data %}
            <div class="form-row mt-2">
                <div class="alert alert-danger">
                    <span data-feather="alert-square" class="feather-16 align-middle"></span>
                    Aucune donnée dans la base de données ne correspond au niveau ou à la classe sélectionnée
                </div>
            </div>
        {% endif %}
    </form>
</section>

<script>
    if (typeof(feather) !== "undefined") {
        feather.replace()
    }
</script>