{% csrf_token %}
<table class="table table-striped table-sm table-hover table-bordered"
    {% if active_nav == 'ar' or active_nav == 'fr' %} id="datatable" {% endif %}>
    <thead class="bg-primary text-white">
    <tr>
        <th>Photo</th>
        <th style="min-width: 120px;">Nom et Prénoms</th>
        <th>Mod</th>
    </tr>
    </thead>
    <tbody>
        {% for enrollment in enrollments %}
            <tr>
                <td class="align-middle text-center" style="min-width: 60px;"
                    hx-target="this" hx-trigger="saved from:body"
                    hx-get="{% url 'school:photo_url' enrollment.student.id %}">
                    {% if enrollment.student.photo %}
                        <img data-original="{{ enrollment.student.photo.url }}"
                            class="lazy border img-thumbnail rounded-circle">
                    {% elif enrollment.is_second_cycle_fr and enrollment.student.student_id %}
                        <img data-original="{{ enrollment.student.government_photo }}"
                            class="lazy border img-thumbnail rounded-circle"
                            id="{{ enrollment.id }}"
                            onload="if (this.src.endsWith('CC')) {
                            this.src = '{{ enrollment.student.blank_photo }}'
                            }"
                            onerror="this.src = '{{ enrollment.student.blank_photo }}'">
                    {% else %}
                        <img data-original="{{ enrollment.student.blank_photo }}"
                            class="lazy border img-thumbnail rounded-circle">
                    {% endif %}
                  </td>
                <td class="align-middle">{{ enrollment }} <br>
                    <span class="text-muted">{{ enrollment.student.student_id|default_if_none:''}}</span></td>
                <td class="align-middle" hx-target="this">                    <div class="d-flex" style="gap: 0.5rem;">
                        <!-- Camera Icon - Trigger Camera -->
                        <button type="button" class="btn btn-sm btn-outline-primary"
                                id="camera-btn-{{ enrollment.student.id }}"
                                onclick="triggerCamera({{ enrollment.student.id }})"
                                title="Prendre une photo">
                            <i data-feather="camera" class="feather-16" id="camera-icon-{{ enrollment.student.id }}"></i>
                            <span class="spinner-border spinner-border-sm d-none" id="camera-spinner-{{ enrollment.student.id }}" role="status" aria-hidden="true"></span>
                        </button>

                        <!-- File Icon - Open File Browser -->
                        <button type="button" class="btn btn-sm btn-outline-success"
                                id="file-btn-{{ enrollment.student.id }}"
                                onclick="triggerFileUpload({{ enrollment.student.id }})"
                                title="Choisir un fichier">
                            <i data-feather="file" class="feather-16" id="file-icon-{{ enrollment.student.id }}"></i>
                            <span class="spinner-border spinner-border-sm d-none" id="file-spinner-{{ enrollment.student.id }}" role="status" aria-hidden="true"></span>
                        </button>                        <!-- Hidden file input for each student -->
                        <input type="file"
                               id="fileInput-{{ enrollment.student.id }}"
                               accept=".jpg,.png,.jpeg,image/*"
                               style="display: none;"
                               onchange="handleFileUpload(this, {{ enrollment.student.id }})"
                               oncancel="handleFileCancel({{ enrollment.student.id }})"
                               capture="environment">
                    </div>
                </td>
            </tr>
        {% endfor %}
</tbody>
</table>

<script>
    if (typeof(feather) != "undefined") {
        feather.replace();
        $("img.lazy").lazyload();
    }    document.addEventListener("DOMContentLoaded", function() {
        $("img.lazy").lazyload();
        
        // Set up cancel event listeners for all file inputs
        document.querySelectorAll('input[type="file"]').forEach(function(fileInput) {
            const studentId = fileInput.id.replace('fileInput-', '');
            
            // Handle the cancel event (when user closes file dialog without selecting)
            fileInput.addEventListener('cancel', function() {
                handleFileCancel(studentId);
            });
            
            // Also handle focus event to detect when file dialog is closed
            let wasClicked = false;
            fileInput.addEventListener('click', function() {
                wasClicked = true;
            });
            
            // Use a timeout to check if file was selected after dialog closes
            window.addEventListener('focus', function() {
                if (wasClicked) {
                    setTimeout(function() {
                        if (!fileInput.files.length) {
                            handleFileCancel(studentId);
                        }
                        wasClicked = false;
                    }, 100);
                }
            });
        });
    });// Function to trigger camera for photo capture
    function triggerCamera(studentId) {
        showLoadingState(studentId, 'camera');
        const fileInput = document.getElementById(`fileInput-${studentId}`);
        // Set capture attribute to use camera
        fileInput.setAttribute('capture', 'environment');
        fileInput.click();
    }

    // Function to trigger file browser
    function triggerFileUpload(studentId) {
        showLoadingState(studentId, 'file');
        const fileInput = document.getElementById(`fileInput-${studentId}`);
        // Remove capture attribute to use file browser
        fileInput.removeAttribute('capture');
        fileInput.click();
    }

    // Function to show loading state
    function showLoadingState(studentId, buttonType) {
        const icon = document.getElementById(`${buttonType}-icon-${studentId}`);
        const spinner = document.getElementById(`${buttonType}-spinner-${studentId}`);
        const button = document.getElementById(`${buttonType}-btn-${studentId}`);
        
        if (icon && spinner && button) {
            icon.classList.add('d-none');
            spinner.classList.remove('d-none');
            button.disabled = true;
        }
    }

    // Function to hide loading state
    function hideLoadingState(studentId, buttonType) {
        const icon = document.getElementById(`${buttonType}-icon-${studentId}`);
        const spinner = document.getElementById(`${buttonType}-spinner-${studentId}`);
        const button = document.getElementById(`${buttonType}-btn-${studentId}`);
        
        if (icon && spinner && button) {
            icon.classList.remove('d-none');
            spinner.classList.add('d-none');
            button.disabled = false;
        }
    }

    // Function to handle file cancel
    function handleFileCancel(studentId) {
        hideLoadingState(studentId, 'camera');
        hideLoadingState(studentId, 'file');
    }

    // Function to handle file upload
    function handleFileUpload(fileInput, studentId) {
        const file = fileInput.files[0];
        
        // If no file is selected (user cancelled), hide all loading states
        if (!file) {
            hideLoadingState(studentId, 'camera');
            hideLoadingState(studentId, 'file');
            return;
        }

        // Create a form data object
        const formData = new FormData();
        formData.append('photo', file);
        formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);

        // Resize image before upload
        reduceImageSize(fileInput, 600, 600, true, function() {
            // After resizing, upload the file
            uploadPhoto(studentId, fileInput.files[0]);
        });
    }    // Function to upload photo via HTMX-style request
    function uploadPhoto(studentId, file) {
        const formData = new FormData();
        formData.append('photo', file);
        formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);

        fetch(`/eleves/${studentId}/importer-photo/`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
            }
        })
        .then(response => {
            // Hide loading states for both buttons
            hideLoadingState(studentId, 'camera');
            hideLoadingState(studentId, 'file');
            
            if (response.ok) {
                // Trigger the saved event to refresh the photo
                document.body.dispatchEvent(new CustomEvent('saved'));
            } else {
                console.error('Upload failed');
            }
        })
        .catch(error => {
            // Hide loading states for both buttons on error
            hideLoadingState(studentId, 'camera');
            hideLoadingState(studentId, 'file');
            console.error('Error:', error);
        });
    }

    // Image resizing function (adapted from photo_form.html)
    function reduceImageSize(fileInput, maxWidth, maxHeight, isPhoto, callback) {
        const file = fileInput.files[0];
        if (!file) {
            console.log('No file');
            return;
        }

        const reader = new FileReader();
        const fileSize = file.size / 1024;
        console.log('Original size', fileSize);

        if ((isPhoto && fileSize <= 50) || (!isPhoto && fileSize <= 100)) {
            console.log('File size acceptable, no resizing needed');
            if (callback) callback();
            return;
        }

        reader.onload = (readerEvent) => {
            const image = new Image();

            image.onload = () => {
                const canvas = document.createElement('canvas');
                let width = image.width;
                let height = image.height;

                if (width > height && width > maxWidth) {
                    height *= maxWidth / width;
                    width = maxWidth;
                } else if (height > maxHeight) {
                    width *= maxHeight / height;
                    height = maxHeight;
                }

                canvas.width = width;
                canvas.height = height;

                const context = canvas.getContext('2d');
                context.drawImage(image, 0, 0, width, height);

                const dataUrl = canvas.toDataURL(file.type);
                const newFile = dataURLtoFile(dataUrl, file.name);
                console.log('Resized', newFile.size);

                // Create a new FileList object with the resized file
                const newFileList = createFileList(newFile);
                fileInput.files = newFileList;

                if (callback) callback();
            };

            image.src = readerEvent.target.result;
        };

        reader.readAsDataURL(file);
    }

    // Helper function to convert data URL to a File object
    function dataURLtoFile(dataUrl, fileName) {
        const arr = dataUrl.split(',');
        const mime = arr[0].match(/:(.*?);/)[1];
        const bstr = atob(arr[1]);
        let n = bstr.length;
        const u8arr = new Uint8Array(n);

        while (n--) {
            u8arr[n] = bstr.charCodeAt(n);
        }

        return new File([u8arr], fileName, { type: mime });
    }

    // Helper function to create a new FileList object
    function createFileList(...files) {
        const dataTransfer = new DataTransfer();
        for (const file of files) {
            dataTransfer.items.add(file);
        }
        return dataTransfer.files;
    }
</script>