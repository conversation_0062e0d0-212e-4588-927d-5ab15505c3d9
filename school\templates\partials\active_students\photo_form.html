{% load widget_tweaks %}

<form action="" class="d-flex jusify-content-center align-items-center" 
      id="{{ student_id}}" hx-include="[name=photo]"
      hx-post="{% url 'school:photo_import' student_id %}"
      hx-trigger="change delay:500ms"
      enctype="multipart/form-data">
    {% csrf_token %}
    {% render_field form.photo|attr:"class:form-control"|attr:"accept:.jpg, .png, .jpeg"|attr:"onchange:reduceImageSize(this, 600, 600, true);" %}
</form>

<script>
    function reduceImageSize(fileInput, maxWidth, maxHeight, isPhoto) {
        const file = event.target.files[0];
        if (!file) {
			console.log('No file')
            return;
        }
        const reader = new FileReader();
        
		  const fileSize = file.size / 1024;
		  console.log('Original size', fileSize)
		  if ((isPhoto && fileSize <= 50) || (!isPhoto && fileSize <= 100)) {
            console.log('Goodbye');
			return;
		  }

		  reader.onload = (readerEvent) => {
			const image = new Image();
	  
			image.onload = () => {
			  const canvas = document.createElement('canvas');
			  let width = image.width;
			  let height = image.height;
	  
			  if (width > height && width > maxWidth) {
				height *= maxWidth / width;
				width = maxWidth;
			  } else if (height > maxWidth) {
				width *= maxHeight / height;
				height = maxHeight;
			  }
	  
			  canvas.width = width;
			  canvas.height = height;
	  
			  const context = canvas.getContext('2d');
			  context.drawImage(image, 0, 0, width, height);
	  
			  const dataUrl = canvas.toDataURL(file.type);
			  const newFile = dataURLtoFile(dataUrl, file.name);
		  	  console.log('Resized', newFile.size)

			  // Create a new FileList object with the resized file
			  const newFileList = createFileList(newFile);

			  // Replace the entire FileList in the file input
			  fileInput.files = newFileList;

			// Replace the initial image with the resized version
			// fileInput.files[0] = newFile;
			  fileInput.files = newFileList;
			};
	  
			image.src = readerEvent.target.result;
		  };
	  
		  reader.readAsDataURL(file);
	  }
	  
	  // Helper function to convert data URL to a File object
	  function dataURLtoFile(dataUrl, fileName) {
		const arr = dataUrl.split(',');
		const mime = arr[0].match(/:(.*?);/)[1];
		const bstr = atob(arr[1]);
		let n = bstr.length;
		const u8arr = new Uint8Array(n);
	  
		while (n--) {
		  u8arr[n] = bstr.charCodeAt(n);
		}
		
		return new File([u8arr], fileName, { type: mime });
	}

	// Helper function to create a new FileList object
	function createFileList(...files) {
		const dataTransfer = new DataTransfer();
		for (const file of files) {
		dataTransfer.items.add(file);
		}
		return dataTransfer.files;
	}
    if (typeof(feather) != "undefined") {
        feather.replace();
    }
</script>