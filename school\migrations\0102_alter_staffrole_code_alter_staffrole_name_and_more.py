# Generated by Django 5.1 on 2024-10-21 13:22

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('school', '0101_staff_date_enlisted'),
    ]

    operations = [
        migrations.AlterField(
            model_name='staffrole',
            name='code',
            field=models.CharField(help_text='code unique pour faciliter les importations', max_length=10, unique=True, verbose_name='code_emploi'),
        ),
        migrations.AlterField(
            model_name='staffrole',
            name='name',
            field=models.CharField(max_length=255, verbose_name='emploi'),
        ),
        migrations.CreateModel(
            name='SalaryPaymentOptions',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, verbose_name='rubrique')),
                ('amount', models.PositiveIntegerField(default=0, verbose_name='montant')),
                ('school', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='school.school')),
            ],
        ),
    ]
