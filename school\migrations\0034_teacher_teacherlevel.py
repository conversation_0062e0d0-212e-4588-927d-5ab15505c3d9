# Generated by Django 4.2.4 on 2023-10-19 10:05

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('exams', '0035_educationyearresult_admission_average_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('school', '0033_alter_school_cycle'),
    ]

    operations = [
        migrations.CreateModel(
            name='Teacher',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('last_name', models.CharField(max_length=255)),
                ('first_name', models.CharField(max_length=255)),
                ('education', models.CharField(choices=[('A', 'Arabe'), ('F', 'Français')], default='F', max_length=1)),
                ('active', models.BooleanField(default=True)),
                ('origin', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='school.year')),
                ('school', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='school.school')),
                ('user', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'enseignant',
            },
        ),
        migrations.CreateModel(
            name='TeacherLevel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('level', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='school.level')),
                ('subjects', models.ManyToManyField(blank=True, to='exams.levelsubject')),
                ('teacher', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='school.teacher')),
            ],
        ),
    ]
