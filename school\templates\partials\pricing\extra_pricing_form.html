{% extends 'partials/modal.html' %}
{% load widget_tweaks %}

{% block modal_title %} 
    Frais annexes
{% endblock %}

{% block modal_body %} 
    <table class="table table-bordered">
        <thead>
            <tr>
                <th style="display: none;">ID</th>
                <th>Rubrique</th>
                <th>Type</th>
                <th>Montant</th>
            </tr>
        </thead>
        <tbody>
            {{ formset.management_form }}
            {% for form in formset %}
                <tr class="mb-3 row">
                    <td style="display: none;">{% render_field form.id class='form-control' readonly='readonly' %}</td>
                    <td>{% render_field form.category class='form-control' %}</td>
                    <td>{% render_field form.price_type class='form-control' %}</td>
                    <td>{% render_field form.price class='form-control' %}</td>
                </tr>
            {% endfor %}
        </tbody>
    </table>
    <div class="form-row">
        <div class="invalid-feedback">{{ formset.errors|first }}</div>
    </div>
{% endblock %}

{% block modal_footer %}
    <input type="submit" class="btn btn-success" value="Enregistrer">
{% endblock %}