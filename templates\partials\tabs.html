{% if request.htmx %}
    <title>{% if page_title %} {{ page_title }} {% elif description %} {{ description }} {% elif subtitle %} {{ subtitle }} {% elif title %} {{ title }} {% else %} {{ item.description|default:'Gestion des Ecoles' }} {% endif %} | EcolePro</title>
{% endif %}

{% if nav_items %}
<ul class="nav nav-tabs">
    {% for item in nav_items %}
        <li class="nav-item">
        <a class="nav-link {% if item.active_nav == active_nav %}active{% endif %}" 
            href="{{ item.url }}" 
            hx-get="{{ item.url }}" 
            hx-target="#app-content"
            hx-push-url="true"><span data-feather="{{ item.icon|default:'chevron-right' }}" class="feather-16 align-middle"></span> {{ item.description }}</a>
        </li>
    {% endfor %}
    {% include 'partials/indicator.html' %}
</ul>
{% endif %}

<script>
    if (typeof(feather) !== "undefined") {
        feather.replace();
    }
</script>