{% csrf_token %}
{% if term %}
<select name="selected-term" id="selected-term" class="d-none">
    <option value="{{ term }}">Période</option>
</select>
{% endif %}
<table class="table table-striped table-bordered table-sm table-hover" id="datatable" style="font-size: 11.5px;">
    <thead class="bg-primary text-white">
    <tr>
        <th style="max-width: 10px;" class="align-middle"><input type="checkbox" name="select-all" id="select-all" onclick="checkAll(this)"></th>
        <th style="max-width: 80px">MATRICULE</th>
        <th style="min-width: 120px" class="sticky-column sticky-header">NOM ET PRENOMS</th>
        {% if lang == 'A' %}
        <th>NOM EN ARABE</th>
        {% endif %}
        {% for subject in subjects %}
        <th>{{ subject.get_first_chars }} <br> <h6 class="text-muted rounded-pill">/ {{ subject.max }}</h6> </th>
        {% endfor %}
        <th>TOTAL</th>
        <th>MOY. <br> <h6 class="text-muted rounded-pill">/ {{ term_max }}</h6></th>
        <th>RANG</th>
        <th>BULLETIN</th>

    </tr>
    </thead>
    <tbody>
    {% for enrollment, grades in data.items %}
    <tr>
        <td class="align-middle">
            <input type="checkbox" name="{{ enrollment.id }}" id="{{ enrollment.id }}" onclick="validateForm()">
        </td> 
        <td class="align-middle">{{ enrollment.student.student_id|default_if_none:enrollment.student.identifier }}</td>
        <td class="align-middle sticky-column {% if not forloop.counter|divisibleby:'2' %} bg-lightgray {% else %} bg-white {% endif %}" style="min-width: 120px;">{{ enrollment }}</td>
        {% if lang == 'A' %}
            <td class="align-middle text-right" style="min-width: 80px;">{{ enrollment.student.full_name_ar|default_if_none:'' }}</td>
        {% endif %}
        {% for grade in grades %}
        <td class="align-middle {% if not grade.grade %} text-danger {% endif %}" id="_{{ grade.subject.id }}_{{ term }}_{{ enrollment.id }}">
            {% if grade.grade or grade.grade == 0 %}
            <a href="" hx-get="{% url 'exams:grade_edit' %}?enrollment={{ enrollment.id }}&subject={{ grade.subject.id }}&level={{short_name}}&education={{lang}}&grade={{ grade.grade }}"
               hx-target="#dialog">
               {{ grade.grade }}
            </a>
            {% elif not grade.grade and not grade %}
            <a href="" hx-get="{% url 'exams:grade_edit' %}?enrollment={{ enrollment.id }}&subject={{ grade.subject.id }}&level={{short_name}}&education={{lang}}&grade={{ grade.grade }}"
               hx-target="#dialog" class="text-danger">
               NC
            </a>
            {% elif not grade.grade %}
            <span>
            <a href="" hx-get="{% url 'exams:grade_edit' %}?enrollment={{ enrollment.id }}&subject={{ grade.subject.id }}&level={{short_name}}&education={{lang}}&grade={{ grade.grade }}"
               hx-target="#dialog" class="text-danger">
               {% if grade > 0 or grade == 0 %} {{ grade }} 
               {% else %} NC 
               {% endif %}
            </a>
            {% endif %}    
        </td>
        {% endfor %}
        <td class="d-flex">
            <a target="_blank" href="{% url 'exams:student_report' %}?eleve={{ enrollment.id }}&periode={{ term }}" class="btn btn-sm btn-warning">1</a>
            <a target="_blank" href="{% url 'exams:student_report' %}?eleve={{ enrollment.id }}&periode={{ term }}&report_type={{ report2 }}" class="ml-1 btn btn-sm btn-info">2</a>
        </td>
    </tr>
    {% endfor %}
    </tbody>
</table>

<!-- End pagination -->
<hr>
{% include 'partials/grade/grade_action.html' %}

<script>
    function checkAll(checkbox) {
      var checkboxes = document.getElementsByTagName('input');
      for (var i = 0; i < checkboxes.length; i++) {
        if (checkboxes[i].type === 'checkbox') {
          checkboxes[i].checked = checkbox.checked;
        }
      }

      validateForm()
    }

    function validateForm(event) {
            console.log('Validating');
    // Get all checkboxes within the form
            var checkboxes = document.querySelectorAll('input[type="checkbox"]');

            // Check if any of the checkboxes are checked
            var isChecked = Array.from(checkboxes).some(function(checkbox) {
                return checkbox.checked;
            });

            // If none of the checkboxes are checked, prevent form submission
            if (!isChecked) {
                if (!document.querySelector('#submit-btn').classList.contains('disabled')) {
                    document.querySelector('#submit-btn').classList.add('disabled')
                };
                document.querySelector('#submit-btn').style.pointerEvents = "none"
                return false
            }

            // If at least one checkbox is checked, allow the form to be submitted
            if (document.querySelector('#submit-btn').classList.contains('disabled')) {
                    document.querySelector('#submit-btn').classList.remove('disabled')
            };
            document.querySelector('#submit-btn').style.pointerEvents = "auto";


    }

    // $.fn.dataTable.ext.errMode = 'none';
	// 	$('#table').DataTable({
	// 	lengthMenu: [
	// 		[ 100, 200, 300],
	// 		[ '100', '200', '300']],
    //     drawCallback: function() {
    //         htmx.process(document.body.querySelector('#table'))
    //         feather.replace();
    //     }
    // })

    // htmx.process(document.body)
    feather.replace();
</script>