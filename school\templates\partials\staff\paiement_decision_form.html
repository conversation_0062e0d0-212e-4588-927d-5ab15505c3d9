{% extends 'partials/modal.html' %}
{% load widget_tweaks %}
{% load i18n %}

{% block modal_title %} {{ form_title }} {% endblock %}

{% block modal_body %}
    {% csrf_token %}
    <div class="container">
        <div class="row">
            <!-- Informations Générales -->
            <div class="col-md-4">
                <h3>{% trans "Généralités" %}</h3>
                <div class="row">
                    <div class="form-group col-12">
                        <label class="{% if form.year.field.required %} font-weight-bold {% endif %}" for="id_year">{% trans "Année scolaire" %} {% if form.year.field.required %} <span class="text-danger">*</span>{% endif %}</label>
                        {% render_field form.year class="form-control" %}
                    </div>
                    <div class="form-group col-12">
                        <label class="{% if form.month.field.required %} font-weight-bold {% endif %}" for="id_month">{% trans "Mois" %} {% if form.month.field.required %} <span class="text-danger">*</span>{% endif %}</label>
                        {% render_field form.month class="form-control" %}
                    </div>
                    <div class="form-group col-12">
                        <label class="{% if form.payment_method.field.required %} font-weight-bold {% endif %}" for="id_payment_method">{% trans "Moyen de paiement" %} {% if form.payment_method.field.required %} <span class="text-danger">*</span>{% endif %}</label>
                        {% render_field form.payment_method class="form-control" %}
                    </div>
                    <div class="form-group col-12">
                        <label class="{% if form.payment_date.field.required %} font-weight-bold {% endif %}" for="id_payment_date">{% trans "Date de paie" %} {% if form.payment_date.field.required %} <span class="text-danger">*</span>{% endif %}</label>
                        {% render_field form.payment_date class="form-control" %}
                    </div>
                    <div class="form-group col-12">
                        <label class="{% if form.decision_number.field.required %} font-weight-bold {% endif %}" for="id_decision_number">{% trans "Numéro de décision" %} {% if form.decision_number.field.required %} <span class="text-danger">*</span>{% endif %}</label>
                        {% render_field form.decision_number class="form-control" %}
                    </div>
                </div>
            </div>

            <!-- Emploi -->
            <div class="col-md-4">
                <h3>{% trans "Gains" %}</h3>
                <div class="form-group">
                    <label class="{% if form.salary.field.required %} font-weight-bold {% endif %}" for="id_salary">{% trans "SALAIRE DE BASE" %} {% if form.salary.field.required %} <span class="text-danger">*</span>{% endif %}</label>
                    <input type="text" class="form-control" name="salary" id="salary" value="BASE" disabled="disabled">
                </div>
                {% for field in form %}
                    {% if '_add_' in field.name %}
                        <div class="form-group">
                            <label class="{% if field.field.required %} font-weight-bold {% endif %}" for="id_work_hours">{{ field.label }} {% if field.field.required %} <span class="text-danger">*</span>{% endif %}</label>
                            {% render_field field class="form-control" %}

                            {% if field.help_text %}
                                <span class="form-text text-muted">{{ field.help_text }}</span>
                            {% endif %}
                        </div>
                    {% endif %}
                {% endfor %}
            </div>
            <div class="col-md-4">
                <h3>{% trans "Retenues" %}</h3>
                {% for field in form %}
                    {% if '_sub_' in field.name %}
                        <div class="form-group">
                            <label class="{% if field.field.required %} font-weight-bold {% endif %}" for="id_work_hours">{{ field.label }} {% if field.field.required %} <span class="text-danger">*</span>{% endif %}</label>
                            {% render_field field class="form-control" %}

                            {% if field.help_text %}
                                <span class="form-text text-muted">{{ field.help_text }}</span>
                            {% endif %}
                        </div>
                    {% endif %}
                {% endfor %}
            </div>
            <!-- Photo -->
        </div>
    </div>

    <script>
        flatpickr("[name*='date']", {
        dateFormat: "d/m/Y",
        enableTime: false,
        allowInput: true,
        timeZone: "Africa/Abidjan",
        disableMobile: "true",
        locale: "fr",
        maxDate: 'today'
    });
</script>
{% endblock %}


{% block modal_footer %}
    <button type="submit" href="" class="btn btn-success">
        <span class="feather-16 align-middle" data-feather="check-circle"></span>
        Enregistrer
    </button>
{% endblock %}
