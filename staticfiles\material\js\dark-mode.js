// Material Design Dark Mode Toggle Functionality

document.addEventListener('DOMContentLoaded', function() {
    // Get the dark mode toggle button
    const darkModeToggle = document.getElementById('darkModeToggle');
    
    // Check if dark mode is enabled in localStorage
    const isDarkMode = localStorage.getItem('darkMode') === 'enabled';
    
    // Apply dark mode if it's enabled
    if (isDarkMode) {
        document.body.classList.add('dark-mode');
        updateDarkModeIcon(true);
    }
    
    // Add click event listener to the dark mode toggle button
    if (darkModeToggle) {
        darkModeToggle.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Toggle dark mode class on body
            const isDarkModeEnabled = document.body.classList.toggle('dark-mode');
            
            // Update localStorage
            if (isDarkModeEnabled) {
                localStorage.setItem('darkMode', 'enabled');
            } else {
                localStorage.setItem('darkMode', 'disabled');
            }
            
            // Update the icon
            updateDarkModeIcon(isDarkModeEnabled);
        });
    }
    
    // Function to update the dark mode icon
    function updateDarkModeIcon(isDarkMode) {
        if (!darkModeToggle) return;
        
        const iconElement = darkModeToggle.querySelector('.material-icons');
        if (iconElement) {
            if (isDarkMode) {
                iconElement.textContent = 'light_mode';
            } else {
                iconElement.textContent = 'dark_mode';
            }
        }
    }
});

// Make sure Material components are initialized when the script loads
document.addEventListener('htmx:load', function() {
    // Re-initialize Material components after HTMX loads
    if (typeof mdc !== 'undefined') {
        const textFields = document.querySelectorAll('.mdc-text-field');
        textFields.forEach(textField => {
            mdc.textField.MDCTextField.attachTo(textField);
        });
        
        const selects = document.querySelectorAll('.mdc-select');
        selects.forEach(select => {
            mdc.select.MDCSelect.attachTo(select);
        });
        
        const checkboxes = document.querySelectorAll('.mdc-checkbox');
        checkboxes.forEach(checkbox => {
            mdc.checkbox.MDCCheckbox.attachTo(checkbox);
        });
        
        const dataTable = document.querySelector('.mdc-data-table');
        if (dataTable) {
            mdc.dataTable.MDCDataTable.attachTo(dataTable);
        }
    }
});
