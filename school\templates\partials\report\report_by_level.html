{% load humanize %}
<div class="tile" id="tile">
    <div class="table-responsive">
        <table class="table table-striped table-hover table-sm table-bordered" id="datatable">
            <thead class="bg-primary text-white">
            <tr>
                <th class="align-middle">Classe</th>
                <th class="align-middle text-center">Effectif</th>
                <th class="align-middle text-right" style="min-width: 80px;">Encaissé</th>
                <th class="align-middle text-center" style="max-width: 70px">Résumé</th>
                <th class="align-middle text-center" style="max-width: 70px">D<PERSON>taillé</th>
            </tr>
            </thead>
            <tbody>
            {% for level in levels %}
            <tr>
                <td class="align-middle">{{ level }}</td>
                <td class="align-middle text-center">{{ level.students }}</td>
                <td class="align-middle text-right font-weight-bold">{% if level.total %} {{ level.total|intcomma }} {% else %} 0 {% endif %} F</td>
                <td class="align-middle text-center font-weight-bold" style="max-width: 70px">
                    <a href="{% url 'school:level_payments_pdf' %}?level_id={{ level.id }}&education={{ lang }}" class="btn btn-sm btn-warning btn-block">
                        <span data-feather="file-text" class="feather-16 align-middle"></span> {{ level }}
                    </a>
                </td>
                <td class="align-middle text-center font-weight-bold" style="max-width: 70px">
                    <a href="{% url 'school:level_payments_pdf' %}?level_id={{ level.id }}&education={{ lang }}&detailed=1" class="btn btn-sm btn-danger btn-block">
                        <span data-feather="file-text" class="feather-16 align-middle"></span> {{ level }}
                    </a>
                </td>
            </tr>
            {% endfor %}
            </tbody>
            <tfoot class="bg-success text-white font-weight-bold h6">
                <tr>
                    <td class="align-middle">TOTAL GENERAL</td>
                    <td class="align-middle text-center">{{ students }}</td>
                    <td class="align-middle text-right">{{ grand_total|intcomma }} F</td>
                    <td class="align-middle text-center">
                        <!-- <a href="" class="btn btn-sm btn-danger btn-block">
                            <span data-feather="file-text" class="feather-16 align-middle"></span>Résumé
                        </a> -->
                    </td>
                </tr>
            </tfoot>
        </table>
    </div>
</div>