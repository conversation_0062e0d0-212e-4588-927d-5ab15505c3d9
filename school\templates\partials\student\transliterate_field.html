<datalist id="{{ value }}">
    {% for item in translations %}
    <option value="{{ item }}">{{ item }}</option>
    {% endfor %}
</datalist>
<input type="text" list="{{ value }}" name="{{ field|default:'full_name_ar' }}" id="id_{{ field|default:'full_name_ar' }}" value="{{ value }}" class="form-control" hx-swap="outerHTML" style="background-color: rgb(255, 254, 224) !important;">
<span id="info" hx-swap-oob="true" class="text-success">Traduction automatique vers l'Arabe: <span class="font-weight-bold">{{ value }}</span></span> </span>