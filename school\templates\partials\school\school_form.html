{% extends 'partials/modal.html' %}
{% load widget_tweaks %}

{% block modal_title %} {{ form_title }} {% endblock %}

{% block modal_body %}
<div class="lead text-center text-primary font-weight-bold mb-1"><u>INFOS ECOLE</u></div>
<div class="form-row">
    <div class="form-group col-md-6 col-lg-3 font-weight-bold">
        <label for="{{ form.location.id_for_label }}">DRENA</label>
        {% render_field form.location class='form-control' %}
        <span class="invalid-feedback">{{ form.location.errors|first }}</span>
        {% if form.location.help_text %}
            <span class="form-text text-muted">{{ form.location.help_text }}</span>
        {% endif %}
    </div>
    <div class="form-group col-md-6 col-lg-3 font-weight-bold">
        <label for="{{ form.status.id_for_label }}">{{ form.status.label|upper }}</label>
        {% render_field form.status class='form-control' %}
        <span class="invalid-feedback">{{ form.status.errors|first }}</span>
        {% if form.status.help_text %}
            <span class="form-text text-muted">{{ form.status.help_text }}</span>
        {% endif %}
    </div>
    <div class="form-group col-lg-3 font-weight-bold">
        <label for="{{ form.education.id_for_label }}">{{ form.education.label|upper }}</label>
        {% render_field form.education class='form-control' %}
        <span class="invalid-feedback">{{ form.education.errors|first }}</span>
        {% if form.education.help_text %}
            <span class="form-text text-muted">{{ form.education.help_text }}</span>
        {% endif %}
    </div>
    <div class="form-group col-md-6 col-lg-3 font-weight-bold">
        <label for="{{ form.cycle.id_for_label }}">{{ form.cycle.label|upper }}</label>
        {% render_field form.cycle class='form-control' %}
        <span class="invalid-feedback">{{ form.cycle.errors|first }}</span>
        {% if form.cycle.help_text %}
            <span class="form-text text-muted">{{ form.cycle.help_text }}</span>
        {% endif %}
    </div>
</div>
<div class="form-row">
    <div class="form-group col-lg-6 font-weight-bold">
        <label for="{{ form.name.id_for_label }}">{{ form.name.label|upper }}</label>
        {% render_field form.name class='form-control' %}
        <span class="invalid-feedback">{{ form.name.errors|first }}</span>
    </div>
    <div class="form-group col-lg-3">
        <label for="{{ form.address.id_for_label }}">{{ form.address.label|upper }}</label>
        {% render_field form.address class='form-control' %}
        <span class="invalid-feedback">{{ form.address.errors|first }}</span>
        {% if form.address.help_text %}
            <span class="form-text text-muted">{{ form.address.help_text }}</span>
        {% endif %}
    </div>
    <div class="form-group col-lg-3">
        <label for="{{ form.logo.id_for_label }}">LOGO (OPTIONNEL)</label>
        <input type="file" class="form-control" name="logo" 
        id="{{ form.logo.id_for_label }}"
        accept=".jpg, .png, .jpeg" >    
    </div>
    <div class="form-group col-lg-3">
        <label for="{{ form.IEP.id_for_label }}">IEPP</label>
        {% render_field form.IEP class='form-control' %}
        <span class="invalid-feedback">{{ form.IEP.errors|first }}</span>
        {% if form.IEP.help_text %}
            <span class="form-text text-muted">{{ form.IEP.help_text }}</span>
        {% endif %}
    </div>
    <div class="form-group col-lg-3">
        <label for="{{ form.secteur_p.id_for_label }}">SECTEUR PEDAGOGIQUE</label>
        {% render_field form.secteur_p class='form-control' %}
        <span class="invalid-feedback">{{ form.secteur_p.errors|first }}</span>
        {% if form.secteur_p.help_text %}
            <span class="form-text text-muted">{{ form.secteur_p.help_text }}</span>
        {% endif %}
    </div>

    <div class="form-group col-lg-3 font-weight-bold">
        <label for="{{ form.phone1.id_for_label }}">{{ form.phone1.label|upper }}</label>
        {% render_field form.phone1 class='form-control' %}
        <span class="invalid-feedback">{{ form.phone1.errors|first }}</span>
        {% if form.phone1.help_text %}
            <span class="form-text text-muted">{{ form.phone1.help_text }}</span>
        {% endif %}
    </div>
    <div class="form-group col-lg-3">
        <label for="{{ form.phone2.id_for_label }}">{{ form.phone2.label|upper }}</label>
        {% render_field form.phone2 class='form-control' %}
        <span class="invalid-feedback">{{ form.phone2.errors|first }}</span>
        {% if form.phone2.help_text %}
            <span class="form-text text-muted">{{ form.phone2.help_text }}</span>
        {% endif %}
    </div>
</div>
<div class="lead text-center text-primary font-weight-bold"><u>INFOS ABONNEMENT ET UTILISATEURS</u></div>
<div class="form-row">
    <div class="form-group col-lg-3 font-weight-bold">
        <label for="{{ subscription_form.plan.id_for_label }}">TYPE D'ABONNEMENT</label>
        {% render_field subscription_form.plan class='form-control' %}
        <span class="invalid-feedback">{{ subscription_form.plan.errors|first }}</span>
        {% if subscription_form.plan.help_text %}
            <span class="form-text text-muted">{{ subscription_form.plan.help_text }}</span>
        {% endif %}
    </div>
    <div class="form-group col-lg-3">
        <label for="{{ subscription_form.level.id_for_label }}">CLASSE A GERER</label>
        {% render_field subscription_form.level class='form-control' %}
        <span class="invalid-feedback">{{ subscription_form.level.errors|first }}</span>
        <span class="form-text text-muted">Pour l'option (BASIC)
        </span>
    </div>
    <div class="form-group col-lg-3 font-weight-bold">
        <label for="{{ subscription_form.admin.id_for_label }}">FONDATEUR DE L'ECOLE</label>
        {% render_field subscription_form.admin class='form-control' %}
        <span class="invalid-feedback">{{ form.phone.errors|first }}</span>
    </div>
    <div class="form-group col-lg-3 font-weight-bold">
        <label for="{{ subscription_form.admin_phone.id_for_label }}">CONTACT DU FONDATEUR</label>
        {% render_field subscription_form.admin_phone class='form-control' %}
        <span class="invalid-feedback">{{ form.admin_phone.errors|first }}</span>
    </div>
    <div class="form-group col-lg-3 font-weight-bold">
        <label for="{{ subscription_form.scientist.id_for_label }}">INFORMATICIEN</label>
        {% render_field subscription_form.scientist class='form-control' %}
        <span class="invalid-feedback">{{ form.scientist_phone.errors|first }}</span>
    </div>
    <div class="form-group col-lg-3 font-weight-bold">
        <label for="{{ subscription_form.scientist_phone.id_for_label }}">CONTACT DE L'INFORMATICIEN</label>
        {% render_field subscription_form.scientist_phone class='form-control' %}
        <span class="invalid-feedback">{{ form.scientist_phone.errors|first }}</span>
    </div>
    <div class="form-group col-lg-6 font-weight-bold">
        <label for="{{ subscription_form.sms_phone.id_for_label }}">CONTACT SUR LEQUEL VOUS RECEVREZ LES INFOS DE CONNEXION</label>
        {% render_field subscription_form.sms_phone class='form-control' %}
        <span class="invalid-feedback">{{ form.sms_phone.errors|first }}</span>
    </div>
</div>

{% endblock %}

{% block modal_footer %}
    <button type="submit" href="" class="btn btn-success">Enregistrer</button>
{% endblock %}