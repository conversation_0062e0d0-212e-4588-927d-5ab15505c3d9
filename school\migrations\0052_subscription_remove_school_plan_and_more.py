# Generated by Django 4.2.7 on 2024-01-10 06:45

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('school', '0051_alter_school_code_alter_school_name_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Subscription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('plan_type', models.CharField(choices=[('T', 'Test'), ('P', 'Premium')], default='P', max_length=1)),
                ('plan', models.CharField(choices=[('S', 'Standard - Pour gérer une école'), ('L', 'Basic - Pour gérer une classe')], default='S', max_length=1, verbose_name='Forfait choisi')),
            ],
            options={
                'verbose_name': 'Abonnement Annuel',
                'verbose_name_plural': 'Abonnements Annuels',
            },
        ),
        migrations.RemoveField(
            model_name='school',
            name='plan',
        ),
        migrations.RemoveField(
            model_name='school',
            name='plan_types',
        ),
        migrations.CreateModel(
            name='SubscriptionPayment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('amount', models.PositiveSmallIntegerField(verbose_name='montant')),
                ('subscription', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='school.subscription')),
            ],
            options={
                'verbose_name': 'Versements pour abonnements',
            },
        ),
        migrations.AddField(
            model_name='subscription',
            name='school',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='school.school'),
        ),
        migrations.AddField(
            model_name='subscription',
            name='year',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='school.year'),
        ),
    ]
