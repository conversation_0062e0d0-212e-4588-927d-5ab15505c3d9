# Generated by Django 5.1.4 on 2024-12-22 07:30

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('school', '0111_alter_staffsalaryformonth_options_staff_work_hours_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='salarypaymentoptions',
            name='rate',
            field=models.PositiveIntegerField(default=0, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='taux'),
        ),
        migrations.AlterField(
            model_name='salarypaymentoptions',
            name='option',
            field=models.CharField(choices=[('E', 'Enseignants uniquement'), ('O', 'Personnel Non-enseignant'), ('A', 'Tout le Personnel')], default='A', max_length=1, verbose_name='Appliquable à'),
        ),
    ]
