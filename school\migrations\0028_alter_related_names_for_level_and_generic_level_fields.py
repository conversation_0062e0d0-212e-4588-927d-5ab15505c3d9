# Generated by Django 4.2.4 on 2023-08-09 15:34

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('school', '0027_increase_student_phone_field_length'),
    ]

    operations = [
        migrations.AlterField(
            model_name='enrollment',
            name='generic_level_ar',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='generic_level_enrollment_ar', to='school.genericlevel', verbose_name='niveau arabe'),
        ),
        migrations.AlterField(
            model_name='enrollment',
            name='generic_level_fr',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.PROTECT, related_name='generic_level_enrollment_fr', to='school.genericlevel', verbose_name='niveau'),
        ),
        migrations.AlterField(
            model_name='enrollment',
            name='level_ar',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='enrollment_ar', to='school.level', verbose_name='classe arabe'),
        ),
    ]
