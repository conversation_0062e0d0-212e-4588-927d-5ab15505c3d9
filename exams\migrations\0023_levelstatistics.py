# Generated by Django 4.2.4 on 2023-10-10 09:13

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('school', '0033_alter_school_cycle'),
        ('exams', '0022_alter_levelsubject_level'),
    ]

    operations = [
        migrations.CreateModel(
            name='LevelStatistics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('boys_admitted', models.PositiveSmallIntegerField()),
                ('boys_perc', models.DecimalField(decimal_places=2, max_digits=5)),
                ('girls_admitted', models.PositiveSmallIntegerField()),
                ('girls_perc', models.DecimalField(decimal_places=2, max_digits=5)),
                ('min_average', models.DecimalField(decimal_places=2, max_digits=5)),
                ('max_average', models.DecimalField(decimal_places=2, max_digits=5)),
                ('level_average', models.DecimalField(decimal_places=2, max_digits=5)),
                ('level', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='school.level')),
                ('term', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='exams.schoolterm')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
