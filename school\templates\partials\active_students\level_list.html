<div class="tile" id="tile">
    <div class="row px-3 mb-3">
        <div class="form-check">
            <input type="checkbox" class="form-check-input" id="show-id" 
            onchange="
                var q_param = 'avec_identifiants';
                elements = document.body.querySelectorAll('.print-list');
                for (element of elements) {
                    link = element.href
                    if ((link.indexOf(q_param) == -1) && (this.checked)) {
                        if (link.indexOf('?') == -1) {
                            link = link + '?' + q_param
                        } else {
                            link = link + '&' + q_param
                        }
                    } else if (!this.checked) {
                        if(link.indexOf('?' + q_param) !== -1) link = link.replace('?' + q_param, '');
                        if(link.indexOf('&' + q_param) !== -1) link = link.replace('&' + q_param, '');
                    }
                    element.setAttribute('href', link)
                }
            ">
            <label class="form-check-label" for="show-id">Afficher les identifiants pour les élèves sans matricules</label>
          </div>
    </div>
    <div class="table-responsive">
        <table class="table table-striped table-sm" id="datatable">
            <thead class="bg-primary text-white">
            <tr>
                <th>Niveau</th>
                <th>Classe</th>
                <th>Eff.</th>
                <th>Imprimer Liste PDF</th>
            </tr>
            </thead>
            <tbody>
            {% for level in levels %}
            <tr>
                <td class="align-middle">{{ level.generic_level }}</td>
                <td class="align-middle">{{ level }}</td>
                <td class="align-middle">{{ level.students }}</td>
                <td class="align-middle"><a href="{% url 'school:level_list_pdf' level.id %}" 
                    class="btn btn-sm btn-secondary print-list" target="_blank">Noir</a>
                <a href="{% url 'school:level_list_pdf' level.id %}?filles_en_rouge=oui" 
                    class="btn btn-sm btn-warning print-list" target="_blank">Couleur</a></td>
            {% endfor %}
            </tbody>
        </table>
    </div>
</div>