# Generated by Django 5.0.2 on 2024-07-29 00:04

import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('school', '0083_alter_levelpricing_annexe_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Message',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('content', models.TextField(verbose_name='contenu du SMS')),
                ('sms_count', models.PositiveSmallIntegerField(default=1)),
                ('to', models.CharField(max_length=10, validators=[django.core.validators.MinLengthValidator(10)])),
                ('school', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='school.school', verbose_name='école')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='school.student', verbose_name='élève')),
                ('year', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='school.year', verbose_name='année')),
            ],
            options={
                'verbose_name': 'SMS',
                'verbose_name_plural': 'SMS',
            },
        ),
        migrations.CreateModel(
            name='MessageBalance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('balance', models.PositiveIntegerField(verbose_name='solde de SMS')),
                ('used', models.PositiveIntegerField(default=0, verbose_name='SMS utilisés')),
                ('school', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='school.school', verbose_name='école')),
            ],
            options={
                'verbose_name': 'Solde de SMS',
                'verbose_name_plural': 'Soldes de SMS',
            },
        ),
        migrations.CreateModel(
            name='MessageUpgrade',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('amount_added', models.PositiveIntegerField(verbose_name='SMS ajoutés')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='notes')),
                ('school', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='school.school', verbose_name='école')),
            ],
            options={
                'verbose_name': 'Ajout de SMS',
                'verbose_name_plural': 'Ajout de SMS',
            },
        ),
    ]
