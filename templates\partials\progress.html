<div class="tile">
    <div class='progress-wrapper bg-light border'>
        <div id='progress-bar' class='progress-bar progress-bar-striped progress-bar-animated' style="background-color: #1eb400; width: 0%;">&nbsp;</div>
    </div>
    <div id="progress-bar-message" class="mt-3">Calculs en cours...</div>

</div>


<script>
    $(document).ready(function() { 
        var progressUrl = "{% url 'celery_progress:task_status' task_id %}";
        CeleryProgressBar.initProgressBar(progressUrl, {
            defaultMessages: {
                "waiting": "Chargement..."
            },
            onSuccess: function(...args) {
                this.onSuccessDefault(...args);
                var successMessage = 'Moyennes actualisées avec succès';
                {% if success_message %}
                successMessage = '{{ success_message }}';
                {% endif %}
                
                Swal.fire({
                    title: 'Fait',
                    text: successMessage,
                    icon: 'success',
                });
                $('#submit-btn').prop('disabled', false);
                // location.reload()
                },
            onProgress: onProgressFunc
        });
    })

    function onProgressFunc(progressBarElement, progressBarMessageElement, progress){
        progressBarElement.style.backgroundColor = this.barColors.progress;
        progressBarElement.style.width = progress.percent + "%";
        var description = progress.description || "";
        if (progress.current == 0) {
            if (progress.pending === true) {
                progressBarMessageElement.textContent = this.messages.waiting;
            } else {
                progressBarMessageElement.textContent = this.messages.started;
            }
        } else {
            progressBarMessageElement.textContent = progress.current + ' sur ' +
                progress.total + ' élément(s) traités. ' + description;
        }
    }
</script>
