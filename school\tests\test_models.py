from datetime import date
from django.contrib.auth import get_user_model
from django.test import TestCase
from model_bakery import baker
from .. import models
from main import utils

class YearModelTests(TestCase):
    def setUp(self):
        self.year = baker.make(models.Year, full_name='2022-2023')

    def test_str_method(self):
        self.assertEqual(f'{self.year}', self.year.full_name)


class FounderModelTests(TestCase):
    def setUp(self):
        self.founder = baker.make(models.Founder, name='Hello')

    def test_str_method(self):
        expected = f'{self.founder.name} ({self.founder.phone})'
        self.assertEqual(f'{self.founder}', f'{expected}')


class LocationModelTests(TestCase):
    def setUp(self):
        self.location = baker.make(models.Location)

    def test_str_method(self):
        expected = self.location.name
        self.assertEqual(f'{self.location}', f'{expected}')


class SchoolModelTests(TestCase):
    def setUp(self):
        self.school = baker.make(models.School)

    def test_str_method(self):
        expected = self.school.name
        self.assertEqual(f'{self.school}', f'{expected}')


class GenericLevelModelTests(TestCase):
    def setUp(self):
        self.generic_level = baker.make(models.GenericLevel)

    def test_str_method(self):
        expected = self.generic_level.short_name
        self.assertEqual(f'{self.generic_level}', f'{expected}')


class LevelModelTests(TestCase):
    def setUp(self):
        self.generic_level = baker.make(models.GenericLevel, 
                             name='CP1', short_name='CP1')
        self.level = baker.make(models.Level, number='CP1 A', 
                     generic_level=self.generic_level)
        
        self.generic_seconday = baker.make(models.GenericLevel, 
                                name='6eme', short_name='6eme')
        self.level_secondary = baker.make(models.Level, 
                              generic_level=self.generic_seconday, 
                              number='6eme 1')
        
    def test_get_name_method(self):
        expected = 'CP1 A'
        self.assertEqual(f'{self.level.get_name()}', f'{expected}')

    def test_str_method(self):
        self.assertEqual(f'{self.level}', 'CP1 A')
    
    def test_get_name_method_secondary(self):
        expected = '6eme 1'
        self.assertEqual(f'{self.level_secondary.get_name()}', f'{expected}')


class StudentModelTests(TestCase):
    def setUp(self):
        year = baker.make(models.Year, active=True)
        self.student = baker.make(models.Student, 
                       last_name='SABA', first_name='ABDOULAYE', 
                       birth_year=1998, birth_month=11, birth_day=10)
        
    def test_get_full_name_with_no_arg(self):
        expected = f'{self.student.last_name} {self.student.first_name}'
        self.assertEqual(f'{self.student.get_full_name()}', expected)

    def test_get_full_name_with_fr_arg(self):
        expected = f'{self.student.last_name} {self.student.first_name}'
        self.assertEqual(
            f'{self.student.get_full_name(utils.EDUCATION_FRENCH)}', 
            expected)
        
    def test_get_full_name_with_ar_arg(self):
        expected = f'{self.student.full_name_ar}'
        self.assertEqual(
            f'{self.student.get_full_name(utils.EDUCATION_ARABIC)}', 
            expected)
        
    def test_str_method(self):
        expected = f'{self.student.last_name} {self.student.first_name}'
        return f'{self.student} {expected}'
    
    def test_birth_date_method(self):
        self.assertEqual(self.student.birth_date(), date(1998, 11, 10))

    def test_birth_date_str_method(self):
        self.assertEqual(self.student.birth_date_str(), '10/11/1998')


class EnrollmentModelTests(TestCase):
    def setUp(self):
        self.year = baker.make(models.Year, active=True, name='2023')
        self.student = baker.make(models.Student, last_name='SABA', 
                       first_name='ABDOULAYE')
        self.school = baker.make(models.School)
        self.user = baker.make(get_user_model(), school=self.school)
        self.enrollment = baker.make(
            models.Enrollment, student=self.student, 
            year=self.year, school=self.school, agent=self.user)

    def test_str_method(self):
        self.assertEqual(f'{self.enrollment}', f'{self.student}')

    def test_payments_total_method(self):
        self.client.force_login(self.user)
        enrollment = baker.make(models.Enrollment, school=self.school)
        payment1 = baker.make(models.Payment, enrollment=enrollment, 
                   amount=5000)
        payment2 = baker.make(models.Payment, enrollment=enrollment, 
                   amount=1000)
        result = models.Enrollment.objects.payments_total(
            self.user, enrollment.year
        )
        self.assertEqual(result, 6000)

    def test_for_user_method(self):
        self.client.force_login(self.user)
        enrollment2 = baker.make(models.Enrollment, school=self.school, 
                      year=self.year)
        enrollment3 = baker.make(models.Enrollment, year=self.year)
        queryset = models.Enrollment.objects.for_user(
                   self.user, year=self.year)
        self.assertEqual(queryset.count(), 2)
        self.assertIn(enrollment2, queryset)


class PaymentModelTests(TestCase):
    def setUp(self):
        self.year = baker.make(models.Year, name='2023', active=True)
        self.payment = baker.make(models.Payment)
        self.user = baker.make(get_user_model())
        self.school = baker.make(models.School)

    def test_str_method(self):
        expected = f'{self.payment.enrollment} -> '  + \
        f'{self.payment.enrollment.year} -> {self.payment.amount}'
        self.assertEqual(f'{self.payment}', f'{expected}')

    def test_for_user_method(self):
        self.client.force_login(self.user)
        payments = baker.make(models.Payment, 2)
        enrollment = baker.make(models.Enrollment, 
                     school=self.school, year=self.year, agent=self.user)
        payment = baker.make(models.Payment, enrollment=enrollment, 
                  amount=10000)
        queryset = models.Payment.objects.for_user(self.year)
        self.assertEqual(queryset.count(), 1)
        self.assertIn(payment, queryset)

class HolidayModelTests(TestCase):
    def setUp(self):
        self.holiday = baker.make(models.Holiday)
    
    def test_str_method(self):
        self.assertEqual(f'{self.holiday}', self.holiday.get_name_display())


class PricingModelTests(TestCase):
    def setUp(self):
        self.year = baker.make(models.Year, name='2023')
        self.school = baker.make(models.School)
        self.user = baker.make(get_user_model(), school=self.school)
        self.pricing1 = baker.make(models.LevelPricing, 
                        school=self.school, year=self.year)
        self.pricing2 = baker.make(models.LevelPricing, 
                        school=self.school, year=self.year, 
                        education=utils.EDUCATION_ARABIC)
        self.pricing3 = baker.make(models.LevelPricing, year=self.year)

    def test_str_method(self):
        year = str(self.year)
        self.assertEqual(
            str(self.pricing1), 
            f'{year} - {self.pricing1.generic_level}')
    
    def test_for_school_method(self):
        # With no education
        queryset = models.LevelPricing.objects.for_school(
            self.user, self.year)
        self.assertEqual(queryset.count(), 2)

        # With education
        queryset = models.LevelPricing.objects.for_school(
            self.user, self.year, utils.EDUCATION_ARABIC)
        self.assertEqual(queryset.count(), 1)


class PricingCategoryModelTests(TestCase):
    def setUp(self):
        self.year = baker.make(models.Year, name='2023')
        self.school = baker.make(models.School)
        self.user = baker.make(get_user_model(), school=self.school)
        self.category1 = baker.make(models.PriceCategory, 
                        school=self.school, year=self.year)
        self.category2 = baker.make(models.PriceCategory, 
                        school=self.school, year=self.year)
        self.category3 = baker.make(models.PriceCategory, year=self.year)

    def test_str_method(self):
        year = str(self.year)
        self.assertEqual(
            str(self.category1), 
            f'{self.category1.label}')
    
    def test_for_school_method(self):
        queryset = models.PriceCategory.objects.for_school(
            self.user, self.year)
        self.assertEqual(queryset.count(), 2)


class ExtraPriceModelTests(TestCase):
    def setUp(self):
        year = baker.make(models.Year)
        self.pricing = baker.make(models.LevelPricing)
        self.extra_price1 = baker.make(models.LevelExtraPrice, pricing=self.pricing)
        extra_price2 = baker.make(models.LevelExtraPrice, pricing=self.pricing)

    def test_str_method(self):
        str_method = f"Frais annexe -> {self.pricing} -> {self.extra_price1.price}"
        self.assertEqual(str_method, str(self.extra_price1))
