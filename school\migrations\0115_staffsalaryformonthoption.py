# Generated by Django 5.1.4 on 2024-12-22 09:39

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('school', '0114_alter_salarypaymentoptions_amount_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='StaffSalaryForMonthOption',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.PositiveIntegerField(blank=True, null=True, verbose_name='montant')),
                ('rate', models.PositiveIntegerField(blank=True, null=True, verbose_name='taux')),
                ('option', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='school.salarypaymentoptions')),
                ('salary', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='school.staffsalaryformonth')),
            ],
        ),
    ]
