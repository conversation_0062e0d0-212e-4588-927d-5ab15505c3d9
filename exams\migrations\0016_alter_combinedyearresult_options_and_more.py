# Generated by Django 4.2.4 on 2023-09-17 12:34

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('school', '0031_school_address_alter_school_director_ar'),
        ('exams', '0015_combinedyearresult_educationyearresult_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='combinedyearresult',
            options={'verbose_name': 'Résulat Général Annuel'},
        ),
        migrations.AlterModelOptions(
            name='educationyearresult',
            options={'verbose_name': 'Résultat annuel'},
        ),
        migrations.AlterField(
            model_name='combinedyearresult',
            name='average_with_coef',
            field=models.DecimalField(blank=True, decimal_places=2, default=0, max_digits=5, null=True, verbose_name='moyenne coefficientée'),
        ),
        migrations.AlterField(
            model_name='combinedyearresult',
            name='enrollment',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='school.enrollment'),
        ),
        migrations.AlterField(
            model_name='educationyearresult',
            name='average_with_coef',
            field=models.DecimalField(blank=True, decimal_places=2, default=0, max_digits=5, null=True, verbose_name='moyenne coefficientée'),
        ),
        migrations.AlterUniqueTogether(
            name='termresult',
            unique_together={('school_term', 'enrollment')},
        ),
    ]
