<div class="tile" id="tile" hx-get="{% url 'school:teachers' %}?lang={{lang}}" hx-trigger="saved from:body" hx-target="#app-content">
    <div class="tile-title-w-btn">
        <div class="btn-group">
            <a class="btn btn-success" href="" 
               hx-get="{% url 'school:teacher_add' %}?lang={{ lang }}" 
               hx-target="#dialog">+ Ajouter un enseignant</a>
        </div>
    </div>
    <div class="table-responsive">
        <table class="table table-striped table-sm" id="datatable">
            <thead class="bg-primary text-white">
            <tr>
                <th style="min-width: 100px;">Nom et Prénoms</th>
                <th style="min-width: 100px;">Classes tenues</th>
                <th>Compte {{ APP_NAME }}?</th>
                <th>Nom d'utilisateur</th>
                <th>Mot de passe</th>
                <th>Actions</th>
            </tr>
            </thead>
            <tbody>
            {% for teacher in teachers %}
            <tr>
                <td class="align-middle">{{ teacher|upper }}</td>
                <td class="align-middle">
                        {% for level in teacher.get_active_year_levels %} 
                            <div class="d-flex justify-content-around">
                                <span>
                                    <a href="" hx-get="{% url 'school:teacher_level_edit' level.id %}"
                                        hx-target="#dialog">{{ level.level }} </a>
                                    <span class="show-on-pc">: 
                                        {% for subject in level.subjects.all %} 
                                        {{ subject }},
                                        {% endfor %} 
                                    </span>
                                </span>
                                <a href="" hx-get="{% url 'school:teacher_level_delete' level.id %}"
                                    hx-target="#dialog" class="badge badge-pill badge-danger align-middle">Annuler</a>
                                
                            </div> 
                        {% endfor %} 
                </td>
                <td class="align-middle" hx-target="#dialog">
                    {% if teacher.user.is_active %} 
                    <span data-feather="check-square" class="feather-16 feather-success align-middle"></span> 
                    <span class="text-success">Actif</span> 
                    {% elif not teacher.user %} 
                    <a href="" class="btn btn-success btn-sm" hx-get="{% url 'users:user_add' %}?teacher_id={{teacher.id}}">Créer compte</a>
                    {% else %}
                    <span class="text-danger">Désactivé</span>
                    {% endif %}
                </td>
                <td class="align-middle">{% if teacher.user %} {{ teacher.user.username }} {% else %} - {% endif %}</td>
                <td class="align-middle" hx-target="this">{% if teacher.user %} {{ teacher.user.get_password_chars }} 
                    <a href="" hx-get="{% url 'users:show_password' teacher.user.id %}">
                        <span data-feather="eye" class="feather-16 feather-success"></span>
                    </a> {% else %} - {% endif %}</td>
                <td class="align-middle">
                    <div class="dropdown">
                        <button class="btn btn-sm dropdown" style="background-color: inherit;" type="button" data-toggle="dropdown" aria-expanded="false">
                            <i data-feather="more-vertical" class="feather-16"></i>
                        </button>
                        <div class="dropdown-menu" hx-target="#dialog">
                            <a href="" class="dropdown-item"
                                hx-get="{% url 'school:teacher_edit' teacher.id %}">Modifier infos</a>
                            <a href="" class="dropdown-item" hx-get="{% url 'school:teacher_level_add' teacher.id %}">Attribuer classe</a>
                            {% if teacher.user %} 
                                <a href="" class="dropdown-item" hx-get="{% url 'users:change_active_status' teacher.user.id %}">Activer/Désactiver compte</a>
                                <a class="dropdown-item" href="#"
                                    hx-get="{% url 'users:reset_password' teacher.user.id %}">Réinit. Mot de passe</a>
                            {% endif %}
                        </div>
                      </div>
                </td>
            </tr>
            {% endfor %}
            </tbody>
        </table>
    </div>
</div>