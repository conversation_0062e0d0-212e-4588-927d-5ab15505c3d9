from main import utils
from exams import models
from exams.grades_utils import update_rank

# From grades_utils.py
def update_student_term_result(
        enrollment, term, subjects_coef=None,
        subjects_max_values=None, 
        use_division_factor=False):
    """ Computes a student's result for a given term """
    total = 0
    total_with_coefs = 0
    average = 0
    grades_count = 0
    coefs = 0

    grade_set = enrollment.grade_set.select_related('subject__subject')\
        .filter(school_term=term) \
        .order_by('subject__order')
    for index, item in enumerate(grade_set):
        grade = item.grade
        if grade:
            grades_count += 1
            total += grade
            coef = 0
            if not use_division_factor:
                coef =  subjects_coef[index] 
                total_with_coefs += (grade * coef) 
            else:
                coef = subjects_max_values[index]
                total_with_coefs += grade 
            coefs += coef

    if coefs == 0:
        coefs = 1

    average = float(total_with_coefs) / coefs
    return {
        'total': total, 'average': average,
        'enrollment': enrollment, 'school_term': term,
        'average_with_coef': total_with_coefs
    }


def update_student_subject_groups_averages(enrollment, term):
    """ Updates the student group average for a given term """
    update_subject_group_average(enrollment, term, utils.CATEGORY_LETTERS)
    update_subject_group_average(enrollment, term, utils.CATEGORY_SCIENCE)
    update_subject_group_average(enrollment, term, utils.CATEGORY_OTHER)

def update_subject_groups_rank(level, term):
    update_subject_group_rank(utils.CATEGORY_LETTERS, level, term)
    update_subject_group_rank(utils.CATEGORY_SCIENCE, level, term)
    update_subject_group_rank(utils.CATEGORY_OTHER, level, term)


def update_subject_group_rank(group, level, term):
    queryset = models.StudentSubjectGroupAverage.objects \
        .for_group(group, term, level)
    update_rank(queryset, 'average', models.StudentSubjectGroupAverage,
                True)


def update_subject_group_average(enrollment, term, category):
    grades_qs = enrollment.grade_set \
        .annotate(
            category=F('subject__subject__category')) \
        .filter(school_term=term)
    
    if category != utils.CATEGORY_OTHER:
        grades_qs = grades_qs.filter(category=category)
    else:
        grades_qs = grades_qs.filter(
            Q(category=category) | Q(category__isnull=True))
        
    average = 0
    coefficients = 0

    for grade in grades_qs:
        coefficient = grade.subject.coefficient
        coefficients += coefficient
        average += float(grade.grade or 0) * coefficient

    average = average / (coefficients or 1)
    if (category == utils.CATEGORY_LETTERS and enrollment.letter_result_exists) \
       or (category == utils.CATEGORY_SCIENCE and enrollment.science_result_exists) \
       or (category == utils.CATEGORY_OTHER and enrollment.other_result_exists):
        enrollment.studentsubjectgroupaverage_set.filter(
            term=term, group=category).update(average=average)
    else:
        models.StudentSubjectGroupAverage.objects.create(
            enrollment=enrollment,
            term=term, group=category,
            average=average
        )