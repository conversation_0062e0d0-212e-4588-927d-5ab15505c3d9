{% load humanize %}

<div class="tile">
    <table class="table table-responsive table-striped table-bordered h5">
        <thead style="background-color: peachpuff;">
            <tr>
                <th class="w-50">Désignation</th>
                <th  class="text-right w-50">Montant</th>
            </tr>
        </thead>
        
        <tbody>
            <tr class="text-primary">
                <th>Montant total encaissé</th>
                <th class="text-right">{{ total|intcomma }} F</th>
            </tr>
            <tr>
                <th>Montant total des dépenses</th>
                <th class="text-right">{{ expenses|intcomma }} F</th>
            </tr>
        </tbody>
        <tfoot class="{% if remaining > 0 %}text-success{% else %}text-danger{% endif %}">
            <tr>
                <th> <span data-feather="dollar-sign" class="feather-16"></span> SOLDE ACTUEL</th>
                <th class="text-right">{{ remaining|intcomma }} F</th>
            </tr>
        </tfoot>
    </table>

    {% if remaining < 0 %}
        <div class="text-danger h5">
            <span data-feather="alert-triangle" class="text-warning align-middle"></span>
           Attention, votre solde est négatif. 
           Cela signifie que les entrées ne suffisent pas 
           pour couvrir vos dépenses
        </div>
    {% endif %}
</div>

<script>
    if(typeof(feather) !== "undefined") {
        feather.replace()
    }
</script>