<div class="row" hx-get="{{ request.path }}?cycle={{cycle}}&lang={{lang}}" hx-trigger="saved from:body" hx-target="#app-content">
    <div class="col">
        <div class="tile">
            <div class="alert alert-success">
                <span data-feather="info" class="feather feather-16 align-middle"></span> Le champ code désigne le l'en-tête de colonne à utiliser pour importer les notes de la matière.
            </div>
            <div class="tile-title-w-btn">
                <div class="btn-group">
                    <a class="btn btn-success" href="" 
                       hx-get="{% url 'exams:subject_add' %}?lang={{lang}}" 
                       hx-target="#dialog">+ Ajouter une matière</a>
                </div>
            </div>
            <div class="table-responsive">
                <table class="table table-striped table-sm" id="datatable">
                    <thead class="bg-primary text-white">
                    <tr>
                        <th>LIBELLE</th>
                        <th>ABBREVIATION</th>
                        {% if lang == 'A' %}
                        <th>TRADUCTION</th>
                        {% endif %}
                        <th>CODE</th>
                    </tr>
                    </thead>
                    <tbody>
                    {% for subject in subjects %}
                    <tr>
                        <td class="align-middle">{{ subject.name }}</td>
                        <td class="align-middle">{{ subject.abbreviation }}</td>
                        {% if lang == 'A' %}
                        <td class="align-middle">{{ subject.translation|default_if_none:'' }}</td>
                        {% endif %}
                        <td class="align-middle">{{ subject.code|default_if_none:'-' }}</td>
                    </tr>
                    {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>