<div class="tile" id="tile" hx-get="{% url 'school:pricing' %}{% if education %}?education={{education}}{% endif %}" hx-trigger="saved from:body" hx-target="#app-content">
    <div class="tile-title-w-btn d-flex justify-content-around">
        <div class="btn-group">
            <a class="btn btn-success" href="" 
               hx-get="{% url 'school:pricing_add' %}{% if education %}?education={{education}}{% endif %}" 
               hx-target="#dialog">+ Par Niveau</a>
        </div>
        <div class="btn-group" role="group">
            <button id="btnGroupDrop1" type="button" class="btn btn-secondary dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
            Par cycle
            </button>
            <div class="dropdown-menu" aria-labelledby="btnGroupDrop1">
            <a class="dropdown-item" href="#" 
                hx-get="{% url 'school:cycle_pricing_add' %}?cycle=P{% if education %}&education={{education}}{% endif %}" 
                hx-target="#dialog">Primaire (Matlle -> CM2)</a>
            {% if not user.school.cycle == 'P' %}
            <a class="dropdown-item" href="#"
            hx-get="{% url 'school:cycle_pricing_add' %}?cycle=1er{% if education %}&education={{education}}{% endif %}" 
                hx-target="#dialog">1er Cycle (6ème -> 3ème)</a>
            <a class="dropdown-item" href="#"
            hx-get="{% url 'school:cycle_pricing_add' %}?cycle=2nd{% if education %}&education={{education}}{% endif %}" 
                hx-target="#dialog">2nd Cycle (2nde -> Tle)</a>
                {% endif %}
            </div>
        </div>
    </div>
    <div class="table-responsive">
        <table class="table table-striped table-sm" id="datatable">
            <thead class="bg-primary text-white">
            <tr>
                <th>Niveau</th>
                <th>Statut</th>
                <th>Inscription</th>
                <th>Scolarité</th>
                <th>Annexe</th>
                <th>Actions</th>
            </tr>
            </thead>
            <tbody>
            {% for pricing in pricing_list %}
            <tr>
                <td class="align-middle">{{ pricing.generic_level }}</td>
                <td class="align-middle">{% if pricing.student_status %} {{ pricing.get_student_status_display }} {% endif %}</td>
                <td class="align-middle">{{ pricing.inscription }}</td>
                <td class="align-middle">{{ pricing.scolarite }}</td>
                <td class="align-middle">{{ pricing.annexe }}</td>
                <td class="align-middle">
                    <a href="" class="btn btn-sm btn-warning"
                        hx-get="{% url 'school:pricing_edit' pricing.id %}"
                          hx-target="#dialog">
                        <span data-feather="edit" class="feather-16"></span>
                    </a>
                </td>
            </tr>
            {% endfor %}
            </tbody>
        </table>
    </div>
</div>