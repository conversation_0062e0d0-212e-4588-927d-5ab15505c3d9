{% csrf_token %}
{% if term %}
<select name="selected-term" id="selected-term" class="d-none">
    <option value="{{ term }}">Période</option>
</select>
{% endif %}

<table class="table table-striped table-bordered table-sm table-hover" id="table">
    <thead class="bg-primary text-white">
    <tr>
        <th><input type="checkbox" name="select-all" id="select-all" onclick="checkAll(this)"></th>
        <th>MATRICULE</th>
        <th>NOM ET PRENOMS</th>
        <th>MOY.</th>
        <th>RANG</th>
        <th>BULL.</th>
    </tr>
    </thead>
    <tbody>
    {% for result in queryset %}
    <tr>
        <td>
            <input type="checkbox" name="{{ result.enrollment.id }}" id="{{ enrollment.id }}" onclick="validateForm()">
        </td> 
        <td class="align-middle">{{ result.enrollment.student.student_id|default_if_none:'' }}</td>
        <td class="align-middle">{{ result.enrollment }}</td>
        <td class="align-middle">{{ result.average }}</td>
        <td class="align-middle">{{ result.rank }}</td>
        <td class="align-middle">
            <a class="btn btn-sm btn-warning" href="{% url 'exams:student_report' %}?eleve={{ result.enrollment.id }}&periode={{ term }}">
                <span data-feather="file-text" class="feather-16"></span>
            </a>
            <a class="btn btn-sm btn-info" href="{% url 'exams:student_report' %}?eleve={{ result.enrollment.id }}&periode={{ term }}&report_type=RW">
                <span data-feather="file-text" class="feather-16"></span>
            </a>
        </td>
    </tr>
    {% endfor %}
    </tbody>
</table>

<script>
    function checkAll(checkbox) {
      var checkboxes = document.getElementsByTagName('input');
      for (var i = 0; i < checkboxes.length; i++) {
        if (checkboxes[i].type === 'checkbox') {
          checkboxes[i].checked = checkbox.checked;
        }
      }

      validateForm()
    }

    function validateForm(event) {
            console.log('Validating');
    // Get all checkboxes within the form
            var checkboxes = document.querySelectorAll('input[type="checkbox"]');

            // Check if any of the checkboxes are checked
            var isChecked = Array.from(checkboxes).some(function(checkbox) {
                return checkbox.checked;
            });

            // If none of the checkboxes are checked, prevent form submission
            if (!isChecked) {
                if (!document.querySelector('#submit-btn').classList.contains('disabled')) {
                    document.querySelector('#submit-btn').classList.add('disabled')
                };
                document.querySelector('#submit-btn').style.pointerEvents = "none"
                return false
            }

            // If at least one checkbox is checked, allow the form to be submitted
            if (document.querySelector('#submit-btn').classList.contains('disabled')) {
                    document.querySelector('#submit-btn').classList.remove('disabled')
            };
            document.querySelector('#submit-btn').style.pointerEvents = "auto";


    }

    $.fn.dataTable.ext.errMode = 'none';
		$('#table').DataTable({
		// dom: 'Bfrtip',
		lengthMenu: [
			[25, 50, 100, 200],
			[ '25','50', '100', '200']
        ],
        drawCallback: function() {
            htmx.process(document.body.querySelector('#table'))
            feather.replace();
        }
    })

    htmx.process(document.body)
    feather.replace();
</script>