{% extends 'partials/modal.html' %}
{% load widget_tweaks %}

{% block modal_title %} Classe {% endblock %}

{% block modal_body %}
    <div class="form-row">
        {% for field in form %}
        <div class="form-group col-md-6">
            <label for="{{ field.id_for_label }}">{{ field.label }}</label>
            {% render_field field class='form-control' %}
            {% if field.name == 'number' %}
            <p class="form-text text-muted mb-0">
                correspond au nom de la classe. Exemple: 6eme, 6eme 2 etc.
            </p>
            <span class="invalid-feedback">{{ field.errors|first }}</span>
            {% endif %}
        </div>
        {% endfor %}
    </div>
    {% if form.errors %}
    <div class="invalid-feedback">
        {{ form.errors}}
    </div>
    {% endif %}
{% endblock %}

{% block modal_footer %} 
    <button type="submit" class="btn btn-success">
        <span class="feather-16 align-middle" data-feather="check-circle"></span>
        Enregistrer
    </button>
{% endblock %}