{% csrf_token %}
<table class="table table-striped table-bordered table-sm table-hover" id="table">
    <thead class="bg-primary text-white">
    <tr>
        <th rowspan="2" class="text-center" style="vertical-align: middle"><input type="checkbox" name="select-all" id="select-all" onclick="checkAll(this)"></th>
        <th rowspan="2" class="text-center" style="vertical-align: middle">MATRICULE</th>
        <th rowspan="2" class="text-center" style="vertical-align: middle">NOM ET PRENOMS</th>
        <th rowspan="2" class="text-center" style="vertical-align: middle">SEXE</th>
        <th colspan="2" class="text-center" style="vertical-align: middle">ARABE</th>
        <th colspan="2" class="text-center" style="vertical-align: middle">FRANCAIS</th>
        <th colspan="2" class="text-center" style="vertical-align: middle">RESULTAT GENERAL</th>
    </tr>
    <tr>
        <th>Moy.</th>
        <th>Rang</th>
        <th>Moy.</th>
        <th>Rang</th>
        <th>Moy.</th>
        <th>Rang</th>
    </tr>
    </thead>
    <tbody>
    {% for enrollment in object_list %}
    <tr>
        <td>
            <input type="checkbox" name="{{ enrollment.id }}" id="{{ enrollment.id }}" onclick="validateForm()">
        </td>
        <td class="align-middle">{{ enrollment.student.student_id|default_if_none:'' }}</td>
        <td class="align-middle">{{ enrollment }}</td>
        <td class="align-middle">{{ enrollment.gender }}</td>
        <td class="align-middle">{{ enrollment.average_ar|default:'-' }}</td>
        <td class="align-middle">{{ enrollment.rank_ar|default:'-' }}</td>
        <td class="align-middle">{{ enrollment.average_fr|default:'-' }}</td>
        <td class="align-middle">{{ enrollment.rank_fr|default:'-' }}</td>
        <td class="align-middle">{{ enrollment.general_average|default:'-' }}</td>
        <td class="align-middle">{{ enrollment.rank_mga|default:'-' }}</td>
    </tr>
    {% endfor %}
    </tbody>
</table>

<script>
    function checkAll(checkbox) {
      var checkboxes = document.getElementsByTagName('input');
      for (var i = 0; i < checkboxes.length; i++) {
        if (checkboxes[i].type === 'checkbox') {
          checkboxes[i].checked = checkbox.checked;
        }
      }

      validateForm()
    }

    function validateForm(event) {
            console.log('Validating');
    // Get all checkboxes within the form
            var checkboxes = document.querySelectorAll('input[type="checkbox"]');

            // Check if any of the checkboxes are checked
            var isChecked = Array.from(checkboxes).some(function(checkbox) {
                return checkbox.checked;
            });

            // If none of the checkboxes are checked, prevent form submission
            if (!isChecked) {
                if (!document.querySelector('#submit-btn').classList.contains('disabled')) {
                    document.querySelector('#submit-btn').classList.add('disabled')
                };
                document.querySelector('#submit-btn').style.pointerEvents = "none"
                return false
            }

            // If at least one checkbox is checked, allow the form to be submitted
            if (document.querySelector('#submit-btn').classList.contains('disabled')) {
                    document.querySelector('#submit-btn').classList.remove('disabled')
            };
            document.querySelector('#submit-btn').style.pointerEvents = "auto";


    }

    $.fn.dataTable.ext.errMode = 'none';
		$('#table').DataTable({
		// dom: 'Bfrtip',
		lengthMenu: [
			[25, 50, 100, 200],
			[ '25','50', '100', '200']
        ],
        drawCallback: function() {
            htmx.process(document.body.querySelector('#table'))
            feather.replace();
        }
    })

    htmx.process(document.body)
    feather.replace();
</script>