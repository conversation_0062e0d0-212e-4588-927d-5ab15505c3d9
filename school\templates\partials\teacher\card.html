{% load humanize %}
<div class="col-lg-3 col-6 my-1">
    <a href="" hx-get="{% url 'school:teacher_subjects_select' %}?classe={{ level.level.id }}" 
        hx-push-url="{% url 'school:teacher_subjects_select' %}?classe={{ level.level.id }}"
        hx-target="#app-content" class="card" 
        style="display: block;">
      <div class="card-body p-2 bg-light">
        <div class="d-flex">
          <div class="">
            <div class="mb-2 bg-{{color}} rounded p-2">
              <i data-feather="{{ icon|default:'folder' }}" class="text-white feather-16"></i>
            </div>
          </div>
          <div class="col-md-10 col-lg-12 col-xl-12 col-xxl-7">
            <h6 class="font-semibold">
              {{ level.level }}
            </h6>
            <h6 class="font-extrabold mb-0">{% if value %} {{ value|intcomma }} {% else %} 0 {% endif %}</h6>
          </div>
        </div>
      </div>
    </a>
  </div>