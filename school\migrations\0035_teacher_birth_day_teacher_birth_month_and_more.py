# Generated by Django 4.2.4 on 2023-10-20 12:49

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('school', '0034_teacher_teacherlevel'),
    ]

    operations = [
        migrations.AddField(
            model_name='teacher',
            name='birth_day',
            field=models.PositiveSmallIntegerField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(31)], verbose_name='né le'),
        ),
        migrations.AddField(
            model_name='teacher',
            name='birth_month',
            field=models.PositiveSmallIntegerField(blank=True, choices=[(1, 'Janvier'), (2, 'Février'), (3, 'Mars'), (4, 'Avril'), (5, 'Mai'), (6, 'Juin'), (7, '<PERSON><PERSON><PERSON>'), (8, 'Août'), (9, 'Septembre'), (10, 'Octobre'), (11, 'Novembre'), (12, 'Décembre')], null=True, verbose_name='mois'),
        ),
        migrations.AddField(
            model_name='teacher',
            name='birth_place',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='lieu de naissance'),
        ),
        migrations.AddField(
            model_name='teacher',
            name='birth_year',
            field=models.PositiveSmallIntegerField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(1920)], verbose_name='année'),
        ),
        migrations.AddField(
            model_name='teacher',
            name='gender',
            field=models.CharField(choices=[('M', 'Masculin'), ('F', 'Féminin')], default=1, max_length=1, verbose_name='sexe'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='teacher',
            name='id_number',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='N° CNI/Att'),
        ),
        migrations.AddField(
            model_name='teacher',
            name='phone',
            field=models.CharField(blank=True, max_length=21, null=True, verbose_name='contacts'),
        ),
        migrations.AlterField(
            model_name='teacher',
            name='first_name',
            field=models.CharField(max_length=255, verbose_name='prénoms'),
        ),
        migrations.AlterField(
            model_name='teacher',
            name='last_name',
            field=models.CharField(max_length=255, verbose_name='nom'),
        ),
    ]
