{% load humanize %}
<div class="tile" id="tile" hx-get="{% url 'school:staff_badges' %}" hx-trigger="saved from:body" hx-target="#app-content">
    <h5 class="row px-3 mb-3">DEMANDES DE CARTES PROFESSIONNELLES</h5>
    <div class="tile-title-w-btn">
        <div class="btn-group">
            <a class="btn btn-success" href="" 
               hx-get="{% url 'school:staff_card_create' %}" 
               hx-target="#dialog-xl">+ Nouvelle demande</a>
        </div>
    </div>
    <div class="table-responsive">
        <table class="table table-striped table-sm" id="datatable">
            <thead class="bg-primary text-white">
            <tr>
                <th>Photo</th>
                <th>Nom</th>
                <th>Prénoms</th>
                <th>Sexe</th>
                <th>Né(e) le</th>
                <th>Emploi</th>
                <th>Autorisation</th>
                <th>Statut Carte</th>
                <th>Actions</th>
            </tr>
            </thead>
            <tbody>
                {% for card in card_list %}
                <tr>
                    <td class="align-middle text-center p-0" style="min-width: 60px;" hx-get="{% url 'school:staff_card_edit' card.pk %}"
                    hx-target="#dialog">
                        {% if card.photo %}
                          <img data-original="{{ card.photo.url }}" 
                              alt="Photo de l'élève" 
                              class="lazy border img-thumbnail rounded-circle">
                        {% else %}
                        <img data-original="{{ card.blank_photo }}" 
                              alt="Photo de l'élève" 
                              class="lazy border img-thumbnail rounded-circle">
                        {% endif %}
                      </td>
                    <td class="align-middle">{{ card.last_name }}</td>
                    <td class="align-middle">{{ card.first_name }}</td>
                    <td class="align-middle">{{ card.get_gender_display }}</td>
                    <td class="align-middle">{{ card.birth_date_str }}</td>
                    <td class="align-middle">{{ card.job }}</td>
                    <td class="align-middle">{{ card.authorization_number }}</td>
                    <td class="align-middle">
                        {% if card.status == 'P' %}
                        <span class="badge badge-warning">{{ card.get_status_display }}</span>
                        {% elif card.status == 'M' %}
                        <span class="badge badge-info">{{ card.get_status_display }}</span>
                        {% else %}
                        <span class="badge badge-success">{{ card.get_status_display }}</span>
                        {% endif %}
                        
                    </td>
                    <td class="align-middle">
                        <a href="" hx-get="{% url 'school:staff_card_edit' card.pk %}" 
                            hx-target="#dialog-xl"
                            class="btn btn-sm btn-primary">
                            <span data-feather="edit" class="feather-16"></span>
                        </a>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

<script>
    document.addEventListener("DOMContentLoaded", function(ev) {
        feather.replace();
        $("img.lazy").lazyload();
    })

</script>