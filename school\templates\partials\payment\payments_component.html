{% load widget_tweaks %}
<section class="{{ width|default:'' }}">
    <h5 class=""><span data-feather="dollar-sign" class="feather-16"></span> Liste des Paiements</h5>
    <table class="table table-bordered table-sm table-striped">
        <thead>
            <tr>
                <th style="width: 22%;">Inscription</th>
                <th style="width: 22%;">Scolarité</th>
                <th style="width: 22%;">Annexe</th>
                <th style="width: 34%;">Date</th>
                {% if not hide_agent %}
                <th class="show-on-pc border-none">Par</th>
                {% endif %}
            </tr>
        </thead>
        <tbody>
            {% if enrollment.payment_set.exists %}
                {% for payment in payments %}
                <tr>
                    <td><input type="number" class="form-control form-control-sm payment_inscription payment row-{{ forloop.counter }}" name="inscription_{{ payment.pk}}" id="payment_{{ payment.pk}}" min="0" value="{{ payment.inscription }}" 
                        oninput="updateTotalPayment('.payment_inscription', '#total_inscription'); updateBigTotal()"></td>
                    <td><input type="number" class="form-control form-control-sm payment_amount payment row-{{ forloop.counter }}" name="amount_{{ payment.pk}}" id="payment_{{ payment.pk}}" min="0" value="{{ payment.amount }}"
                        oninput="updateTotalPayment('.payment_amount', '#total_amount'); updateBigTotal()"></td>
                    <td><input type="number" class="form-control form-control-sm payment_annexe payment row-{{ forloop.counter }}" name="annexe_{{ payment.pk}}" id="payment_{{ payment.pk}}" min="0" value="{{ payment.annexe }}"
                        oninput="updateTotalPayment('.payment_annexe', '#total_annexe'); updateBigTotal()"></td>
                    <td>
                        <input type="text" class="form-control form-control-sm payment_date row-{{ forloop.counter }}" name="date_{{ payment.pk}}" id="payment_{{ payment.pk}}" value="{{ payment.date_str }}">
                    </td>
                    {% if not hide_agent %}
                    <td class="show-on-pc">{{ request.user.get_full_name }}</td>
                    {% endif %}
                    <!-- <td style="text-wrap: nowrap;">
                        <a href="" class="btn btn-sm btn-light border-secondary" 
                            data-toggle="tooltip" title="Modifier le paiement"
                            onclick="event.preventDefault(); $('.row-{{ forloop.counter }}').attr('readOnly', false)"><span data-feather="edit" class="feather-11"></span></a>
                    </td> -->
                </tr>
                {% endfor %}
            {% endif %}
                
                <tr class="{% if enrollment.get_fees_total > 0 and not enrollment.get_payments_total < enrollment.get_fees_total %} d-none {% endif %}" data-toggle="tooltip" title="Ajouter un nouveau paiement" id="new_payment" style="background-color: rgb(181, 240, 181);">
                    <td>{% render_field form.enrollment_fee1 class='form-control form-control-sm payment payment_inscription' min='0' value='0' oninput='updateInscription()' %}</td>
                    <td>{% render_field form.year_fee1 class='form-control form-control-sm payment payment_amount' min='0' value='0' oninput='updateAmount()' %}</td>
                    <td>{% render_field form.annexe_fee1 class='form-control form-control-sm payment payment_annexe' min='0' value='0' oninput='updateAnnexe()' %}</td>
                    <td>{% render_field form.date1 class='form-control form-control-sm payment_date' %}</td>
                    {% if not hide_agent %}
                    <td class="show-on-pc">{{ request.user.get_full_name }}</td>
                    {% endif %}
                </tr>
                
                {% if form.errors %}
                <div class="invalid-feedback">{{ form.errors.0 }}</div>
                {% endif %}
        </tbody>
        <tfoot class="{% if not enrollment.payment_set.exists %} d-none {% endif %}">
            <tr class="px-2 bg-white">
                <td id="total_inscription">0 F</td>
                <td id="total_amount">0 F </td>
                <td id="total_annexe">0 F</td>
                {% if with_agent %}
                <td></td>
                {% endif %}
                <td id="big_total"></td>
            </tr>
        </tfoot>
    </table>
    <div class="form-group col-12">
        <input type="checkbox" name="sms_check" id="sms_check" 
        class="form-check" style="display: inline;" checked="checked"
        {% if not sms_balance or sms_balance == 0 %} disabled="disabled" {% endif %}> 
        <label for="sms_check">Notification sms après un nouveau paiement</label>
    </div>
    <div class="p-2 rounded {% if sms_balance and sms_balance > 0 %} alert-success {% else %} alert-secondary {% endif %}">
        Solde SMS: {{ sms_balance|default:'0' }}
    </div>
</section>

<script>
    flatpickr("[name*='date']", {
        dateFormat: "d/m/Y",
        enableTime: false,
        allowInput: true,
        timeZone: "Africa/Abidjan",
        disableMobile: "true",
        locale: "fr",
        maxDate: 'today'
    });

    function updateTotalPayment(className, targetElement) {
        const paymentInputs = document.querySelectorAll(className);
        var total = 0;
        paymentInputs.forEach(input => {
            total += parseFloat(input.value);
        });
        document.body.querySelector(targetElement).textContent = total;
    }
    
    function updateInscription() {
        updateTotalPayment('.payment_inscription', '#total_inscription');
        updateBigTotal();
    }
    function updateAmount() {
        updateTotalPayment('.payment_amount', '#total_amount');
        updateBigTotal();
    }
    function updateAnnexe() {
        updateTotalPayment('.payment_annexe', '#total_annexe');
        updateBigTotal();
    }
    function updateBigTotal() {
        updateTotalPayment('.payment', '#big_total');
    }

    updateInscription();
    updateAmount();
    updateAnnexe();
    updateBigTotal();
</script>