# Generated by Django 4.2.3 on 2023-07-17 09:07

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('school', '0007_alter_enrollment_qualite_alter_enrollment_status'),
    ]

    operations = [
        migrations.CreateModel(
            name='PriceCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('label', models.CharField(max_length=255, verbose_name='rubrique')),
                ('school', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='school.school')),
                ('year', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='school.year')),
            ],
            options={
                'verbose_name': 'Rubrique',
                'unique_together': {('label', 'school', 'year')},
            },
        ),
        migrations.CreateModel(
            name='LevelPricing',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('inscription', models.PositiveSmallIntegerField(default=0, verbose_name="frais d'inscription")),
                ('scolarite', models.PositiveSmallIntegerField(default=0, verbose_name='frais de scolarité')),
                ('student_status', models.CharField(blank=True, choices=[('Aff', 'Affecté'), ('Naff', 'Non-Affecté')], default='Naff', max_length=4, null=True, verbose_name='statut Aff/Naff')),
                ('generic_level', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='school.genericlevel', verbose_name='niveau')),
                ('school', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='school.school', verbose_name='école')),
                ('year', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='school.year')),
            ],
            options={
                'verbose_name': "Frais d'écolage",
                'ordering': ['generic_level__order'],
                'unique_together': {('generic_level', 'school', 'year', 'student_status')},
            },
        ),
        migrations.CreateModel(
            name='LevelExtraPrice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('price', models.PositiveSmallIntegerField(verbose_name='montant à payer')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='school.pricecategory')),
                ('pricing', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='school.levelpricing')),
            ],
            options={
                'verbose_name': 'Frais annexe',
            },
        ),
    ]
