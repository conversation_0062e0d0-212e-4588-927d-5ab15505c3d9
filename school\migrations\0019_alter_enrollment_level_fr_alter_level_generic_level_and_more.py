# Generated by Django 4.2.3 on 2023-08-01 10:50

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('school', '0018_add_max_field_to_level'),
    ]

    operations = [
        migrations.AlterField(
            model_name='enrollment',
            name='level_fr',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='school.level', verbose_name='classe'),
        ),
        migrations.AlterField(
            model_name='level',
            name='generic_level',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='school.genericlevel', verbose_name='niveau'),
        ),
        migrations.AlterField(
            model_name='level',
            name='max',
            field=models.PositiveSmallIntegerField(default=50, verbose_name='capacité maximale (nbre délèves)'),
        ),
        migrations.AlterField(
            model_name='level',
            name='number',
            field=models.PositiveSmallIntegerField(verbose_name='classe N°'),
        ),
    ]
