from datetime import datetime
from django.db import models
from django.db.models import Q
from django.utils.translation import gettext_lazy as _
from django.contrib.auth.hashers import make_password
from django.db import transaction
from django.contrib.auth import get_user_model
import requests
from main import utils

APP_NAME = 'EcolePro'
EDUCATION_ARABIC = 'A'
EDUCATION_FRENCH = 'F'

GENDER_MALE = 'M'
GENDER_FEMALE = 'F'
GENDER_CHOICES = (
    (GENDER_MALE, 'Masculin'),
    (GENDER_FEMALE, 'Féminin'),
)

EDUCATION_CHOICES = (
    (EDUCATION_ARABIC, 'Arabe'),
    (EDUCATION_FRENCH, 'Français'),
)

MONTH_JANUARY = 1
MONTH_FEBRUARY = 2
MONTH_MARCH = 3
MONTH_APRIL = 4
MONTH_MAY = 5
MONTH_JUNE = 6
MONTH_JULY = 7
MONTH_AUGUST = 8
MONTH_SEPTEMBER = 9
MONTH_OCTOBER = 10
MONTH_NOVEMBER = 11
MONTH_DECEMBER = 12
MONTH_CHOICES = (
    (MONTH_JANUARY, 'Janvier (1)'),
    (MONTH_FEBRUARY, 'Février (2)'),
    (MONTH_MARCH, 'Mars (3)'),
    (MONTH_APRIL, 'Avril (4)'),
    (MONTH_MAY, 'Mai (5)'),
    (MONTH_JUNE, 'Juin (6)'),
    (MONTH_JULY, 'Juillet (7)'),
    (MONTH_AUGUST, 'Août (8)'),
    (MONTH_SEPTEMBER, 'Septembre (9)'),
    (MONTH_OCTOBER, 'Octobre (10)'),
    (MONTH_NOVEMBER, 'Novembre (11)'),
    (MONTH_DECEMBER, 'Décembre (12)'),
)

# User roles
ROLE_ACCOUNTANT = 'AC'
ROLE_DIRECTOR = 'DR'
ROLE_FOUNDER = 'FD'
ROLE_TEACHER = 'TC'
ROLE_COMPUTER_SCIENTIST = 'CS'

# Student types 
STATUS_AFF = 'Aff'
STATUS_NAFF = 'Naff'
STATUS_CHOICES = (
    (STATUS_AFF, 'Affecté'),
    (STATUS_NAFF, 'Non-Affecté'),
)

# Cycle
CYCLE_PRIMARY = 'P'
CYCLE_SECONDARY = 'S'
CYCLE_BOTH = 'B'
CYCLE_CHOICES = (
    (CYCLE_PRIMARY, 'Primaire'),
    (CYCLE_SECONDARY, 'Secondaire'),
    (CYCLE_BOTH, 'Les deux (Groupe Scolaire)')
)

# Payment types
TYPE_MANDATORY = 'M'
TYPE_OPTIONAL = 'O'
TYPE_CHOICES = (
    (TYPE_MANDATORY, _('Obligatoire')),
    (TYPE_OPTIONAL, _('Facultatif')),
)

# Subject categories
CATEGORY_LETTERS = 'L'
CATEGORY_SCIENCE = 'S'
CATEGORY_OTHER = 'A'
CATEGORY_RELIGION =  'R'
CATEGORY_ARABIC =  'A'

CATEGORY_CHOICES = (
    (CATEGORY_LETTERS, 'Lettres'),
    (CATEGORY_SCIENCE, 'Sciences'),
    (CATEGORY_OTHER, 'Autres'),
    (CATEGORY_RELIGION, 'Religion'),
    (CATEGORY_ARABIC, 'Arabe'),
)


ASSOCIATION_OEECI = 'OE'
ASSOCIATION_CHERIFLA = 'CH'
ASSOCIATION_LECIM = 'LE'
ASSOCIATION_CHOICES = (
    (ASSOCIATION_OEECI, 'OEECI'),
    (ASSOCIATION_CHERIFLA, 'CHERIFLA'),
    (ASSOCIATION_LECIM, 'LECIM'),
) 

TRANSLATIONS_FR_AR = {
    'n°': 'رقم',
    'nom et prénoms': 'الاسم واللقب',
    'nom': 'الاسم واللقب',
    'sexe': 'الجنس',
    'classe': 'الفصل الفرنسي',
    'classe arabe': 'الفصل العربي',
    'né(e) le': 'تاريخ الميلاد',
    'matricule': 'رقم التسجيل',
    'matiere': 'المواد الدراسية',
    'bulletin': 'كشف الدرجات',
    'age': 'عمر',
    'total': 'المجموع',
    'moyenne': 'المعدل',
    'rang': 'الترتيب',
    'mention': 'التقدير',
    'resultat': 'النتيجة',
    'effectif': 'عدد الطلاب',
    'garcons': 'ذكور',
    'filles': 'إناث',
    'presents': 'حاضرون',
    'admis': 'الناجحون',
    'pourc_admis': 'نسبة الناجحين',
}

CEPE_TRANSLATION = 'الشهــــــــــادة الابْتِدَائِيَّةُ'
BEPC_TRANSLATION = 'الشهــــــــــادة الإعدادية'
BAC_TRANSLATION = ' الشهادة الثانوية للدراسات الإسلامية  '


DEFAULT_PASSWORD = 'bonjour'

# Transformations
CLOUDINARY_TRANFORMATIONS = {
    'gravity':'faces', 'quality': 'auto',
    'crop': 'thumb', 'width': 250, 'height': 290,
    'effect':'improve:outdoor', 'zoom': 0.75
}

APPRECIATIONS_SUR_10 = {
    '1': 'Médiocre',
    '2': 'Médiocre',
    '3': 'Faible',
    '4': 'Insuffisant',
    '5': 'Passable',
    '6': 'Assez-Bien',
    '7': 'Assez-Bien',
    '8': 'Bien',
    '9': 'Très-Bien',
    '10': 'Excellent',
}

APPRECIATIONS_SUR_20 = {
    '1': 'Médiocre',
    '2': 'Médiocre',
    '3': 'Médiocre',
    '4': 'Médiocre',
    '5': 'Médiocre',
    '6': 'Faible',
    '7': 'Faible',
    '8': 'Insuffisant',
    '9': 'Insuffisant',
    '10': 'Passable',
    '11': 'Passable',
    '12': 'Assez-Bien',
    '13': 'Assez-Bien',
    '14': 'Bien',
    '15': 'Bien',
    '16': 'Très-Bien',
    '17': 'Excellent',
    '18': 'Excellent',
    '19': 'Excellent',
    '20': 'Excellent',
}
APPRECIATIONS_AR_FR_SUR_10 = {
    '1': 'Médiocre',
    '2': 'Médiocre',
    '3': 'Faible',
    '4': 'Insuffisant',
    '5': 'Passable',
    '6': 'Bien',
    '7': 'Bien',
    '8': 'Très-Bien',
    '9': 'Excellent',
    '10': 'Excellent',
}

APPRECIATIONS_AR_FR_SUR_20 = {
    '1': 'Médiocre',
    '2': 'Médiocre',
    '3': 'Médiocre',
    '4': 'Médiocre',
    '5': 'Médiocre',
    '6': 'Faible',
    '7': 'Faible',
    '8': 'Insuffisant',
    '9': 'Insuffisant',
    '10': 'Passable',
    '11': 'Passable',
    '12': 'Passable',
    '13': 'Bien',
    '14': 'Bien',
    '15': 'Bien',
    '16': 'Très-Bien',
    '17': 'Excellent',
    '18': 'Excellent',
    '19': 'Excellent',
    '20': 'Excellent',
}

APPRECIATIONS_AR_SUR_10 = {
    '1': 'سيء',
    '2': 'سيء',
    '3': 'سيء',
    '4': 'سيء',
    '5': 'مقبول',
    '6': 'جيد',
    '7': 'جيد',
    '8': 'جيد جدا',
    '9': 'ممتاز',
    '10': 'ممتاز',
}

APPRECIATIONS_AR_SUR_20 = {
    '1': 'سيء',
    '2': 'سيء',
    '3': 'سيء',
    '4': 'سيء',
    '5': 'سيء',
    '6': 'سيء',
    '7': 'سيء',
    '8': 'سيء',
    '9': 'سيء',
    '10': 'مقبول',
    '11': 'مقبول',
    '12': 'مقبول',
    '13': 'جيد',
    '14': 'جيد',
    '15': 'جيد',
    '16': 'جيد جدا',
    '17': 'جيد جدا',
    '18': 'ممتاز',
    '19': 'ممتاز',
    '20': 'ممتاز',
}

DISTINCTIONS = {
    '1': 'bad_job',
    '2': 'bad_job',
    '3': 'bad_job',
    '4': 'bad_job',
    '5': 'bad_job',
    '5': 'bad_job',
    '7': 'bad_job',
    '8': 'bad_job',
    '14': 'good_job',
    '15': 'great_job',
    '16': 'great_job',
    '17': 'great_job',
    '18': 'great_job',
    '19': 'great_job',
    '20': 'great_job',
}

RESULT_AR = {
    GENDER_MALE : {
        'admitted': 'ناجح',
        'not_admitted': 'راسب',
    },
    GENDER_FEMALE: {
        'admitted': 'ناجحة',
        'not_admitted': 'راسبة'
    }
}


EXAM_LEVELS = ['CM2', '3EME', 'TLE']

def get_result_translation(gender, admitted, return_language=EDUCATION_ARABIC):
    if return_language == EDUCATION_FRENCH:
        if admitted:
            return 'Admis' if gender == GENDER_MALE else 'Admise'
        else:
            return 'Non-Admis' if gender == GENDER_MALE else 'Non-Admise'
        
    if gender in RESULT_AR:
        text = 'not_' if not admitted else ''
        return RESULT_AR[gender][f"{text}admitted"]


class TimeStampedModel(models.Model):
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        abstract = True
    

def get_session_year_name(request):
    return request.session[f'{request.user.id}']


def get_education(request):
    education = request.GET.get('lang', EDUCATION_FRENCH)
    education = str(education)[0]
    return education


def get_level_cycle(level):
    return level.generic_level.cycle


def get_other_education(education):
    # Returns the other education
    if education == EDUCATION_FRENCH:
        return EDUCATION_ARABIC
    return EDUCATION_FRENCH

def is_second_cycle_fr(level):
    return level.education == EDUCATION_FRENCH \
        and get_level_cycle(level) == CYCLE_SECONDARY

def is_second_cycle_ar(level):
    return level.education == EDUCATION_ARABIC \
        and get_level_cycle(level) == CYCLE_SECONDARY

def is_primary_fr(level):
    return level.education == EDUCATION_FRENCH \
        and get_level_cycle(level) == CYCLE_PRIMARY

def is_primary_ar(level):
    return level.education == EDUCATION_ARABIC \
        and get_level_cycle(level) == CYCLE_PRIMARY


def filter_enrollment_by_level(level, queryset):
    """ Filters an enrollment queryset by level """
    if level.education == EDUCATION_FRENCH:
        return queryset.filter(level_fr=level)
    else:
        return queryset.filter(level_ar=level)
    

def generate_new_username(user):
    prefix = str(user.get_role_display()).upper()[0]
    return f'{prefix}{str(user.id).zfill(4)}'


def has_decimal_part(number):
    parts = str(number).split('.')
    
    if len(parts) == 2:
        decimal_part = parts[1]
        return int(decimal_part) > 0

    return False

def get_date_str():
    return datetime.today().strftime('%d/%m/%Y')


def get_rank_str(rank, is_ex=False):
    if not rank:
        return ''
    if rank == 1:
        return f"1er{' ex' if is_ex else ''}"
    return f"{rank}{'ex' if is_ex else 'è'}"

def get_distinction(average, education=EDUCATION_FRENCH, return_language=None):

    average_str = ''
    if not average:
        average_str = 0
    else:
        average_str = str(int(average)) 
    
    if (education == EDUCATION_ARABIC) and \
        (return_language == EDUCATION_ARABIC):
        return APPRECIATIONS_AR_SUR_20.get(average_str,'-')
    
    if education == EDUCATION_ARABIC:
        return APPRECIATIONS_AR_FR_SUR_20.get(average_str, '-')
    return APPRECIATIONS_SUR_20.get(average_str, '-')

def get_distinction_10(average, education=EDUCATION_FRENCH, return_language=None):
    average_str = ''
    if average:
        average_str = str(int(average)) 
    else:
        average_str = 0
    
    if education == EDUCATION_ARABIC and \
        (return_language == EDUCATION_ARABIC):
        return APPRECIATIONS_AR_SUR_10.get(average_str,'-')
    
    if education == EDUCATION_ARABIC:
        return APPRECIATIONS_AR_FR_SUR_10.get(average_str, '-')
    return APPRECIATIONS_SUR_10.get(average_str,'-')

def compute_distinction(average, max, education=EDUCATION_FRENCH, return_language=None):
    if max == 20:
        return get_distinction(average, education, return_language=return_language)
    elif max == 10:
        return get_distinction_10(average, education, return_language=return_language)
    
    if max == 0:
        max = 1

    quotient = float(average or 0) / (max / 20)
    return get_distinction(quotient, education, return_language or education)


def get_decision(average, max, short=True, level_code=None):
    if short:
        return 'A' if average and average >= (max / 2) else 'NA'
    
    if level_code and level_code == '3EME':
        return "Rédouble en cas d'échec"
    return 'Admis en classe supérieure' if average and average >= (max / 2) else 'Redouble'


def get_checked_items_list(request, prefix='check'):
    ids = []
    request_data = request.POST if request.method == 'POST' else request.GET
    for item in request_data:
        if str(item).startswith(prefix) and request_data[item] == 'on':
            item_id = str(item).split('-')[1]
            ids.append(item_id)
    return ids

def apply_subscription_level_filter(user, year_name, plan_level, queryset):
    subscription = user.get_school_subscription(
    school_id=user.school_id, year_id=year_name)
    
    if subscription and subscription.plan ==  plan_level:
        education = user.school.education
        if education == EDUCATION_FRENCH:
            queryset = queryset.filter(generic_level_fr=subscription.level)
        else:
            queryset = queryset.filter(
                Q(generic_level_fr=subscription.level) |
                Q(generic_level_ar=subscription.level)
            )
    return queryset


def get_active_nav_item(nav_items, query):
    for item in nav_items:
        if item['active_nav'] == query:
            return item


def is_exam_level(generic_level):
    return str(generic_level).upper() in EXAM_LEVELS

def create_user(user, user_password, school, year, plan=None, username=None):
    with transaction.atomic():
        enc_password = None
        if user_password:  
            user_password = str(user_password.strip())
            enc_password = make_password(user_password)
        else:
            enc_password = make_password(utils.DEFAULT_PASSWORD)
            user.custom_password = utils.DEFAULT_PASSWORD
        
        user.password = enc_password
        user.school = school

        if user.role == utils.ROLE_TEACHER:
            user.year = year

        group_name = ''
        if user.role == utils.ROLE_ACCOUNTANT:
            group_name = 'Comptable'
        elif user.role == utils.ROLE_COMPUTER_SCIENTIST:
            group_name = 'Informaticien'
        elif user.role == utils.ROLE_FOUNDER:
            group_name = 'Fondateur'
        elif user.role == utils.ROLE_DIRECTOR:
            group_name = 'Directeur'
        elif user.role == utils.ROLE_TEACHER:
            if not plan or plan == 'S':
                group_name = 'Enseignant'
            else:
                group_name = 'Enseignant Unique'

        user.save()
        if not username or get_user_model().objects.filter(username=username).exists():
            user.username = utils.generate_new_username(user)
        else:
            user.username = username
        return user, group_name
    
def infer_format(filename):
  """ Returns imported file format """
  if filename.endswith('.xlsx'):
    return 'xlsx'
  elif filename.endswith('.xls'):
    return 'xls'
  elif filename.endswith('.csv'):
      return 'csv'
  raise ValueError("Unsupported file format")


def transliterate_to_arabic(text):
    url = f'https://transliterate.qcri.org/en2ar/{text}'
    resp = requests.get(url)
    return resp.text