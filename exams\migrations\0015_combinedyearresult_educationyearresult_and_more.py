# Generated by Django 4.2.4 on 2023-09-17 07:44

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('school', '0031_school_address_alter_school_director_ar'),
        ('exams', '0014_add_custom_permission_to_grade_model'),
    ]

    operations = [
        migrations.CreateModel(
            name='CombinedYearResult',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('total', models.PositiveSmallIntegerField(default=0, verbose_name='total')),
                ('average', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='moyenne')),
                ('average_with_coef', models.DecimalField(blank=True, decimal_places=2, default=0, max_digits=5, null=True, verbose_name='moyenne coeffcientée')),
                ('rank', models.PositiveSmallIntegerField(default=0)),
                ('decision', models.CharField(blank=True, choices=[('A', 'Admis'), ('R', 'Redouble'), ('E', 'Exclu(e)')], max_length=1, null=True)),
                ('enrollment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='school.enrollment')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='EducationYearResult',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('total', models.PositiveSmallIntegerField(default=0, verbose_name='total')),
                ('average', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='moyenne')),
                ('average_with_coef', models.DecimalField(blank=True, decimal_places=2, default=0, max_digits=5, null=True, verbose_name='moyenne coeffcientée')),
                ('rank', models.PositiveSmallIntegerField(default=0)),
                ('education', models.CharField(choices=[('A', 'Arabe'), ('F', 'Français')], max_length=1)),
                ('decision', models.CharField(blank=True, choices=[('A', 'Admis'), ('R', 'Redouble'), ('E', 'Exclu(e)')], max_length=1, null=True)),
                ('enrollment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='school.enrollment')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='termresult',
            name='average_with_coef',
            field=models.DecimalField(blank=True, decimal_places=2, default=0, max_digits=5, null=True, verbose_name='moyenne coeffcientée'),
        ),
        migrations.DeleteModel(
            name='YearResult',
        ),
    ]
