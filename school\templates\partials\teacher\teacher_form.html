{% extends 'partials/modal.html' %}
{% load i18n %}
{% load widget_tweaks %}

{% block modal_title %}
Inscription
{% endblock %}

{% block modal_body %}
<div class="form-row">
    <div class="form-group col-md-6">
        <label for="{{ form.last_name.id_for_label }}">{{ form.last_name.label }}</label>
        {% render_field form.last_name class='form-control' %}
    </div>
    <div class="form-group col-md-6">
        <label for="{{ form.first_name.id_for_label }}">{{ form.first_name.label }}</label>
        {% render_field form.first_name class='form-control' %}
    </div>
    <div class="form-group col-md-4 d-none ">
        <label for="{{ form.education.id_for_label }}">{{ form.education.label }}</label>
        {% render_field form.education class='form-control' %}
    </div>
</div>
<div class="form-row">
    <div class="form-group col-4">
        <label for="{{ form.birth_day.id_for_label }}">{{ form.birth_day.label }}</label>
        {% render_field form.birth_day class='form-control' %}
    </div>
    <div class="form-group col-4">
        <label for="{{ form.birth_month.id_for_label }}">{{ form.birth_month.label }}</label>
        {% render_field form.birth_month class='form-control' %}
    </div>
    <div class="form-group col-4">
        <label for="{{ form.birth_year.id_for_label }}">{{ form.birth_year.label }}</label>
        {% render_field form.birth_year class='form-control' %}
    </div>
</div>
<div class="form-row">
    <div class="form-group col-md-4">
        <label for="{{ form.gender.id_for_label }}">{{ form.gender.label }}</label>
        {% render_field form.gender class='form-control' %}
    </div>
    <div class="form-group col-md-8 col-xl-4">
        <label for="{{ form.phone.id_for_label }}">{{ form.phone.label }}</label>
        {% render_field form.phone class='form-control' %}
    </div>
    <div class="form-group col-xl-4">
        <label for="{{ form.id_number.id_for_label }}">{{ form.id_number.label }}</label>
        {% render_field form.id_number class='form-control' %}
    </div>
</div>
{% endblock %}

{% block modal_footer %}
<input type="submit" class="btn btn-success" value="{% trans 'Valider' %}" id="submit-btn" />
{% endblock %}