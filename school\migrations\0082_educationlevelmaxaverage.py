# Generated by Django 5.0.6 on 2024-07-16 12:09

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('school', '0081_alter_school_pricing_option'),
    ]

    operations = [
        migrations.CreateModel(
            name='EducationLevelMaxAverage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('max', models.PositiveSmallIntegerField(default=20)),
                ('level', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='school.genericlevel')),
                ('school', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='school.school')),
                ('year', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='school.year')),
            ],
            options={
                'verbose_name': 'moyenne maximale par niveau',
                'verbose_name_plural': 'moyennes maximales par niveau',
            },
        ),
    ]
