{% load widget_tweaks %}
<div hx-get="{{ request.path }}?exam={{ exam|default_if_none:'cepe' }}" hx-trigger="saved from:body" hx-target="#app-content" id="students_form">
  <div class="row">
      <div class="col">
          <div class="tile">
            <div class="row px-3 d-flex justify-content-between">
                <span class="alert alert-warning">Candidats: <span class="font-weight-bold">{{ enrollments_count }}</span></span>
                <span class="alert alert-success">Dossiers ouverts: <span class="font-weight-bold">{{ enrollments_opened }}</span></span>
            </div>
            <div class="form-group row">
              <!-- Table starts here -->
              <div class="table-responsive p-3">
                  <table class="table table-striped table-sm table-bordered table-hover" style="font-size: 11.2px;" id="datatable">
                      <thead class="bg-primary text-white">
                      <tr>
                          <th class="align-middle">Photo</th>
                          <th style="min-width: 150px;" class="align-middle"> Nom et Prénoms</th>
                          <th class="text-center">Actions</th>
                          <th class="align-middle text-right"> Nom arabe</th>
                          <th style="width: auto" class="text-right align-middle">Niveau</th>
                          <th style="width: auto" class="text-right align-middle">Né(e) le</th>
                      </tr>
                      </thead>
                      <tbody>
                      {% for enrollment in enrollments %}
                      <tr>
                        <td class="align-middle text-center p-0" style="min-width: 60px;">
                            {% if enrollment.student.photo %}
                            <img data-original="{{ enrollment.student.photo.url }}" 
                                alt="Photo de l'élève" 
                                class="lazy border img-thumbnail rounded-circle">
                            {% else %}
                            <img data-original="{{ enrollment.student.blank_photo }}" 
                                    alt="Photo de l'élève" 
                                    class="lazy border img-thumbnail rounded-circle">
                            {% endif %}
                        </td>
                        <td class="align-middle" style="min-width: 150px;">
                          <a href="">
                            {{ enrollment }} <br>
                            <span class="badge badge-pill {% if enrollment.cherifla %} badge-success {% else %} badge-warning {% endif %}">
                                {% if enrollment.cherifla %} OUVERT {% else %} NON-OUVERT {% endif %}
                            </span>
                            </a> 
                        </td>
                        <td class="align-middle">
                            {% if not enrollment.cherifla %}
                            <a href="" class="btn btn-sm btn-info" 
                            hx-post="{% url 'exams:cherifla_create' %}?student_id={{ enrollment.id }}"
                            onclick="this.disabled = true;">Ouvrir</a>
                            {% endif %}
                        </td>
                        <td class="align-middle text-right" style="min-width: 110px;">{{ enrollment.student.full_name_ar|default_if_none:'' }}</td>
                        <td class="align-middle" style="min-width: 60px">{{ enrollment.generic_level_ar }}</td>
                        <td class="align-middle">{{ enrollment.student.birth_date_str }}</td>
                      </tr>
                      {% endfor %}
                      </tbody>
                  </table>
              </div>
          </div>
      </div>
  </div>
</div>

<script>

  element = document.querySelector('#logout');
  if (typeof(element) !=" undefined") element.style.display = 'block';

  if (typeof(feather) != "undefined") {
    feather.replace();
    $("img.lazy").lazyload();
  }

</script> 