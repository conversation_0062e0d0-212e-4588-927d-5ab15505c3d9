// Dashboard Charts Implementation
function initializeDashboardCharts(chartData) {
    // Wait for DOM and Chart.js to be fully loaded
    if (typeof Chart === 'undefined') {
        console.error('Chart.js is not loaded');
        return;
    }

    // Destroy existing charts if they exist
    if (window.enrollmentChart) {
        window.enrollmentChart.destroy();
        window.enrollmentChart = null;
    }
    if (window.paymentChart) {
        window.paymentChart.destroy();
        window.paymentChart = null;
    }

    // Chart.js configuration
    Chart.defaults.font.family = 'Roboto, sans-serif';
    Chart.defaults.color = '#666';
    Chart.defaults.plugins.legend.display = true;
    Chart.defaults.plugins.legend.position = 'bottom';
    Chart.defaults.responsive = true;
    Chart.defaults.maintainAspectRatio = false;

    // Evolution des Paiements Chart (Monthly Payment Amounts)
    const enrollmentCtx = document.getElementById('enrollmentChart');
    if (enrollmentCtx) {
        const enrollmentChart = new Chart(enrollmentCtx, {
            type: 'line',
            data: {
                labels: ['Sept', 'Oct', 'Nov', 'Déc', 'Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Juin'],
                datasets: [{
                    label: 'Montant des paiements (F CFA)',
                    data: chartData.monthly_payments || [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                    borderColor: '#4CAF50',
                    backgroundColor: 'rgba(76, 175, 80, 0.1)',
                    tension: 0.4,
                    fill: true,
                    pointBackgroundColor: '#4CAF50',
                    pointBorderColor: '#4CAF50',
                    pointHoverBackgroundColor: '#4CAF50',
                    pointHoverBorderColor: '#4CAF50',
                    pointRadius: 5,
                    pointHoverRadius: 7
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        },
                        ticks: {
                            callback: function(value) {
                                return new Intl.NumberFormat('fr-FR').format(value) + ' F';
                            }
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 20
                        }
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        backgroundColor: 'rgba(0,0,0,0.8)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        borderColor: '#ddd',
                        borderWidth: 1,
                        callbacks: {
                            label: function(context) {
                                const value = new Intl.NumberFormat('fr-FR').format(context.parsed.y);
                                return `${context.dataset.label}: ${value} F`;
                            }
                        }
                    }
                },
                interaction: {
                    mode: 'nearest',
                    axis: 'x',
                    intersect: false
                }
            }
        });

        // Store chart reference for resize handling
        window.enrollmentChart = enrollmentChart;
    }

    // État des Paiements Chart
    const paymentCtx = document.getElementById('paymentChart');
    if (paymentCtx) {
        const totalInscription = chartData.inscription || 0;
        const totalScolariteAnnexe = chartData.total_scolarite_annexe || 0;
        const totalPaid = totalInscription + totalScolariteAnnexe;
        
        // Calculate estimated total fees (this is a rough estimate)
        const estimatedTotalFees = chartData.students * 150000; // Assuming average 150k per student
        const remainingAmount = Math.max(0, estimatedTotalFees - totalPaid);

        const paymentChart = new Chart(paymentCtx, {
            type: 'doughnut',
            data: {
                labels: ['Inscription', 'Scolarité + Annexe', 'Reste à payer'],
                datasets: [{
                    data: [totalInscription, totalScolariteAnnexe, remainingAmount],
                    backgroundColor: [
                        '#4CAF50',
                        '#2196F3', 
                        '#FFC107'
                    ],
                    borderColor: [
                        '#4CAF50',
                        '#2196F3',
                        '#FFC107'
                    ],
                    borderWidth: 2,
                    hoverOffset: 4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            usePointStyle: true,
                            padding: 15,
                            generateLabels: function(chart) {
                                const data = chart.data;
                                if (data.labels.length && data.datasets.length) {
                                    return data.labels.map((label, i) => {
                                        const value = data.datasets[0].data[i];
                                        const formattedValue = new Intl.NumberFormat('fr-FR').format(value);
                                        return {
                                            text: `${label}: ${formattedValue} F`,
                                            fillStyle: data.datasets[0].backgroundColor[i],
                                            strokeStyle: data.datasets[0].borderColor[i],
                                            lineWidth: data.datasets[0].borderWidth,
                                            pointStyle: 'circle',
                                            hidden: false,
                                            index: i
                                        };
                                    });
                                }
                                return [];
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0,0,0,0.8)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        borderColor: '#ddd',
                        borderWidth: 1,
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = new Intl.NumberFormat('fr-FR').format(context.parsed);
                                const percentage = ((context.parsed / (totalInscription + totalScolariteAnnexe + remainingAmount)) * 100).toFixed(1);
                                return `${label}: ${value} F (${percentage}%)`;
                            }
                        }
                    }
                },
                cutout: '60%'
            }
        });

        // Store chart reference for resize handling
        window.paymentChart = paymentChart;
    }

    // Add chart responsiveness on window resize
    window.addEventListener('resize', function() {
        if (window.enrollmentChart) {
            window.enrollmentChart.resize();
        }
        if (window.paymentChart) {
            window.paymentChart.resize();
        }
    });
}

// Initialize Material Design menus
function initializeMaterialMenus() {
    // Destroy existing menu instances if they exist
    if (window.paymentsMenuInstance) {
        window.paymentsMenuInstance.destroy();
        window.paymentsMenuInstance = null;
    }
    if (window.statusMenuInstance) {
        window.statusMenuInstance.destroy();
        window.statusMenuInstance = null;
    }

    // Initialize payments chart menu
    const paymentsMenuButton = document.getElementById('paymentsChartMenuButton');
    const paymentsMenu = document.getElementById('paymentsChartMenu');

    if (paymentsMenuButton && paymentsMenu && typeof mdc !== 'undefined') {
        window.paymentsMenuInstance = new mdc.menu.MDCMenu(paymentsMenu);
        paymentsMenuButton.addEventListener('click', () => {
            window.paymentsMenuInstance.open = !window.paymentsMenuInstance.open;
        });
    }

    // Initialize status chart menu
    const statusMenuButton = document.getElementById('statusChartMenuButton');
    const statusMenu = document.getElementById('statusChartMenu');

    if (statusMenuButton && statusMenu && typeof mdc !== 'undefined') {
        window.statusMenuInstance = new mdc.menu.MDCMenu(statusMenu);
        statusMenuButton.addEventListener('click', () => {
            window.statusMenuInstance.open = !window.statusMenuInstance.open;
        });
    }
}

// Initialize charts when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Check if chart data is available
    if (typeof window.chartData !== 'undefined') {
        initializeDashboardCharts(window.chartData);
    } else {
        console.warn('Chart data not available');
    }

    // Initialize Material Design menus
    initializeMaterialMenus();

    // Expose functions globally
    window.initializeDashboardCharts = initializeDashboardCharts;
    window.initializeMaterialMenus = initializeMaterialMenus;
});

// Add htmx event listener after swap triggered by navigation
if (typeof htmx !== 'undefined') {
    htmx.on("htmx:afterSwap", (e) => {
        if (e.detail.target.id === "content-area") {
            // Wait a bit for the DOM to be fully updated
            setTimeout(() => {
                if (typeof window.chartData !== 'undefined' && window.initializeDashboardCharts) {
                    window.initializeDashboardCharts(window.chartData);
                    if (window.initializeMaterialMenus) {
                        window.initializeMaterialMenus();
                    }
                } else {
                    console.warn('Chart data not available after HTMX swap');
                }
            }, 100);
        }
    });
}
