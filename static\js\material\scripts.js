// ===================================
// ALPINE.JS DATA STORE
// ===================================

function minimalistApp() {
    return {
        // State variables
        activeNav: 'home',
        pageTitle: 'Dashboard',

        // Methods
        setActiveNav(nav) {
            this.activeNav = nav;
            this.updatePageTitle(nav);

            // Close sidebar on mobile after navigation
            if (window.innerWidth <= 768) {
                closeSidebar();
            }
        },

        updatePageTitle(nav) {
            const titles = {
                'home': 'Dashboard',
                'page1': 'Page 1',
                'section1': 'Section 1',
                'section2': 'Section 2',
                'subsection1': 'Subsection 1',
                'subsection2': 'Subsection 2',
                'subsection3': 'Subsection 3',
                'subsection4': 'Subsection 4',
                'souspartie1': 'Sous-partie 1',
                'souspartie2': 'Sous-partie 2'
            };

            this.pageTitle = titles[nav] || 'Dashboard';
        }
    }
}

// ===================================
// MATERIAL DESIGN COMPONENTS INITIALIZATION
// ===================================

// Initialize Material Design Components
mdc.autoInit();

// Initialize page preloader spinner
let pagePreloaderSpinner;
if (window.mdc && window.mdc.circularProgress) {
    const preloaderElement = document.getElementById('page-preloader-spinner');
    if (preloaderElement) {
        pagePreloaderSpinner = new mdc.circularProgress.MDCCircularProgress(preloaderElement);
    }
}

// ===================================
// LOADING FUNCTIONS
// ===================================

function showPagePreloader() {
    const preloader = document.getElementById('page-preloader');
    preloader.classList.remove('hidden');
    
    // Start the spinner animation
    if (pagePreloaderSpinner) {
        pagePreloaderSpinner.open();
    }
}

function hidePagePreloader() {
    const preloader = document.getElementById('page-preloader');
    preloader.classList.add('hidden');
    
    // Stop the spinner animation
    if (pagePreloaderSpinner) {
        pagePreloaderSpinner.close();
    }
    
    // Remove preloader from DOM after animation completes
    setTimeout(() => {
        preloader.style.display = 'none';
    }, 500);
}

function showLoadingOverlay(text = 'Processing...') {
    const overlay = document.getElementById('loading-overlay');
    const loadingText = document.getElementById('loading-text');
    loadingText.textContent = text;
    overlay.classList.add('active');
}

function hideLoadingOverlay() {
    const overlay = document.getElementById('loading-overlay');
    overlay.classList.remove('active');
}

// ===================================
// SIDEBAR FUNCTIONS
// ===================================

function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.querySelector('.main-content');
    const contentHeader = document.querySelector('.content-header');

    if (window.innerWidth <= 768) {
        // Mobile behavior: toggle open class
        if (sidebar) {
            sidebar.classList.toggle('open');
            const overlay = document.getElementById('sidebar-overlay');
            if (sidebar.classList.contains('open')) {
                overlay.classList.add('active');
                document.body.style.overflow = 'hidden';
                // Change hamburger to close icon
                updateMenuIcon(true);
            } else {
                overlay.classList.remove('active');
                document.body.style.overflow = '';
                // Change close icon back to hamburger
                updateMenuIcon(false);
            }
        }
    } else {
        // Desktop behavior: toggle collapsed class
        if (sidebar) {
            sidebar.classList.toggle('collapsed');
            // Update menu icon based on sidebar state
            updateMenuIcon(!sidebar.classList.contains('collapsed'));
        }
        if (mainContent) {
            mainContent.classList.toggle('expanded');
        }
        if (contentHeader) {
            contentHeader.classList.toggle('expanded');
        }
    }
}

// Function to update menu icon
function updateMenuIcon(isOpen) {
    const menuBtn = document.getElementById('menu-btn');
    if (!menuBtn) return;

    if (isOpen) {
        // Show close icon
        menuBtn.classList.add('menu-active');
        menuBtn.innerHTML = 'close';
    } else {
        // Show hamburger menu icon
        menuBtn.classList.remove('menu-active');
        menuBtn.innerHTML = 'menu';
    }
}

function openSidebar() {
    const sidebar = document.getElementById('sidebar');
    const overlay = document.getElementById('sidebar-overlay');

    sidebar.classList.add('open');
    overlay.classList.add('active');
    document.body.style.overflow = 'hidden';
    // Show close icon
    updateMenuIcon(true);
}

function closeSidebar() {
    const sidebar = document.getElementById('sidebar');
    const overlay = document.getElementById('sidebar-overlay');

    sidebar.classList.remove('open');
    overlay.classList.remove('active');
    document.body.style.overflow = '';
    // Reset menu icon to hamburger
    updateMenuIcon(false);
}

// ===================================
// OFFCANVAS FUNCTIONS
// ===================================

function toggleUserOffcanvas() {
    const offcanvasOverlay = document.getElementById('offcanvas-overlay');

    if (offcanvasOverlay.classList.contains('active')) {
        closeUserOffcanvas();
    } else {
        openUserOffcanvas();
    }
}

function openUserOffcanvas() {
    const offcanvasOverlay = document.getElementById('offcanvas-overlay');
    const offcanvas = document.getElementById('user-offcanvas');

    offcanvasOverlay.classList.add('active');
    offcanvas.classList.add('active');
    document.body.style.overflow = 'hidden';
}

function closeUserOffcanvas() {
    const offcanvasOverlay = document.getElementById('offcanvas-overlay');
    const offcanvas = document.getElementById('user-offcanvas');

    offcanvasOverlay.classList.remove('active');
    offcanvas.classList.remove('active');
    document.body.style.overflow = '';
}

// ===================================
// DARK MODE FUNCTIONS
// ===================================

// Dark Mode State
let isDarkMode = localStorage.getItem('darkMode') === 'true';

function toggleDarkMode() {
    isDarkMode = !isDarkMode;
    localStorage.setItem('darkMode', isDarkMode);
    updateDarkModeUI();
}

function updateDarkModeUI() {
    const darkModeToggle = document.getElementById('dark-mode-toggle');

    if (isDarkMode) {
        document.documentElement.setAttribute('data-theme', 'dark');
        if (darkModeToggle) {
            darkModeToggle.textContent = 'light_mode';
            darkModeToggle.title = 'Switch to Light Mode';
        }
    } else {
        document.documentElement.removeAttribute('data-theme');
        if (darkModeToggle) {
            darkModeToggle.textContent = 'dark_mode';
            darkModeToggle.title = 'Switch to Dark Mode';
        }
    }
}

// ===================================
// MODAL FUNCTIONS
// ===================================

let modalInstance = null;

function showModal(options = {}) {
    const {
        title = 'Modal Title',
        content = 'Modal content goes here...',
        confirmText = 'Confirm',
        cancelText = 'Cancel',
        showCancel = true,
        onConfirm = null,
        onCancel = null,
        onClose = null
    } = options;

    const modalOverlay = document.getElementById('modal-overlay');
    const modalTitle = document.getElementById('modal-title');
    const modalContent = document.getElementById('modal-content');
    const modalConfirm = document.getElementById('modal-confirm');
    const modalCancel = document.getElementById('modal-cancel');

    // Set content
    modalTitle.textContent = title;
    modalContent.innerHTML = content;
    modalConfirm.querySelector('.mdc-button__label').textContent = confirmText;
    modalCancel.querySelector('.mdc-button__label').textContent = cancelText;

    // Show/hide cancel button
    modalCancel.style.display = showCancel ? 'inline-flex' : 'none';

    // Store callbacks
    modalInstance = {
        onConfirm,
        onCancel,
        onClose
    };

    // Show modal
    modalOverlay.classList.add('active');
    document.body.style.overflow = 'hidden';

    // Focus on confirm button
    setTimeout(() => {
        modalConfirm.focus();
    }, 100);
}

function hideModal() {
    const modalOverlay = document.getElementById('modal-overlay');
    modalOverlay.classList.remove('active');
    document.body.style.overflow = '';

    // Call onClose callback if provided
    if (modalInstance && modalInstance.onClose) {
        modalInstance.onClose();
    }

    modalInstance = null;
}

// ===================================
// SNACKBAR FUNCTIONS
// ===================================

let snackbarMDC = null;

function showSnackbar(message, options = {}) {
    const {
        actionText = null,
        onAction = null,
        timeout = 4000,
        dismissible = true
    } = options;

    const snackbar = document.getElementById('snackbar');
    const snackbarLabel = document.getElementById('snackbar-label');
    const snackbarAction = document.getElementById('snackbar-action');
    const snackbarDismiss = document.getElementById('snackbar-dismiss');

    // Initialize MDC Snackbar if not already done
    if (!snackbarMDC && window.mdc && window.mdc.snackbar) {
        snackbarMDC = new mdc.snackbar.MDCSnackbar(snackbar);
    }

    // Set message
    snackbarLabel.textContent = message;

    // Configure action button
    if (actionText && onAction) {
        snackbarAction.querySelector('.mdc-button__label').textContent = actionText;
        snackbarAction.style.display = 'inline-flex';
        snackbarAction.onclick = () => {
            onAction();
            if (snackbarMDC) {
                snackbarMDC.close();
            }
        };
    } else {
        snackbarAction.style.display = 'none';
    }

    // Configure dismiss button
    snackbarDismiss.style.display = dismissible ? 'inline-flex' : 'none';

    // Set timeout
    if (snackbarMDC) {
        snackbarMDC.timeoutMs = timeout;
        snackbarMDC.open();
    } else {
        // Fallback if MDC is not available
        snackbar.classList.add('mdc-snackbar--open');
        setTimeout(() => {
            snackbar.classList.remove('mdc-snackbar--open');
        }, timeout);
    }
}

// ===================================
// SCROLL BEHAVIOR FUNCTIONS
// ===================================

function initializeScrollBehavior() {
    let lastScrollTop = 0;
    let ticking = false;

    // Get elements
    const topAppBar = document.querySelector('.app-bar');
    const bottomAppBar = document.getElementById('bottom-app-bar');
    const contentHeader = document.querySelector('.content-header');

    console.log('Initializing scroll behavior...', {
        topAppBar: !!topAppBar,
        bottomAppBar: !!bottomAppBar,
        contentHeader: !!contentHeader,
        windowWidth: window.innerWidth,
        isMobile: window.innerWidth <= 768
    });

    if (!topAppBar && !bottomAppBar) {
        console.log('No app bars found, skipping scroll behavior');
        return;
    }

    // Force initial state
    if (topAppBar) {
        topAppBar.style.transition = 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
        topAppBar.style.transform = 'translateY(0)';
    }

    if (bottomAppBar) {
        bottomAppBar.style.transition = 'transform 0.3s ease';
        bottomAppBar.style.transform = 'translateY(0)';
    }

    if (contentHeader) {
        contentHeader.style.transition = 'top 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
        contentHeader.style.top = '56px';
    }

    function handleScroll() {
        if (!ticking) {
            requestAnimationFrame(() => {
                const currentScrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop;
                const scrollDelta = Math.abs(currentScrollTop - lastScrollTop);

                // Only react to significant scroll movements (more than 5px)
                if (scrollDelta > 5) {
                    if (currentScrollTop > lastScrollTop && currentScrollTop > 50) {
                        // Scrolling down - hide app bars
                        hideScrollElements();
                    } else if (currentScrollTop < lastScrollTop) {
                        // Scrolling up - show app bars
                        showScrollElements();
                    }

                    lastScrollTop = Math.max(0, currentScrollTop); // Prevent negative values
                }

                ticking = false;
            });

            ticking = true;
        }
    }

    function hideScrollElements() {
        // Hide bottom app bar (mobile only)
        var sideBar = document.getElementById('sidebar');
        var sideBarCollapsed = !sideBar.classList.contains('open');

        if (bottomAppBar && window.innerWidth <= 768 && sideBarCollapsed) {
            bottomAppBar.style.transform = 'translateY(100%)';
        }

        // Hide top app bar (on all devices)
        if (topAppBar && sideBarCollapsed) {
            topAppBar.style.transform = 'translateY(-100%)';

            // Adjust content header position when app bar is hidden
            if (contentHeader) {
                contentHeader.style.top = '0';
            }
        }
    }

    function showScrollElements() {
        // Show bottom app bar (mobile only)
        if (bottomAppBar && window.innerWidth <= 768) {
            bottomAppBar.style.transform = 'translateY(0)';
        }

        // Show top app bar (on all devices)
        if (topAppBar) {
            topAppBar.style.transform = 'translateY(0)';

            // Restore content header position when app bar is shown
            if (contentHeader) {
                contentHeader.style.top = '56px';
            }
        }
    }

    // Add scroll event listener
    window.addEventListener('scroll', handleScroll, { passive: true });

    // Touch support for mobile
    let touchStartY = 0;
    let touchEndY = 0;

    document.addEventListener('touchstart', (e) => {
        touchStartY = e.changedTouches[0].screenY;
    }, { passive: true });

    document.addEventListener('touchend', (e) => {
        touchEndY = e.changedTouches[0].screenY;
        const touchDelta = Math.abs(touchEndY - touchStartY);

        if (touchDelta > 50) { // Significant touch movement
            if (touchEndY < touchStartY) {
                // Swiping up (scrolling down)
                hideScrollElements();
            } else {
                // Swiping down (scrolling up)
                showScrollElements();
            }
        }
    }, { passive: true });

    // Handle window resize
    window.addEventListener('resize', () => {
        // Reset elements on resize
        if (topAppBar) {
            topAppBar.style.transform = 'translateY(0)';
        }
        if (bottomAppBar) {
            bottomAppBar.style.transform = 'translateY(0)';
        }
        if (contentHeader) {
            contentHeader.style.top = '56px';
        }
    });

    // Store cleanup function
    window.cleanupScrollBehavior = () => {
        window.removeEventListener('scroll', handleScroll);
    };

    // Manual hide/show functions for testing
    window.hideElements = hideScrollElements;
    window.showElements = showScrollElements;

    // Show scroll elements when content header back button is clicked
    const contentHeaderBackBtn = document.querySelector('.content-header .material-icons:first-child');
    if (contentHeaderBackBtn) {
        contentHeaderBackBtn.addEventListener('click', () => {
            showScrollElements();
        });
    }
}

// ===================================
// SUBMENU FUNCTIONS
// ===================================

function toggleSubmenu(submenuId) {
    const submenu = document.getElementById(submenuId);
    const parentItem = document.querySelector(`[data-submenu="${submenuId}"]`);

    if (submenu && parentItem) {
        const isExpanded = submenu.classList.contains('expanded');

        // Close all other submenus at the same level
        document.querySelectorAll('.submenu.expanded').forEach(menu => {
            if (menu !== submenu && !menu.closest('.submenu-level-2')) {
                menu.classList.remove('expanded');
                const parent = document.querySelector(`[data-submenu="${menu.id}"]`);
                if (parent) {
                    parent.classList.remove('expanded');
                }
            }
        });

        // Toggle current submenu
        if (isExpanded) {
            submenu.classList.remove('expanded');
            parentItem.classList.remove('expanded');
        } else {
            submenu.classList.add('expanded');
            parentItem.classList.add('expanded');
        }
    }
}

// Function to handle level-2 submenu toggles
function toggleLevel2Submenu(submenuId) {
    const submenu = document.getElementById(submenuId);
    const parentItem = document.querySelector(`[data-submenu="${submenuId}"]`);

    if (submenu && parentItem) {
        const isExpanded = submenu.classList.contains('expanded');

        // Close all other level-2 submenus
        document.querySelectorAll('.submenu-level-2.expanded').forEach(menu => {
            if (menu !== submenu) {
                menu.classList.remove('expanded');
                const parent = document.querySelector(`[data-submenu="${menu.id}"]`);
                if (parent) {
                    parent.classList.remove('expanded');
                }
            }
        });

        // Toggle current level-2 submenu
        if (isExpanded) {
            submenu.classList.remove('expanded');
            parentItem.classList.remove('expanded');
        } else {
            submenu.classList.add('expanded');
            parentItem.classList.add('expanded');
        }
    }
}

// ===================================
// EVENT LISTENERS
// ===================================

document.addEventListener('DOMContentLoaded', function() {
    // Initialize dark mode
    if (isDarkMode) {
        document.documentElement.setAttribute('data-theme', 'dark');
    }
    updateDarkModeUI();
    
    // Menu button click handler
    const menuBtn = document.getElementById('menu-btn');
    if (menuBtn) {
        menuBtn.addEventListener('click', toggleSidebar);
        // Initialize menu icon state
        updateMenuIcon(false);
    }
    
    // Dark mode toggle
    const darkModeToggle = document.getElementById('dark-mode-toggle');
    if (darkModeToggle) {
        darkModeToggle.addEventListener('click', toggleDarkMode);
    }
    
    // More menu button (user offcanvas)
    const moreMenuBtn = document.getElementById('more-menu-btn');
    if (moreMenuBtn) {
        moreMenuBtn.addEventListener('click', toggleUserOffcanvas);
    }
    
    // Close user offcanvas button
    const closeUserOffcanvasBtn = document.getElementById('close-user-offcanvas');
    if (closeUserOffcanvasBtn) {
        closeUserOffcanvasBtn.addEventListener('click', closeUserOffcanvas);
    }
    
    // Sidebar overlay click handler
    const sidebarOverlay = document.getElementById('sidebar-overlay');
    if (sidebarOverlay) {
        sidebarOverlay.addEventListener('click', function() {
            closeSidebar();
        });
    }

    // Offcanvas overlay click handler
    const offcanvasOverlay = document.getElementById('offcanvas-overlay');
    if (offcanvasOverlay) {
        offcanvasOverlay.addEventListener('click', function(e) {
            if (e.target === offcanvasOverlay) {
                closeUserOffcanvas();
            }
        });
    }
    
    // Submenu toggle handlers for main sidebar items
    document.querySelectorAll('.sidebar-item-with-submenu').forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            const submenuId = this.dataset.submenu;
            if (submenuId) {
                toggleSubmenu(submenuId);
            }
        });
    });

    // Submenu toggle handlers for level-2 submenu items
    document.querySelectorAll('.submenu-item-with-submenu').forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            const submenuId = this.dataset.submenu;
            if (submenuId) {
                toggleLevel2Submenu(submenuId);
            }
        });
    });
    
    // Initialize modal event listeners
    const modalOverlay = document.getElementById('modal-overlay');
    const modalClose = document.getElementById('modal-close');
    const modalConfirm = document.getElementById('modal-confirm');
    const modalCancel = document.getElementById('modal-cancel');

    if (modalClose) {
        modalClose.addEventListener('click', () => {
            if (modalInstance && modalInstance.onClose) {
                modalInstance.onClose();
            }
            hideModal();
        });
    }

    if (modalConfirm) {
        modalConfirm.addEventListener('click', () => {
            if (modalInstance && modalInstance.onConfirm) {
                modalInstance.onConfirm();
            }
            hideModal();
        });
    }

    if (modalCancel) {
        modalCancel.addEventListener('click', () => {
            if (modalInstance && modalInstance.onCancel) {
                modalInstance.onCancel();
            }
            hideModal();
        });
    }

    if (modalOverlay) {
        modalOverlay.addEventListener('click', (e) => {
            if (e.target === modalOverlay) {
                if (modalInstance && modalInstance.onClose) {
                    modalInstance.onClose();
                }
                hideModal();
            }
        });
    }

    // Initialize snackbar
    const snackbar = document.getElementById('snackbar');
    if (snackbar && window.mdc && window.mdc.snackbar) {
        snackbarMDC = new mdc.snackbar.MDCSnackbar(snackbar);
    }

    // Add ripple effects to offcanvas menu items
    document.querySelectorAll('.offcanvas-menu-item').forEach(item => {
        item.addEventListener('click', (e) => {
            createRipple(e, item, true);
        });
    });

    // Keyboard support for modal, offcanvas and sidebar
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            const modalOverlay = document.getElementById('modal-overlay');
            const offcanvasOverlay = document.getElementById('offcanvas-overlay');
            const sidebarOverlay = document.getElementById('sidebar-overlay');

            if (modalOverlay && modalOverlay.classList.contains('active')) {
                if (modalInstance && modalInstance.onClose) {
                    modalInstance.onClose();
                }
                hideModal();
            } else if (offcanvasOverlay && offcanvasOverlay.classList.contains('active')) {
                closeUserOffcanvas();
            } else if (sidebarOverlay && sidebarOverlay.classList.contains('active')) {
                closeSidebar();
            }
        }
    });

    // Initialize scroll behavior (with delay to ensure DOM is ready)
    setTimeout(() => {
        initializeScrollBehavior();
    }, 500);

    // Initialize sidebar search
    initializeSidebarSearch();

    // Hide page preloader after page load
    setTimeout(() => {
        hidePagePreloader();
    }, 1000);
});

// ===================================
// RESPONSIVE HANDLERS
// ===================================

// Handle window resize
window.addEventListener('resize', function() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.querySelector('.main-content');
    const contentHeader = document.querySelector('.content-header');

    if (window.innerWidth > 768) {
        // Desktop: remove mobile classes and close offcanvas
        if (sidebar) {
            sidebar.classList.remove('open');
            // Update menu icon based on collapsed state
            updateMenuIcon(!sidebar.classList.contains('collapsed'));
        }
        closeSidebar();
        closeUserOffcanvas();
    } else {
        // Mobile: remove desktop classes
        if (sidebar) {
            sidebar.classList.remove('collapsed');
        }
        if (mainContent) {
            mainContent.classList.remove('expanded');
        }
        if (contentHeader) {
            contentHeader.classList.remove('expanded');
        }
        // Reset menu icon to hamburger for mobile
        updateMenuIcon(false);
    }
});

// ===================================
// DEMO FUNCTIONS
// ===================================

function showModalDemo() {
    showModal({
        title: 'Demo Modal',
        content: '<p>This is a demo modal with Material Design styling.</p><p>You can customize the title, content, and button labels.</p>',
        confirmText: 'Got it',
        cancelText: 'Cancel',
        showCancel: true,
        onConfirm: () => {
            showSnackbar('Modal confirmed!');
        },
        onCancel: () => {
            showSnackbar('Modal cancelled');
        },
        onClose: () => {
            console.log('Modal closed');
        }
    });
}

function showSnackbarDemo() {
    showSnackbar('This is a simple snackbar message!');
}

function showSnackbarWithAction() {
    showSnackbar('Message with action button', {
        actionText: 'UNDO',
        onAction: () => {
            showSnackbar('Action button clicked!');
        },
        timeout: 6000
    });
}

// ===================================
// SIDEBAR SEARCH FUNCTIONS
// ===================================

let originalSidebarState = null;

function initializeSidebarSearch() {
    const searchInput = document.getElementById('sidebar-search-input');
    if (!searchInput) return;

    // Store original state of all sidebar items
    storeOriginalSidebarState();

    // Add event listener for search input
    searchInput.addEventListener('input', handleSidebarSearch);
}

function storeOriginalSidebarState() {
    originalSidebarState = {
        sidebarItems: [],
        submenus: [],
        submenuItems: []
    };

    // Store all sidebar items
    document.querySelectorAll('.sidebar-item').forEach(item => {
        originalSidebarState.sidebarItems.push({
            element: item,
            display: getComputedStyle(item).display,
            text: item.textContent.trim()
        });
    });

    // Store all submenus
    document.querySelectorAll('.submenu').forEach(submenu => {
        originalSidebarState.submenus.push({
            element: submenu,
            display: getComputedStyle(submenu).display,
            expanded: submenu.classList.contains('expanded'),
            searchExpanded: submenu.classList.contains('search-expanded')
        });
    });

    // Store all submenu items
    document.querySelectorAll('.submenu-item').forEach(item => {
        originalSidebarState.submenuItems.push({
            element: item,
            display: getComputedStyle(item).display,
            text: item.textContent.trim()
        });
    });
}

function handleSidebarSearch(event) {
    const query = event.target.value.toLowerCase().trim();

    if (query === '') {
        resetSidebarToOriginalState();
        return;
    }

    filterSidebarItems(query);
}

function resetSidebarToOriginalState() {
    if (!originalSidebarState) return;

    // Reset all sidebar items
    originalSidebarState.sidebarItems.forEach(item => {
        item.element.style.display = item.display;
    });

    // Reset all submenus
    originalSidebarState.submenus.forEach(submenu => {
        submenu.element.style.display = submenu.display;
        submenu.element.classList.remove('search-expanded');
        if (!submenu.expanded) {
            submenu.element.classList.remove('expanded');
        }
    });

    // Reset all submenu items
    originalSidebarState.submenuItems.forEach(item => {
        item.element.style.display = item.display;
    });
}

function filterSidebarItems(query) {
    if (!originalSidebarState) return;

    // Hide all items initially
    originalSidebarState.sidebarItems.forEach(item => {
        item.element.style.display = 'none';
    });
    originalSidebarState.submenus.forEach(submenu => {
        submenu.element.style.display = 'none';
        submenu.element.classList.remove('search-expanded');
    });
    originalSidebarState.submenuItems.forEach(item => {
        item.element.style.display = 'none';
    });

    // Find matching items and show them with their parents
    const matchingItems = new Set();
    const menusToShow = new Set();
    const submenusToExpand = new Set();

    // Check sidebar items
    originalSidebarState.sidebarItems.forEach(item => {
        if (item.text.toLowerCase().includes(query)) {
            matchingItems.add(item.element);

            // If this item has a submenu, mark it for expansion
            const submenuId = item.element.dataset.submenu;
            if (submenuId) {
                submenusToExpand.add(submenuId);
            }
        }
    });

    // Check submenu items (including level-2 submenu items)
    originalSidebarState.submenuItems.forEach(item => {
        if (item.text.toLowerCase().includes(query)) {
            matchingItems.add(item.element);

            // Find all parent elements up the hierarchy
            let currentElement = item.element;

            // Traverse up to find all parent submenus and sidebar items
            while (currentElement) {
                const parentSubmenu = currentElement.closest('.submenu');
                if (parentSubmenu) {
                    menusToShow.add(parentSubmenu);
                    submenusToExpand.add(parentSubmenu.id);

                    // Check if this submenu is a level-2 submenu
                    if (parentSubmenu.classList.contains('submenu-level-2')) {
                        // Find the parent submenu-item that controls this level-2 submenu
                        const parentSubmenuItem = document.querySelector(`[data-submenu="${parentSubmenu.id}"]`);
                        if (parentSubmenuItem) {
                            matchingItems.add(parentSubmenuItem);

                            // Find the parent level-1 submenu
                            const level1Submenu = parentSubmenuItem.closest('.submenu');
                            if (level1Submenu) {
                                menusToShow.add(level1Submenu);
                                submenusToExpand.add(level1Submenu.id);
                            }
                        }
                    }

                    // Find and show parent sidebar item
                    const parentSidebarItem = document.querySelector(`[data-submenu="${parentSubmenu.id}"]`);
                    if (parentSidebarItem && parentSidebarItem.classList.contains('sidebar-item')) {
                        matchingItems.add(parentSidebarItem);
                    }

                    // Move up to check for more parent levels
                    currentElement = parentSubmenu.parentElement;
                } else {
                    break;
                }
            }
        }
    });

    // Show matching sidebar items
    matchingItems.forEach(item => {
        item.style.display = '';
    });

    // Show and expand relevant submenus
    menusToShow.forEach(submenu => {
        submenu.style.display = '';
        submenu.classList.add('search-expanded');
    });

    // Expand submenus that have matching items
    submenusToExpand.forEach(submenuId => {
        const submenu = document.getElementById(submenuId);
        if (submenu) {
            submenu.style.display = '';
            submenu.classList.add('search-expanded');
        }
    });

    // Show matching submenu items
    originalSidebarState.submenuItems.forEach(item => {
        if (item.text.toLowerCase().includes(query)) {
            item.element.style.display = '';
        }
    });
}

// ===================================
// UTILITY FUNCTIONS
// ===================================

// Simulate loading delay for better UX
function simulateLoading(callback, delay = 300) {
    setTimeout(callback, delay);
}

// Create ripple effect for buttons
function createRipple(event, element, isCircular = false) {
    const ripple = document.createElement('span');
    const rect = element.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;

    ripple.style.width = ripple.style.height = size + 'px';
    ripple.style.left = x + 'px';
    ripple.style.top = y + 'px';
    ripple.classList.add('ripple');

    if (isCircular) {
        ripple.classList.add('ripple-circular');
    }

    element.appendChild(ripple);

    setTimeout(() => {
        ripple.remove();
    }, 600);
}

// ===================================
// HTMX LOADING STATES
// ===================================

// Initialize HTMX loading indicators
document.addEventListener('DOMContentLoaded', function() {
    initializeHTMXLoading();
});

function initializeHTMXLoading() {
    // Add loading indicators to elements that will be HTMX targets
    addLoadingIndicators();

    // Set up HTMX event listeners
    setupHTMXEventListeners();
}

function addLoadingIndicators() {
    // Add loading indicators to common HTMX target elements
    const targets = [
        '#content-area',
        '#dialog',
        '.dashboard-card',
        '.mdc-card',
        '[hx-target]'
    ];

    targets.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            if (!element.querySelector('.htmx-indicator')) {
                const indicator = createMaterialLoadingIndicator();
                element.appendChild(indicator);
            }
        });
    });
}

function createMaterialLoadingIndicator() {
    const indicator = document.createElement('div');
    indicator.className = 'htmx-indicator';

    indicator.innerHTML = `
        <div class="mdc-circular-progress mdc-circular-progress--htmx mdc-circular-progress--indeterminate" role="progressbar" aria-label="Loading..." aria-valuemin="0" aria-valuemax="1">
            <div class="mdc-circular-progress__determinate-container">
                <svg class="mdc-circular-progress__determinate-circle-graphic" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                    <circle class="mdc-circular-progress__determinate-track" cx="16" cy="16" r="12.5" stroke-width="3"/>
                    <circle class="mdc-circular-progress__determinate-circle" cx="16" cy="16" r="12.5" stroke-dasharray="78.54" stroke-dashoffset="78.54" stroke-width="3"/>
                </svg>
            </div>
            <div class="mdc-circular-progress__indeterminate-container">
                <div class="mdc-circular-progress__spinner-layer">
                    <div class="mdc-circular-progress__circle-clipper mdc-circular-progress__circle-left">
                        <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="16" cy="16" r="12.5" stroke-dasharray="78.54" stroke-dashoffset="39.27" stroke-width="3"/>
                        </svg>
                    </div>
                    <div class="mdc-circular-progress__gap-patch">
                        <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="16" cy="16" r="12.5" stroke-dasharray="78.54" stroke-dashoffset="39.27" stroke-width="2.4"/>
                        </svg>
                    </div>
                    <div class="mdc-circular-progress__circle-clipper mdc-circular-progress__circle-right">
                        <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="16" cy="16" r="12.5" stroke-dasharray="78.54" stroke-dashoffset="39.27" stroke-width="3"/>
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    `;

    return indicator;
}

function setupHTMXEventListeners() {
    // Only set up if HTMX is available
    if (typeof htmx === 'undefined') {
        console.warn('HTMX not found, loading indicators will not work');
        return;
    }

    // Before HTMX request starts
    htmx.on('htmx:beforeRequest', function(evt) {
        const target = evt.detail.target;
        if (target) {
            target.classList.add('htmx-request');

            // Initialize Material Design progress indicator if it exists
            const indicator = target.querySelector('.mdc-circular-progress');
            if (indicator && window.mdc && window.mdc.circularProgress) {
                const progressInstance = new mdc.circularProgress.MDCCircularProgress(indicator);
                progressInstance.determinate = false;
                target._progressInstance = progressInstance;
            }
        }
    });

    // After HTMX request completes (success or error)
    htmx.on('htmx:afterRequest', function(evt) {
        const target = evt.detail.target;
        if (target) {
            target.classList.remove('htmx-request');

            // Clean up Material Design progress indicator
            if (target._progressInstance) {
                target._progressInstance.destroy();
                delete target._progressInstance;
            }
        }
    });

    // When HTMX swaps content
    htmx.on('htmx:afterSwap', function(evt) {
        const target = evt.detail.target;
        if (target) {
            // Re-add loading indicators to new content
            setTimeout(() => {
                addLoadingIndicators();
            }, 100);
        }
    });

    // Handle HTMX errors
    htmx.on('htmx:responseError', function(evt) {
        const target = evt.detail.target;
        if (target) {
            target.classList.remove('htmx-request');

            // Clean up Material Design progress indicator
            if (target._progressInstance) {
                target._progressInstance.destroy();
                delete target._progressInstance;
            }
        }

        console.error('HTMX request failed:', evt.detail);
    });
}
