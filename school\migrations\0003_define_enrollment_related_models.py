# Generated by Django 4.2.2 on 2023-07-02 19:49

import cloudinary.models
from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('school', '0002_change_year_full_name_field_type_to_charfield'),
    ]

    operations = [
        migrations.CreateModel(
            name='Enrollment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('enrollment_fees', models.PositiveIntegerField(default=0)),
                ('year_fees', models.PositiveIntegerField(default=0)),
                ('status', models.BooleanField(default=False, help_text="status de l'élève: Affecté ou Non affecté", verbose_name='affecté')),
                ('stays_down', models.BooleanField(default=False, verbose_name='redoublant')),
                ('active', models.BooleanField(default=True, help_text="indique si l'élève est actif ou inactif. Un élève inactif n'apparaîtra pas dans les résultats et documents", verbose_name='actif')),
                ('lv2', models.CharField(blank=True, choices=[('E', 'Espagnol'), ('A', 'Allemand')], max_length=1, null=True, verbose_name='LV2')),
                ('has_scolarship', models.BooleanField(default=False, help_text="indique si l'élève est boursier ou non.", verbose_name='boursier')),
                ('discount', models.PositiveSmallIntegerField(blank=True, null=True)),
                ('agent', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='inscrit par')),
            ],
            options={
                'verbose_name': 'inscription',
            },
        ),
        migrations.AlterField(
            model_name='genericlevel',
            name='name',
            field=models.CharField(max_length=255, unique=True, verbose_name='nom du niveau'),
        ),
        migrations.AlterField(
            model_name='genericlevel',
            name='short_name',
            field=models.CharField(max_length=10, unique=True, verbose_name='abbréviation'),
        ),
        migrations.CreateModel(
            name='Student',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('identifier', models.CharField(max_length=10, unique=True, verbose_name='identifiant')),
                ('student_id', models.CharField(blank=True, max_length=9, null=True, verbose_name='matricule')),
                ('last_name', models.CharField(max_length=255, verbose_name='nom en français')),
                ('first_name', models.CharField(max_length=255, verbose_name='prénoms en français')),
                ('full_name_ar', models.CharField(blank=True, max_length=255, null=True, verbose_name='nom et prénoms en arabe')),
                ('gender', models.CharField(choices=[('M', 'Masculin'), ('F', 'Féminin')], max_length=1, verbose_name='sexe')),
                ('birth_day', models.PositiveSmallIntegerField(validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(31)], verbose_name='jour')),
                ('birth_month', models.PositiveSmallIntegerField(choices=[(1, 'Janvier'), (2, 'Février'), (3, 'Mars'), (4, 'Avril'), (5, 'Mai'), (6, 'Juin'), (7, 'Juillet'), (8, 'Août'), (9, 'Septembre'), (10, 'Octobre'), (11, 'Novembre'), (12, 'Décembre')], verbose_name='mois')),
                ('birth_year', models.PositiveSmallIntegerField(validators=[django.core.validators.MinValueValidator(1980)], verbose_name='année')),
                ('birth_place', models.CharField(max_length=255, verbose_name='lieu de naissance')),
                ('birth_place_ar', models.CharField(blank=True, max_length=255, null=True, verbose_name='lieu de naissance (traduction arabe)')),
                ('phone', models.CharField(blank=True, max_length=14, null=True, verbose_name="contact de l'élève")),
                ('father', models.CharField(blank=True, max_length=255, null=True, verbose_name='père')),
                ('father_phone', models.CharField(blank=True, max_length=255, null=True, verbose_name='contact du père')),
                ('mother', models.CharField(blank=True, max_length=255, null=True, verbose_name='mère')),
                ('mother_phone', models.CharField(blank=True, max_length=255, null=True, verbose_name='contact de la mère')),
                ('certificate_num', models.CharField(blank=True, max_length=10, null=True, verbose_name="numéro de l'extrait")),
                ('certificate_date', models.DateField(blank=True, null=True, verbose_name='établi le')),
                ('certificate_place', models.CharField(blank=True, max_length=255, null=True)),
                ('photo', cloudinary.models.CloudinaryField(blank=True, max_length=255, null=True, verbose_name='image')),
                ('certificate_img', cloudinary.models.CloudinaryField(blank=True, max_length=255, null=True, verbose_name='image')),
                ('origin', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='school.year', verbose_name='origine')),
            ],
            options={
                'verbose_name': 'élèvé',
            },
        ),
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('amount', models.IntegerField(verbose_name='montant versé')),
                ('agent', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('enrollment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='school.enrollment')),
            ],
            options={
                'verbose_name': 'versement',
            },
        ),
        migrations.CreateModel(
            name='Level',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('number', models.PositiveSmallIntegerField()),
                ('translation', models.CharField(blank=True, max_length=255, null=True)),
                ('education', models.CharField(choices=[('A', 'Franco-Arabe'), ('F', 'Française')], default='F', max_length=2, verbose_name='langue')),
                ('generic_level', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='school.genericlevel')),
                ('school', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='school.school')),
                ('year', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='school.year')),
            ],
            options={
                'verbose_name': 'classe',
                'ordering': ['school', 'generic_level__order', 'number'],
                'unique_together': {('year', 'school', 'generic_level', 'number', 'education')},
            },
        ),
        migrations.AddField(
            model_name='enrollment',
            name='level_ar',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to='school.level', verbose_name='classe arabe'),
        ),
        migrations.AddField(
            model_name='enrollment',
            name='level_fr',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='school.level', verbose_name='classe'),
        ),
        migrations.AddField(
            model_name='enrollment',
            name='school',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='school.school'),
        ),
        migrations.AddField(
            model_name='enrollment',
            name='student',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='school.student'),
        ),
        migrations.AddField(
            model_name='enrollment',
            name='year',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='school.year'),
        ),
        migrations.CreateModel(
            name='Holiday',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(choices=[('RMD', 'Fête de Ramadan'), ('TBK', 'Fête de Tabaski'), ('FBR', 'Congés de Février'), ('TST', 'Fête de Toussaint'), ('NYR', 'Noel et du Nouvel An'), ('PDY', 'Fête National de la Paix'), ('EAS', 'Fête de Pâcques'), ('EOY', 'Grandes vacances'), ('VLT', 'Saint Valentin'), ('OTH', 'Autres')], max_length=255, verbose_name='congés')),
                ('start_date', models.DateField(verbose_name='Début')),
                ('end_date', models.DateField(verbose_name='Fin')),
                ('description', models.CharField(blank=True, max_length=255, null=True, verbose_name='description')),
                ('year', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='school.year', verbose_name='année scolaire')),
            ],
            options={
                'verbose_name': 'congé',
                'unique_together': {('year', 'name')},
            },
        ),
        migrations.AlterUniqueTogether(
            name='enrollment',
            unique_together={('student', 'year')},
        ),
    ]
