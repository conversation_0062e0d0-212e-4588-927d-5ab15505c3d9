{% load widget_tweaks %}
<div class="row" hx-get="{% url 'exams:results' %}?type={{type}}&lang={{lang}}" 
     hx-trigger="saved from:body" 
     hx-target="#table_container"
     hx-include="this">
    <div class="col">
        <div class="tile">
            <div class="alert bg-light">
                <a href="#" onclick="event.preventDefault()" class="btn btn-sm btn-warning">Modèle 1: Par défaut</a>
                <a href="#" onclick="event.preventDefault()" class="btn btn-sm btn-info">Modèle 2: Avec Rappels</a>
            </div>
            <form action="" method="post" class="mb-3">
                {% csrf_token %}
                <div class="form-row">
                    <div class="col-3 col-lg-3 form-group">
                        <label for="generic_level">Niveau</label>
                        <select name="short_name" id="generic_level" class="form-control" 
                                hx-get="{% url 'exams:sublevels' %}?lang={{lang}}"
                                hx-target="#level_container">
                            <option value="">--------</option>
                            {% for level in generic_levels %}
                            <option value="{{ level.id }}">{{ level }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-4 col-lg-3 form-group" id="level_container">
                        <label for="level">Classe</label>
                        <select name="level" id="level" class="form-control"></select>
                    </div>
                    <div class="col-5 col-lg-3 form-group" id="term_container">
                        <label for="id_term">Période</label>
                        <select name="term" id="id_term" class="form-control" 
                                required="required" hx-swap="outerHTML">
                        </select>
                    </div>
                </div>
                <button type="submit" class="btn btn-success btn-sm" 
                        hx-get="{% url 'exams:period_results' %}?lang={{ lang }}" 
                        hx-target="#table_container">Afficher</button>
            </form>
            <form method="post" class="table-responsive" id="table_container" 
                  action="{{ request.path }}">
            </form>
        </div>
    </div>
</div>