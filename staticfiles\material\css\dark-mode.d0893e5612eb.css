/* Material Design Dark Mode Styles */

/* Variables for dark mode */
:root {
  --dark-bg-primary: #121212;
  --dark-bg-secondary: #1e1e1e;
  --dark-surface: #242424;
  --dark-card: #2a2a2a;
  --dark-text-primary: rgba(255, 255, 255, 0.87);
  --dark-text-secondary: rgba(255, 255, 255, 0.6);
  --dark-text-disabled: rgba(255, 255, 255, 0.38);
  --dark-divider: rgba(255, 255, 255, 0.12);
  --dark-elevation-1: rgba(255, 255, 255, 0.05);
  --dark-elevation-2: rgba(255, 255, 255, 0.07);
  --dark-elevation-3: rgba(255, 255, 255, 0.08);
  --dark-elevation-4: rgba(255, 255, 255, 0.09);
  --dark-elevation-6: rgba(255, 255, 255, 0.11);
  --dark-elevation-8: rgba(255, 255, 255, 0.12);
  --dark-elevation-12: rgba(255, 255, 255, 0.14);
  --dark-elevation-16: rgba(255, 255, 255, 0.15);
  --dark-elevation-24: rgba(255, 255, 255, 0.16);
  
  /* Transition settings */
  --transition-speed: 0.3s;
  --transition-timing: ease;
}

/* Base transition for all elements */
body, .mdc-drawer, .mdc-top-app-bar, .page-content, .mdc-card, 
.mdc-data-table, .mdc-text-field, .mdc-button, .mdc-menu, 
.mdc-dialog, .mdc-select, .mdc-checkbox, .mdc-radio, .mdc-switch,
.mdc-list, .mdc-list-item, input, select, textarea {
  transition: all var(--transition-speed) var(--transition-timing);
}

/* Dark mode class applied to body */
body.dark-mode {
  background-color: var(--dark-bg-primary);
  color: var(--dark-text-primary);
}

/* App Bar */
body.dark-mode .mdc-top-app-bar {
  background-color: var(--dark-bg-secondary);
}

/* Drawer */
body.dark-mode .mdc-drawer {
  background-color: var(--dark-bg-secondary);
  border-right-color: var(--dark-divider);
}

body.dark-mode .mdc-drawer .mdc-list-item {
  color: var(--dark-text-primary);
}

body.dark-mode .mdc-drawer .mdc-list-item--activated {
  background-color: var(--dark-elevation-8);
  color: var(--md-primary);
}

body.dark-mode .mdc-drawer .mdc-list-item:hover {
  background-color: var(--dark-elevation-4);
}

/* Main content */
body.dark-mode .page-content {
  background-color: var(--dark-bg-primary);
}

/* Cards */
body.dark-mode .mdc-card {
  background-color: var(--dark-card);
  color: var(--dark-text-primary);
  border-color: var(--dark-divider);
}

/* Data Tables */
body.dark-mode .mdc-data-table {
  background-color: var(--dark-card);
  border-color: var(--dark-divider);
}

body.dark-mode .mdc-data-table__header-cell {
  color: var(--dark-text-primary);
  border-bottom-color: var(--dark-divider);
}

body.dark-mode .mdc-data-table__row {
  border-top-color: var(--dark-divider);
}

body.dark-mode .mdc-data-table__cell {
  color: var(--dark-text-primary);
  border-top-color: var(--dark-divider);
}

body.dark-mode .mdc-data-table__row:hover {
  background-color: var(--dark-elevation-4);
}

/* Text Fields */
body.dark-mode .mdc-text-field--outlined {
  background-color: transparent;
}

body.dark-mode .mdc-text-field--outlined .mdc-notched-outline__leading,
body.dark-mode .mdc-text-field--outlined .mdc-notched-outline__notch,
body.dark-mode .mdc-text-field--outlined .mdc-notched-outline__trailing {
  border-color: var(--dark-divider);
}

body.dark-mode .mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-notched-outline__leading,
body.dark-mode .mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-notched-outline__notch,
body.dark-mode .mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-notched-outline__trailing {
  border-color: var(--dark-text-secondary);
}

body.dark-mode .mdc-text-field--outlined .mdc-floating-label {
  color: var(--dark-text-secondary);
}

body.dark-mode .mdc-text-field--outlined .mdc-text-field__input {
  color: var(--dark-text-primary);
}

body.dark-mode .mdc-text-field--outlined .mdc-text-field__input::placeholder {
  color: var(--dark-text-disabled);
}

/* Buttons */
body.dark-mode .mdc-button--outlined {
  border-color: var(--dark-divider);
  color: var(--dark-text-primary);
}

body.dark-mode .mdc-button--outlined:hover {
  background-color: var(--dark-elevation-4);
}

/* Dialogs */
body.dark-mode .mdc-dialog .mdc-dialog__surface {
  background-color: var(--dark-card);
}

body.dark-mode .mdc-dialog .mdc-dialog__title {
  color: var(--dark-text-primary);
}

body.dark-mode .mdc-dialog .mdc-dialog__content {
  color: var(--dark-text-secondary);
}

/* Select */
body.dark-mode .mdc-select--outlined .mdc-notched-outline__leading,
body.dark-mode .mdc-select--outlined .mdc-notched-outline__notch,
body.dark-mode .mdc-select--outlined .mdc-notched-outline__trailing {
  border-color: var(--dark-divider);
}

body.dark-mode .mdc-select--outlined:not(.mdc-select--disabled):hover .mdc-notched-outline__leading,
body.dark-mode .mdc-select--outlined:not(.mdc-select--disabled):hover .mdc-notched-outline__notch,
body.dark-mode .mdc-select--outlined:not(.mdc-select--disabled):hover .mdc-notched-outline__trailing {
  border-color: var(--dark-text-secondary);
}

body.dark-mode .mdc-select--outlined .mdc-floating-label {
  color: var(--dark-text-secondary);
}

body.dark-mode .mdc-select--outlined .mdc-select__selected-text {
  color: var(--dark-text-primary);
}

body.dark-mode .mdc-select__menu .mdc-list {
  background-color: var(--dark-card);
}

body.dark-mode .mdc-select__menu .mdc-list-item {
  color: var(--dark-text-primary);
}

body.dark-mode .mdc-select__menu .mdc-list-item:hover {
  background-color: var(--dark-elevation-4);
}

/* Checkboxes */
body.dark-mode .mdc-checkbox__background {
  border-color: var(--dark-text-secondary);
}

/* Bottom Navigation */
body.dark-mode .bottom-nav {
  background-color: var(--dark-bg-secondary);
}

body.dark-mode .bottom-nav-item {
  color: var(--dark-text-secondary);
}

body.dark-mode .bottom-nav-item.active {
  color: var(--md-primary);
}

/* Dark mode toggle button */
.dark-mode-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: transform 0.5s ease;
}

body.dark-mode .dark-mode-toggle {
  transform: rotate(180deg);
}
