{% load static %}
<form action="">
    {% csrf_token %}
    <table class="table table-striped table-sm checkbox-table" id="datatable">
        <thead>
            <tr>
                <th><input type="checkbox" name="select-all" id="select-all"></th>
                <th>Matricule</th>
                <th>Nom et Prénoms</th>
                <th>Classe</th>

                {% for header in headers %}
                <th>{{ header }}</th>
                {% endfor %}
                <th>MGA</th>
                <th>DFA</th>
            </tr>
        </thead>

        <tbody>
            {% for enrollment, averages in data.items %}
            <tr>
                <td><input type="checkbox" name="check-{{ enrollment.id}}" id="check-{{ enrollment.id}}"></td>
                <td>{% if enrollment.student.student_id %} {{ enrollment.student.student_id }} {% endif %}</td>
                <td>{{ enrollment}}</td>
                <td>{% if request.GET.education == 'fr' or request.GET.education == 'F' %} {{ enrollment.level_fr }} {% else %} {{ enrollment.level_ar }} {% endif %}</td>

                {% for average in averages %}
                <td>{{ average }}</td>
                {% endfor %}
                <td>{{ enrollment.mga }}</td>
                <td>{% if enrollment.dfa %} {{ enrollment.dfa }} {% else %} - {% endif %}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% include 'partials/student/actions.html' with enable_btn=True %}
</form>

{% if request.GET.education == 'ar' %}
    <a href="{% url 'exams:combined_results_ar' %}?level={{ level }}" target="_blank" class="btn btn-success">Imprimer Résultats Détaillés Par Matière</a>
{% endif %}

<script>
    $(document).ready(function(){
        $(".checkbox-table").simpleCheckboxTable();
    });
</script>