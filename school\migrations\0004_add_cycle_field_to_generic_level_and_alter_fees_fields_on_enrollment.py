# Generated by Django 4.2.2 on 2023-07-02 21:52

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('school', '0003_define_enrollment_related_models'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='student',
            options={'verbose_name': 'élève'},
        ),
        migrations.AddField(
            model_name='genericlevel',
            name='cycle',
            field=models.CharField(choices=[('P', 'Primaire'), ('S', 'Secondaire')], default='P', max_length=1),
        ),
        migrations.AlterField(
            model_name='enrollment',
            name='enrollment_fees',
            field=models.PositiveIntegerField(default=0, verbose_name='frais inscription'),
        ),
        migrations.AlterField(
            model_name='enrollment',
            name='year_fees',
            field=models.PositiveIntegerField(default=0, verbose_name='scolarité'),
        ),
    ]
