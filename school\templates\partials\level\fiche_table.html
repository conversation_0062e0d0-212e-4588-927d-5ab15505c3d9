{% load exams_tags %}
<div class="tile" id="tile" hx-get="{% url 'school:levels' %}?education={{active_nav}}" hx-trigger="saved from:body" hx-target="#app-content">
    {% if perms.school.add_level %}
    <div class="tile-title-w-btn d-flex">
        <div class="btn-group">
            <a class="btn btn-success" href="" 
               hx-get="{% url 'school:level_add' %}?education={{ active_nav }}" 
               hx-target="#dialog">+ Ajouter classe</a>
        </div>
        {% if update_action %}
        <div class="btn-group shadow" title="Appuyer pour générer les fiches de table">
            <a id="submit-btn" hx-get="{% url update_action %}?education={{ active_nav }}" 
               hx-target="#progress_container" class="btn btn-info text-white"
               hx-on="htmx:beforeRequest: if(event.detail.target.id == 'progress_container') $('#submit-btn').prop('disabled', true);">
               <span data-feather="refresh-ccw" class="feather-16"></span> Générer
            </a>
        </div>
        {% endif %}
    </div>
    {% endif %}

    <div id="progress_container"></div>
    <div class="alert alert-info">
        <span data-feather="info" class="feather feather-16 align-middle"></span> 
        Option 1: Vous pouvez imprimer les fiches de table par lot de 14 élèves.
    </div>
    <div class="alert alert-warning">
        <span data-feather="info" class="feather feather-16 align-middle"></span> 
        Option 2: Cliquez sur générer (en haut à droite) pour générer toutes les fiches de table. <br>
        <span data-feather="info" class="feather feather-16 align-middle"></span> 
        Notez que cette opération peut prendre du temps selon l'effectif des élèves.
    </div>
 
    <div class="table-responsive">
        <table class="table table-striped table-sm" id="datatable">
            <thead class="bg-primary text-white">
            <tr>
                <th>Niveau</th>
                <th>Classe</th>
                <th>Effectif</th>
                <th>Par lot</th>
                <th>Par classe</th>
            </tr>
            </thead>
            <tbody>
            {% for level in level_list %}
                <tr>
                    <td class="align-middle">{{ level.generic_level }}</td>
                    <td class="align-middle">{{ level }}</td>
                    <td class="align-middle">{{ level.students }}</td>
                    <td class="align-middle">
                        <span class="feather-16 align-middle"></span>
                        {% if level.range_of_14 %}
                            {% for i in level.range_of_14|times %}
                            <a class="btn btn-sm btn-warning" target="_blank" href="{{ action_url }}?level={{ level.id }}&doc_type={{ doc_type }}&lot={{ i|add:1 }}">Lot {{ i|add:1 }}</a>
                            {% endfor %}
                        {% else %} - 
                        {% endif %}
                    </td>
                    <td>
                        {% if level.is_clean %}
                        <a class="btn btn-sm btn-warning" target="_blank" href="{% url 'exams:file_download' level.file_id %}">{{ level }}</a>
                        {% else %}
                        <a class="btn btn-sm btn-danger disabled" href="#">Actualiser</a>
                        {% endif %}
                    </td>
                </tr>
            {% endfor %}
            </tbody>
        </table>
    </div>
</div>