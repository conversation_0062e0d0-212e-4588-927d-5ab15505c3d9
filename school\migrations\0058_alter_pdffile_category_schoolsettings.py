# Generated by Django 5.0.2 on 2024-04-08 13:59

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('school', '0057_alter_pdffile_options_alter_pdffile_category_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='pdffile',
            name='category',
            field=models.CharField(choices=[('RS', 'Matrice des moyennes'), ('R2', 'Matrice des moyennes simplifiée'), ('RP', 'Relevé de notes'), ('RW', 'Relevé de notes avec données précédentes'), ('MS', 'Fiche de notation'), ('FT', 'Fiche de table'), ('CL', 'Liste de classe'), ('RG', "Liste d'appel"), ('DR', "Liste d'appel journalier")], max_length=2),
        ),
        migrations.CreateModel(
            name='SchoolSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('annual_average_formula', models.CharField(choices=[('E', 'Par langue'), ('D', 'Par la méthode: (ARABE + FRANCAIS) / 2')], default='E', max_length=1)),
                ('school', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='school.school')),
                ('year', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='school.year')),
            ],
            options={
                'unique_together': {('year', 'school')},
            },
        ),
    ]
