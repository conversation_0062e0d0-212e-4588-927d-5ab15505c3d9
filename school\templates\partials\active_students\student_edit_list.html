{% load static %}
{% load humanize %}
{% load widget_tweaks %}
<div class="row" id="edit_table">
    <div class="col">
        <div class="tile">
            <div class="alert alert-info">
                <span class="badge badge-info">NOUVEAU</span>
                Le mode édition vous permet de modifier/corriger les informations des élèves aisément et rapidement.
                Vous pouvez sélectionner les colonnes à afficher/masquer.
            </div>
            <div class="tile-title-w-btn">
                <div class="btn-group d-flex justify-content-between">
                    <!-- Existing buttons -->
                    <a class="btn btn-sm btn-primary" 
                            href="{% url 'school:active_students' %}">
                        {% if edit_mode %}
                        <i data-feather="x" class="align-middle"></i> Quitter mode édition
                        {% else %}
                        <i data-feather="edit-2" class="align-middle"></i> Mode édition
                        {% endif %}
                    </a>
                </div>
                <div class="form-inline mx-2">
                    <label class="mr-2">Niveau:</label>
                    <select class="form-control form-control-sm" 
                            name="level_filter"
                            hx-get="{{ request.path }}"
                            hx-target="#app-content"
                            hx-trigger="change">
                        <option value="">Tous les niveaux</option>
                        {% for level in generic_levels %}
                            <option value="{{ level.pk }}" 
                                    {% if request.GET.level_filter == level.pk|stringformat:"s" %}selected{% endif %}>
                                {{ level }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="dropdown">
                    <button class="btn btn-secondary btn-sm dropdown-toggle" type="button" data-toggle="dropdown">
                        <i data-feather="columns" class="align-middle"></i> Colonnes à modifier
                    </button>
                    <div class="dropdown-menu p-2">
                        <div class="form-check">
                            <input class="form-check-input column-toggle" type="checkbox" value="matricule" id="toggle-matricule" checked>
                            <label class="form-check-label" for="toggle-matricule">
                                Matricule
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input column-toggle" type="checkbox" value="nom" id="toggle-nom" checked>
                            <label class="form-check-label" for="toggle-nom">
                                Nom
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input column-toggle" type="checkbox" value="prenoms" id="toggle-prenoms" checked>
                            <label class="form-check-label" for="toggle-prenoms">
                                Prénoms
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input column-toggle" type="checkbox" value="sexe" id="toggle-sexe" checked>
                            <label class="form-check-label" for="toggle-sexe">
                                Sexe
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input column-toggle" type="checkbox" value="birth-day" id="toggle-birth-day" checked>
                            <label class="form-check-label" for="toggle-birth-day">
                                Jour de naissance
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input column-toggle" type="checkbox" value="birth-month" id="toggle-birth-month" checked>
                            <label class="form-check-label" for="toggle-birth-month">
                                Mois de naissance
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input column-toggle" type="checkbox" value="birth-year" id="toggle-birth-year" checked>
                            <label class="form-check-label" for="toggle-birth-year">
                                Année de naissance
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Existing search and filters -->
            <form id="student-table" hx-post="{% url 'school:student_edit_list' %}"
                hx-target="#edit_table"
                class="inline-edit-form"
                hx-on="htmx:beforeRequest: $('#save-btn').prop('disabled', true);
                       htmx:afterRequest: $('#save-btn').prop('disabled', false);"
                style="overflow-x: scroll;">
                {% csrf_token %}
                <table class="table table-striped table-sm table-hover table-bordered" style="font-size: 12px !important;">
                    <thead class="bg-primary text-white">
                        <tr>
                            <!-- <th class="align-middle">
                                <input type="checkbox" name="select-all" id="select-all">
                            </th> -->
                            {% if not edit_mode %}
                                <!-- Existing columns -->
                                <th style="min-width: 120px;" class="align-middle sticky-col sticky-header">Nom et Prénoms</th>
                                <th class="align-middle">Matricule</th>
                                <!-- Other existing columns -->
                            {% else %}
                                <th>Nom et Prénoms</th>
                                <th>Ancien Matricule</th>
                                <th>Classe</th>
                                <th style="width: 120px;" data-column="matricule">Matricule</th>
                                <th data-column="nom">Nom</th>
                                <th data-column="prenoms">Prénoms</th>
                                <th data-column="sexe">Sexe</th>
                                <th data-column="birth-day" style="width: 70px;">Jour</th>
                                <th data-column="birth-month">Mois</th>
                                <th data-column="birth-year" style="width: 80px;">Année</th>
                            {% endif %}
                        </tr>
                    </thead>
                    <tbody>
                        {% for enrollment in students %}
                        <tr id="student-row-{{ enrollment.student.id }}"
                        data-modified="false" 
                        class="position-relative">
                            <!-- <td>
                                <input type="checkbox" name="check-{{enrollment.id}}" id="check-{{enrollment.id}}" class="row-checkbox">
                            </td> -->
                            {% if not edit_mode %}
                                <!-- Existing row data -->
                                <td>{{ enrollment.student.get_full_name }}</td>
                                <td>{{ enrollment.student.student_id }}</td>
                                <!-- Other existing columns -->
                            {% else %}
                                    <td>{{ enrollment.student.get_full_name }}</td>
                                    <td>{{ enrollment.student.student_id|default:'-' }}</td>
                                    <td>{{ enrollment.level_fr|default:'-' }}</td>
                                    <td data-column="matricule">
                                        <input type="hidden" 
                                               name="student_id"
                                               class="form-control form-control-sm"
                                               value="{{ enrollment.pk }}"
                                               >
                                        <input type="text" 
                                               name="student[{{ enrollment.pk }}][new_matricule]"
                                               class="form-control form-control-sm"
                                               value="{{ enrollment.student.student_id|default:'' }}"
                                               >
                                    </td>
                                    <td data-column="nom">
                                        <input type="text" 
                                               name="student[{{ enrollment.pk }}][last_name]"
                                               class="form-control form-control-sm"
                                               value="{{ enrollment.student.last_name }}"
                                               >
                                    </td>
                                    <td data-column="prenoms"> 
                                        <input type="text" 
                                               name="student[{{ enrollment.pk }}][first_name]"
                                               class="form-control form-control-sm"
                                               value="{{ enrollment.student.first_name }}"
                                               >
                                    </td>
                                    <td data-column="sexe">
                                        <select name="student[{{ enrollment.pk }}][gender]" class="form-control form-control-sm">
                                            <option value="M" {% if enrollment.student.gender == 'M' %}selected{% endif %}>M</option>
                                            <option value="F" {% if enrollment.student.gender == 'F' %}selected{% endif %}>F</option>
                                        </select>
                                    </td>
                                    <td data-column="birth-day">
                                        <input type="number" 
                                            class="form-control form-control-sm" 
                                            name="student[{{ enrollment.pk }}][birth_day]" 
                                            id="birth_day" 
                                            value="{{ enrollment.student.birth_day|default:'' }}"
                                            min="1" 
                                            max="31">
                                    </td>
                                    <td data-column="birth-month">
                                        <select name="student[{{ enrollment.pk }}][birth_month]" class="form-control form-control-sm">
                                            <option value="1" {% if enrollment.student.birth_month == 1 %}selected{% endif %}>Janvier (1)</option>
                                            <option value="2" {% if enrollment.student.birth_month == 2 %}selected{% endif %}>Février (2)</option>
                                            <option value="3" {% if enrollment.student.birth_month == 3 %}selected{% endif %}>Mars (3)</option>
                                            <option value="4" {% if enrollment.student.birth_month == 4 %}selected{% endif %}>Avril (4)</option>
                                            <option value="5" {% if enrollment.student.birth_month == 5 %}selected{% endif %}>Mai (5)</option>
                                            <option value="6" {% if enrollment.student.birth_month == 6 %}selected{% endif %}>Juin (6)</option>
                                            <option value="7" {% if enrollment.student.birth_month == 7 %}selected{% endif %}>Juillet (7)</option>
                                            <option value="8" {% if enrollment.student.birth_month == 8 %}selected{% endif %}>Août (8)</option>
                                            <option value="9" {% if enrollment.student.birth_month == 9 %}selected{% endif %}>Septembre (9)</option>
                                            <option value="10" {% if enrollment.student.birth_month == 10 %}selected{% endif %}>Octobre (10)</option>
                                            <option value="11" {% if enrollment.student.birth_month == 11 %}selected{% endif %}>Novembre (11)</option>
                                            <option value="12" {% if enrollment.student.birth_month == 12 %}selected{% endif %}>Décembre (12)</option>
                                        </select>
                                    </td>
                                    <td data-column="birth-year">
                                        <input type="number" class="form-control form-control-sm" 
                                        name="student[{{ enrollment.pk }}][birth_year]" id="birth_year" 
                                        value="{{ enrollment.student.birth_year|default:'' }}"
                                        min="{{ current_year|add:'-20' }}" 
                                        max="{{ 2024 }}"
                                        >
                                    </td>
                            {% endif %}
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
                <div class="my-3">
                    <button type="submit" class="btn btn-success" id="save-btn">
                        <i data-feather="check" class="feather-16 align-middle"></i> Enregistrer
                    </button>
                </div>
                <!-- Existing pagination -->
                {% include 'partials/pagination.html' with include_items="[name=level_filter]" vals_items='{"statut": "{{active_nav}}" }' %}
            </form>
        </div>
    </div>
</div>

<script>
    // Existing scripts
    function initializeColumnVisibility() {
    // Load saved preferences
    const savedColumns = JSON.parse(localStorage.getItem('visibleColumns') || '{}');
    
    // Initialize checkboxes from saved preferences
    document.querySelectorAll('.column-toggle').forEach(checkbox => {
        const column = checkbox.value;
        if (savedColumns[column] !== undefined) {
            checkbox.checked = savedColumns[column];
        }
        updateColumnVisibility(column, checkbox.checked);
        
        // Re-attach change listeners
        checkbox.addEventListener('change', function() {
            const column = this.value;
            updateColumnVisibility(column, this.checked);
            
            // Save preferences
            const savedColumns = JSON.parse(localStorage.getItem('visibleColumns') || '{}');
            savedColumns[column] = this.checked;
            localStorage.setItem('visibleColumns', JSON.stringify(savedColumns));
        });
    });
};
document.addEventListener('DOMContentLoaded', initializeColumnVisibility);
document.addEventListener('htmx:afterSwap', initializeColumnVisibility);

function updateColumnVisibility(column, isVisible) {
    document.querySelectorAll(`[data-column="${column}"]`).forEach(cell => {
        cell.style.display = isVisible ? '' : 'none';
    });
}

if (typeof(feather) != "undefined") {
    feather.replace();
};
</script>