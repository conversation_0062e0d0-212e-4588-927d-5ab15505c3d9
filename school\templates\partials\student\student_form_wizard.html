{% extends 'partials/modal.html' %}
{% load i18n %}
{% load widget_tweaks %}

{% block modal_title %}
<p>Inscription > Etape {{ wizard.steps.step1 }} sur {{ wizard.steps.count }}</p>
{% endblock %}

{% block modal_body %}
  <table>
  {{ wizard.management_form }}
  {% if wizard.form.forms %}
    {{ wizard.form.management_form }}
    {% for form in wizard.form.forms %}
      {{ form }}
    {% endfor %}
  {% else %}
  <div class="stepwizard">
    <div class="stepwizard-row setup-panel">
        <div class="stepwizard-step col-xs-3"> 
            <a href="#step-1" type="button" class="btn {% if wizard.steps.step1 == 1 %} btn-success {% else %} btn-default disabled {% endif %} btn-circle">1</a>
            <p><small>Infos Elève</small></p>
        </div>
        <div class="stepwizard-step col-xs-3"> 
            <a href="#step-2" type="button" class="btn {% if wizard.steps.step1 == 2 %} btn-success {% else %} btn-default disabled {% endif %} btn-circle">2</a>
            <p><small>Parent, Scolarité et Classe</small></p>
        </div>
    </div>
</div>
    <div class="form-row">
      {% for field in wizard.form %}
      <div class="form-group 
          {% if field.label|lower == 'né le' or field.label|lower == 'mois' or field.label|lower == 'année' or field.name|lower == 'gender' or field.name|lower == 'enrollment_fees' or field.name|lower == 'year_fees' or field.name|lower == 'annexe_fees' or field.name|lower == 'enrollment_fee1' or field.name|lower == 'year_fee1' or field.name|lower == 'annexe_fee1'  %} col-4
          {% elif field.label|lower == 'statut aff/naff' or field.label|lower == 'qualité red/nred' or field.name|lower == 'generic_level_ar' or field.name|lower == 'generic_level_fr' or field.name|lower == 'level_fr' or field.name|lower == 'level_ar' or field.name|lower == 'full_name_ar' or field.name|lower == 'birth_place' or field.name|lower == 'birth_place_ar' or field.name|lower == 'father_phone' or field.name|lower == 'father' or field.name|lower == 'mother' %} col-6
          {% elif field.name|lower == 'nationality' %} col-8 
          {% else %} col-md-6 
          {% endif %} 
          {% if field.help_text == 'd-none' %} d-none
          {% elif user.school.education == 'F' %} 
            {% if field.name == 'level_ar' or field.name == 'birth_place_ar' %} d-none {% endif %} 
          {% endif %}">
          <label for="{{ field.id_for_label }}" class="{% if field.field.required %}font-weight-bold{% endif %}">{% if field.field.required %} * {% endif %} {% if field.name|lower == 'father_phone' %} Contact {% else %} {{ field.label }} {% endif %}</label>
          {% if field.label|lower == 'statut aff/naff' or field.name|lower == 'level_fr' or field.name|lower == 'level_ar' %}
            {% render_field field|add_class:"form-control"|attr:"hx-get:/versements/frais_scolarite/"|attr:"hx-target:#id_2-year_fees" hx-swap="outerHTML" %}
          {% elif field.name|lower == 'enrollment_fees' or field.name|lower == 'year_fees' or field.name|lower == 'annexe_fees' %}
            {% render_field field class='form-control' hx-swap="outerHTML" %}
          {% elif field.label|lower != 'statut aff/naff' and field.label|lower != 'qualité red/nred' %}
            {% render_field field class='form-control' onkeyup='this.value = this.value.toUpperCase();' %}
          {% else %}
            {% render_field field class='form-control' %}
          {% endif %}
          <span class="invalid-feedback">{{ field.errors|first }}</span>

          {% if field.label|lower == 'né le' %}
          <div class="text-danger">{{ wizard.form.non_field_errors|first }}</div>
          {% endif %}
      </div>
      {% endfor %}
    </div>
  {% endif %}
  </table>

{% endblock %}

{% block modal_footer %}

{% if wizard.steps.prev %}
<button name="wizard_goto_step" type="submit" 
        value="{{ wizard.steps.first }}" 
        class="btn btn-info">{% trans "Etape 1" %}</button>
<button name="wizard_goto_step" type="submit" value="{{ wizard.steps.prev }}" class="btn btn-info">{% trans "Etape Préc." %}</button>
{% endif %}
<div class="spinner-border d-none" role="status" id="spinner">
  <span class="sr-only">En cours...</span>
</div>
<input type="submit" class="btn btn-success" value="{% trans 'Valider' %}" id="submit-btn" />
{% endblock %}