{% load humanize %}
<div class="tile" id="tile" hx-get="{% url 'school:expenses' %}" hx-target="#app-content" hx-trigger="saved from:body">
    <div class="tile-title-w-btn d-flex justify-content-between">
        <div class="btn-group">
            <a class="btn btn-success" href="" 
               hx-get="{% url 'school:expense_add' %}" 
               hx-target="#dialog">+ Ajouter Dépense</a>
        </div>
        <div class="btn-group">
            <div class="dropdown dropleft" data-toggle="tooltip" data-placement="left" title="Filtrer les données">
                <button class="btn btn-sm dropdown {% if selected_category %}border shadow{% endif %}" style="background-color: inherit;" type="button" data-toggle="dropdown" aria-expanded="false">
                    <i data-feather="filter" class="feather-16"></i>
                </button>
                <div class="dropdown-menu">
                    {% for category, description in EXPENSE_TYPES %}
                        <a class="dropdown-item {% if selected_category == category|lower %}active{% endif %}" href="#"
                            hx-get="{% url 'school:expenses' %}?categorie={{ category|lower }}"
                            hx-push-url="{% url 'school:expenses' %}?categorie={{ category|lower }}"
                            hx-target="#app-content">{{ description }}</a>
                    {% endfor %}
                    <a class="dropdown-item" href="#"
                        hx-get="{% url 'school:expenses' %}"
                        hx-push-url="{% url 'school:expenses' %}"
                        hx-target="#app-content">Tous</a>
                </div>
            </div>
        </div>
    </div>
    <div class="table-responsive">
        <table class="table table-striped table-hover table-sm" id="datatable">
            <thead class="bg-primary text-white">
            <tr>
                <th style="min-width: 150px;">Motif</th>
                <th>Montant dépensé</th>
                <th>Date</th>
                <th>Actions</th>
            </tr>
            </thead>
            <tbody>
            {% for expense in expenses %}
            <tr>
                <td class="align-middle">{{ expense.get_expense_type_display }}</td>
                <td class="align-middle">{{ expense.amount|intcomma }}</td>
                <td class="align-middle">{{ expense.created_at|date:'d/m/Y' }}</td>
                <td class="align-middle">
                    <div class="dropdown">
                        <button class="btn btn-sm dropdown" style="background-color: inherit;" type="button" data-toggle="dropdown" aria-expanded="false">
                            <i data-feather="more-vertical" class="feather-16"></i>
                        </button>
                        <div class="dropdown-menu">
                          <a class="dropdown-item" href="#" hx-get="{% url 'school:expense_edit' expense.id %}" 
                             hx-target="#dialog">Modifier</a>
                        </div>
                    </div>
                </td>
            </tr>
            {% endfor %}
            </tbody>
            <tfoot class="bg-secondary text-white">
                <tr>
                    <td>TOTAL</td>
                    <td colspan="2">{% if total %} {{ total|intcomma }} {% else %} 0 {% endif %} F</td>
                    <td></td>
                </tr>
            </tfoot>
        </table>
    </div>
</div>