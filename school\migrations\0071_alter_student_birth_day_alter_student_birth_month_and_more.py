# Generated by Django 5.0.2 on 2024-06-17 07:31

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('school', '0070_alter_school_association'),
    ]

    operations = [
        migrations.AlterField(
            model_name='student',
            name='birth_day',
            field=models.PositiveSmallIntegerField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(31)], verbose_name='né le'),
        ),
        migrations.AlterField(
            model_name='student',
            name='birth_month',
            field=models.PositiveSmallIntegerField(blank=True, choices=[(1, 'Janvier'), (2, 'Février'), (3, 'Mars'), (4, 'Avril'), (5, 'Mai'), (6, 'Juin'), (7, '<PERSON><PERSON><PERSON>'), (8, 'A<PERSON>ût'), (9, 'Septembre'), (10, 'Octobre'), (11, 'Novembre'), (12, 'Déce<PERSON>')], null=True, verbose_name='mois'),
        ),
        migrations.AlterField(
            model_name='student',
            name='birth_year',
            field=models.PositiveSmallIntegerField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(1980)], verbose_name='année'),
        ),
    ]
