# Generated by Django 4.2.7 on 2023-11-13 13:37

import cloudinary.models
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('school', '0038_alter_teacherlevel_unique_together_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='school',
            name='header_img_secondary',
            field=cloudinary.models.CloudinaryField(blank=True, max_length=255, null=True, verbose_name='entête au secondaire'),
        ),
        migrations.AddField(
            model_name='school',
            name='name_secondary',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='nom au secondaire'),
        ),
        migrations.AlterField(
            model_name='school',
            name='director_ar',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='directeur arabe'),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name='school',
            name='director_fr',
            field=models.<PERSON><PERSON><PERSON><PERSON>(blank=True, max_length=255, null=True, verbose_name='directeur'),
        ),
        migrations.AlterField(
            model_name='school',
            name='founder',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='school.founder', verbose_name='fondateur'),
        ),
    ]
