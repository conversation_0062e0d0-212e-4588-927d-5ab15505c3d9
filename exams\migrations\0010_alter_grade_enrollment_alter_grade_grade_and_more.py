# Generated by Django 4.2.4 on 2023-08-16 18:23

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('school', '0028_alter_related_names_for_level_and_generic_level_fields'),
        ('exams', '0009_alter_grade_subject_alter_levelsubject_max'),
    ]

    operations = [
        migrations.AlterField(
            model_name='grade',
            name='enrollment',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='school.enrollment', verbose_name='élève'),
        ),
        migrations.AlterField(
            model_name='grade',
            name='grade',
            field=models.PositiveSmallIntegerField(blank=True, null=True, verbose_name='note'),
        ),
        migrations.AlterField(
            model_name='grade',
            name='school_term',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='exams.schoolterm', verbose_name='période'),
        ),
        migrations.AlterField(
            model_name='grade',
            name='subject',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='exams.levelsubject', verbose_name='matière'),
        ),
    ]
