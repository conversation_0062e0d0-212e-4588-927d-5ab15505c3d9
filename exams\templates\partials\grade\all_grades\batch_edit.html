{% load widget_tweaks %}

<form hx-post="{{request.path}}?level={{ level.id }}&student_id={{ student_id }}&term={{ term.id }}" method="post" 
    hx-on="
    htmx:beforeRequest: $('#submit-btn').prop('disabled', true); 
    htmx:afterRequest:  $('#submit-btn').prop('disabled', false); 
    "
    style="margin-bottom: 70px;" class="tile" hx-target="#app-content">
    {% csrf_token %}
    <div class="tile-title-w-btn d-flex justify-content-between" style="border-bottom: 1px solid #efefef;">
        <button type="button" id="next-btn" class="btn btn-secondary btn-sm"
        hx-get="{% url 'exams:grade_batch_edit' %}?level={{ level.id }}&term={{ term.id }}&student_id={{form.previous_student}}" 
        hx-target="#app-content" {% if not form.previous_student %} disabled="disabled" {% endif %}>
            <i data-feather="arrow-left"></i> <!-- Previous Icon -->
        </button>
        <span id="progression">{{ level }} / {{ term.term.abbreviation }} >></span> 
        <span class="badge badge-success" style="font-size: 20px !important;">{{ form.marked_index }}/{{ students_count }}</span>
    
        <button type="button" id="next-btn" class="btn btn-secondary btn-sm"
        hx-get="{% url 'exams:grade_batch_edit' %}?level={{ level.id }}&term={{ term.id }}&student_id={{form.next_student}}&previous_student={{ previous_student }}" 
        hx-target="#app-content" {% if not form.next_student %} disabled="disabled" {% endif %}>
            <i data-feather="arrow-right"></i> <!-- Next Icon -->
        </button>
    </div>
    <div class="form-row">
        {% for field in form %}
            <div class="form-group mb-2 {% if field.name == 'student_id' or field.name == 'full_name'  %} col-6 {% else %} col-lg-3 col-md-6 {% endif %}">
                <label for="{{ field.id_for_label }}" class="d-flex justify-content-between">
                <span>{{ field.label }}</span> <span>{{ field.help_text }}</span></label>
                {% render_field field class='form-control' %}
                <div class="text-danger">{{ field.errors }}</div>
            </div>
        {% endfor %}
    </div>
    
    <!-- Add Previous and Next Buttons with Feather Icons -->
    <div class="form-row mt-2">
        <div class="form-group col-md-12 d-flex justify-content-between">
            <button type="submit" id="submit-btn" class="btn btn-success btn-sm">
                <i data-feather="check" class="align-middle"></i> Enregistrer et continuer <!-- Save Icon -->
            </button>
        </div>
    </div>

</form>
{% load sweetify %} {% sweetify %}

<script>
    if (typeof(feather) !== 'undefined') {
      feather.replace(); 
    };
    
    var elementWithSetFocus = document.querySelector('[setfocus]');

        // Check if the element is found
    if (elementWithSetFocus) {
        // Call the focus method on the element
        elementWithSetFocus.focus();
        var val = elementWithSetFocus.value; //store the value of the element
        elementWithSetFocus.value = ''; //clear the value of the element
        elementWithSetFocus.value = val;
    }

    // const gradeInputs = document.querySelectorAll('input[name^="grade_"]');
    // // Add event listener for focus to each input element
    // gradeInputs.forEach(input => {
    // input.addEventListener('focus', function() {
    //     this.select();
    // });
    // });
</script>