# Generated by Django 5.0.1 on 2025-01-05 07:57

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('school', '0109_staffcard_job'),
    ]

    operations = [
        migrations.CreateModel(
            name='Subschool',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255, verbose_name='nom')),
                ('name_ar', models.CharField(blank=True, max_length=255, null=True, verbose_name='nom arabe')),
                ('cycle', models.CharField(choices=[('P', 'Primaire'), ('S', 'Secondaire'), ('B', '<PERSON> (Groupe Scolaire)')], default='B', max_length=1)),
                ('school', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='school.school')),
            ],
            options={
                'verbose_name': 'sous-école',
                'verbose_name_plural': 'sous-écoles',
                'ordering': ['school', 'name'],
            },
        ),
    ]
