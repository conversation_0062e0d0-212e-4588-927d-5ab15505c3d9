import requests
from django.conf import settings
from django.utils.http import urlencode

def send_sms(session_key, to, message):
    params = urlencode({
        'apikey': settings.SMS_API_KEY,
        'apitoken': settings.SMS_API_TOKEN,
        'type': 'sms',
        'from': settings.SMS_SENDER_ID,
        'to': to,
        'text': message,
    })
    session_id = session_key
    headers = {
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'Accept-Encoding': 'gzip, deflate, br, zstd',
    'Accept-Language': 'fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7',
    'Cookie': f'SESSIONID={session_id}',
    'Sec-Fetch-Dest': 'document',
    'Sec-Fetch-Mode': 'navigate',
    'Sec-Fetch-Site': 'none',
    'Sec-Fetch-User': '?1',
    'Upgrade-Insecure-Requests': '1',
    'User-Agent': 'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36'
    }
    
    url = f'https://panel.smsing.app/smsAPI?sendsms&{params}'
    response = requests.get(url, headers=headers)
    print('Sending SMS. Sent ?:', response.status_code == 200)
    return response.status_code == 200