
<div class="tile" id="tile">
    <form method="post" 
            hx-get="{% url 'exams:grade_by_subject' %}?lang={{ level.education }}&short_name={{ level.generic_level.id }}&level={{ level.id }}&type=matiere"
        hx-include="[name=term], [name=subject]"
        hx-target="#container"
        hx-trigger="submit, saved from:body"
        class="no-hiding">
        {% csrf_token %}

        <div class="row">
            <div class="col-6 form-group" id="term_container">
                <label for="">Période</label>
                <select name="term" id="id_term" class="form-control" 
                        required="required" hx-swap="outerHTML">
                    {% for term in terms %}
                        {% if term.allow_marking %}
                            <option value="{{ term.id }}">{{ term }}</option>
                        {% endif %}
                    {% endfor %}
                </select>
            </div>
            
            <div class="col-6 form-group" id="subject_container">
                <label for="">Matière</label>
                <select name="subject" id="id_subject" class="form-control" hx-swap="outerHTML">
                    {% for subject in subjects %}
                            <option value="{{ subject.id }}">{{ subject }}</option>
                    {% endfor %}
        
                </select>
            </div>
        </div>
        <div class="row px-3 d-flex justify-content-between">
            <button type="submit" class="btn btn-success btn-sm">Valider</button>
            <div class="dropdown">
                <button class="btn btn-warning btn-sm dropdown-toggle" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <span data-feather="chevrons-down" class="feather-16 align-middle"></span> Noter les élèves
                </button>
                <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                    <a type="submit" class="dropdown-item" 
                        hx-get="{% url 'exams:grade_batch_edit' %}?level={{ level.id }}" 
                        hx-target="#app-content" hx-include="[name=term], [name=level]">
                        <span data-feather="refresh-cw" class="feather-16 align-middle"></span>
                        Commencer
                    </a>
                    <a type="submit" class="dropdown-item" 
                        hx-get="{% url 'exams:grade_batch_edit' %}?level={{ level.id }}&continue" 
                        hx-target="#app-content" hx-include="[name=term], [name=level]">
                        <span data-feather="arrow-right" class="feather-16 align-middle"></span>
                        Continuer
                    </a>
                </div>
            </div>
        </div>
    </form>
    <form method="post" class="table-responsive container mt-3" id="container" 
                action="{% url 'exams:grade_export' %}">
    </form>
</div>

{% load sweetify %} {% sweetify %}