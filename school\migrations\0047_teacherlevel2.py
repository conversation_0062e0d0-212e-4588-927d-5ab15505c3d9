# Generated by Django 4.2.7 on 2023-12-02 12:19

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('exams', '0047_alter_subject_code'),
        ('school', '0046_alter_teacherlevel_unique_together'),
    ]

    operations = [
        migrations.CreateModel(
            name='TeacherLevel2',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_main_teacher', models.BooleanField(default=False, verbose_name='Est professeur principal (PP)')),
                ('level', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='school.level')),
                ('subjects', models.ManyToManyField(blank=True, to='exams.levelsubject')),
                ('teacher', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='school.teacher')),
            ],
            options={
                'verbose_name': 'Attribution de classe',
                'verbose_name_plural': 'Attributions de classe',
                'unique_together': {('teacher', 'level')},
            },
        ),
    ]
