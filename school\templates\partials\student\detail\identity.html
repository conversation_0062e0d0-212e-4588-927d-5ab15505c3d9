{% load humanize %}
<div class="py-3 tile" hx-get="{{ request.path }}" hx-target="#app-content" hx-trigger="saved from:body">
  <div class="row">
    <div class="col-md-2 text-center border rounded bg-light px-0 pt-2">
      <!-- Student Photo -->
        {% if enrollment.student.photo %}
        <img src="{{ enrollment.student.photo.url }}" 
                alt="Photo de l'élève" loading="lazy" height="150px"
                class="rounded">
        {% elif enrollment.is_second_cycle_fr and enrollment.student.student_id %}
            <img src="{{ enrollment.student.government_photo }}" 
                alt="Photo de l'élève" loading="lazy" height="150px"
                id="{{ enrollment.id }}"
                onload="if (this.src.endsWith('CC')) {
                  this.src = '{{ enrollment.student.blank_photo }}'
                }" class="rounded">
          {% else %}
          <img src="{{ enrollment.student.blank_photo }}" 
                alt="Photo de l'élève" loading="lazy" height="150px">
          {% endif %}
    </div>
    <div class="col-md-5 mt-2">
      <!-- Student Details -->
      <h5>Informations sur l'élève</h5>
      <table class="table table-striped table-sm">
        <tbody>
          <tr>
            <th>Identifiant EcolePro:</th>
            <td>{{ enrollment.student.identifier }}</td>
          </tr>
          <tr>
            <th>Matricule DSPS:</th>
            <td>{{ enrollment.student.student_id|default_if_none:'-' }}</td>
          </tr>
          <tr>
            <th>Nom:</th>
            <td>{{ enrollment.student.last_name }}</td>
          </tr>
          <tr>
            <th>Prénoms:</th>
            <td>{{ enrollment.student.first_name }}</td>
          </tr>
          <tr>
            <th>Sexe:</th>
            <td>{{ enrollment.student.get_gender_display }}</td>
          </tr>
          {% if user.school.education == EDUCATION_ARABIC %}
          <tr>
            <th>Nom et Prénoms en arabe:</th>
            <td>{{ enrollment.student.full_name_ar|default_if_none:'-' }}</td>
          </tr>
          {% endif %}
          <tr>
            <th>Né(e) le:</th>
            <td>{{ enrollment.student.birth_date_str }}</td>
          </tr>
          <tr>
            <th>Lieu de naissance:</th>
            <td>{{ enrollment.student.birth_place|default_if_none:'N/A' }}</td>
          </tr>
          {% if user.school.education == EDUCATION_ARABIC %}
          <tr>
            <th>Lieu de naissance en arabe:</th>
            <td>{{ enrollment.student.birth_place_ar|default_if_none:'-' }}</td>
          </tr>
          {% endif %}
          <tr>
            <th>Statut:</th>
            <td>{{ enrollment.get_status_display }}</td>
          </tr>
          <tr class="{% if enrollment.qualite == 'Red' %}text-danger{% endif %}">
            <th>Qualité:</th>
            <td>{{ enrollment.get_qualite_display }}</td>
          </tr>
          <tr>
            <th>Contact:</th>
            <td>
              {% if enrollment.student.phone %} 
                {{ enrollment.student.phone }} 
              {% else %}
                {{ enrollment.student.father_phone|default_if_none:'N/A' }} 
              {% endif %}</td>
          </tr>
          <tr>
            <th>Père:</th>
            <td>{{ enrollment.student.father|default_if_none:'N/A' }}</td>
          </tr>
          <tr>
            <th>Mère:</th>
            <td>{{ enrollment.student.mother|default_if_none:'N/A' }}</td>
          </tr>
        </tbody>
      </table>
    </div>
    <div class="col-md-5 mt-2">
      <h5>Fréquentation {{ active_year }}</h5>
      <table class="table table-striped table-sm">
        <tbody>
          <tr>
            <th>Niveau actuel:</th>
            <td>{{ enrollment.generic_level_fr }}</td>
          </tr>
          <tr>
            <th>Classe actuelle:</th>
            <td>{{ enrollment.level_fr|default_if_none:'Non-Attribuée' }}</td>
          </tr>
          {% if user.school.education == EDUCATION_ARABIC %}
            <tr>
              <th>Niveau arabe actuel:</th>
              <td>{{ enrollment.generic_level_fr }}</td>
            </tr>
            <tr>
              <th>Classe arabe  actuelle:</th>
              <td>{{ enrollment.level_fr|default_if_none:'Non-Attribuée' }}</td>
            </tr>

          {% endif %}
        </tbody>
      </table>
      </div>
      </div>
      <div class="d-flex justify-content-around">
        <a href="" class="btn btn-primary show-on-phone" hx-get="{% url 'school:student_edit_wizard' enrollment.id %}" hx-target="#dialog">
          <span data-feather="edit" class="feather-16 align-middle"></span> Modifier</a>
        <a href="" class="btn btn-primary show-on-pc" hx-get="{% url 'school:student_edit' enrollment.id %}" hx-target="#dialog-xl">
          <span data-feather="edit" class="feather-16 align-middle"></span> Modifier</a>
      </div>
</div>