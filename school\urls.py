from django.urls import path
from . import views

app_name = 'school'
urlpatterns = [
    path('tableau-de-bord/', views.HomePageView.as_view(), name='home'),
    path('api/chart-data/', views.ChartDataAPIView.as_view(), name='chart_data_api'),
    path('', views.LandingPageView.as_view(), name='landing'),
    path('inscriptions/', views.StudentsListView.as_view(), name='students'),
    path('inscriptions/ajouter/', views.student_form_view, name='student_add'),
    path('inscriptions/par-etape/ajouter/', views.StudentFormWizardView.as_view(), name='student_add_wizard'),
    path('inscriptions/<int:pk>/changer_statut/', views.StudentStatusChangeView.as_view(), name='student_status_change'),
    path('inscriptions/<int:pk>/editer/', views.student_form_view, name='student_edit'),
    path('inscriptions/par-etape/<int:pk>/editer/', views.StudentFormWizardView.as_view(), name='student_edit_wizard'),
    path('inscriptions/<int:pk>/releve-de-paiement/', views.student_payments_report_pdf, name='student_payments_pdf'),
    path('inscriptions/<int:pk>/supprimer/', views.EnrollmentDeleteView.as_view(), name='student_delete'),
    path('inscriptions/<int:enrollment_id>/fiche_scolarite/', views.fiche_scolarite_pdf, name='fiche_scolarite_pdf'),
]

urlpatterns += [
    path('transliterate/', views.transliterate_names_view, name='transliterate'),
]

urlpatterns += [
    path('versements/', views.PaymentsListView.as_view(), name='payments'),
    path('versements/ajouter/', views.PaymentCreateView.as_view(), name='payment_add'),
    path('versements/<int:pk>/editer/', views.PaymentsUpdateView.as_view(), name='payment_edit'),
    path('versements/<int:pk>/supprimer/', views.PaymentDeleteView.as_view(), name='payment_delete'),
    path('versements/frais_scolarite/', views.get_scolarite_fee, name='scolarite_fee'),
    path('versements/rapports-pdf/', views.payments_list_pdf, name='payments_list_pdf'),
]

finance_url = 'scolarite'
urlpatterns += [
    path(f'{finance_url}/', views.LevelPricingListView.as_view(), name='pricing'),
    path(f'{finance_url}/ajouter/', views.LevelPricingCreateView.as_view(), name='pricing_add'),
    path(f'{finance_url}/ajouter-par-cycle/', views.CyclePricingCreateView.as_view(), name='cycle_pricing_add'),
    path(f'{finance_url}/<int:pk>/editer/', views.LevelPricingUpdateView.as_view(), name='pricing_edit'),
    path(f'{finance_url}/<int:pk>/frais-annexes/', views.extra_price_view, name='extra_pricing'),
    path(f'{finance_url}/rubriques/', views.PricingCategoryListView.as_view(), name='pricing_categories'),
    path(f'{finance_url}/rubriques/ajouter/', views.PricingCategoryCreateView.as_view(), name='pricing_category_create'),
    path(f'{finance_url}/rubriques/<int:pk>/editer/', views.PricingCategoryUpdateView.as_view(), name='pricing_category_edit'),
    path(f'{finance_url}/rapports/', views.PaymentsSummaryListView.as_view(), name='reports'),
    path(f'{finance_url}/rapports-periode/', views.period_payments_view, name='period_reports'),
    path(f'{finance_url}/rapports-classe/', 
         views.PaymentsSummaryByLevelListView.as_view(), 
         name='level_reports'),
    path(f'{finance_url}/rapports-classe/pdf/', 
         views.payments_list_pdf_by_class, 
         name='level_payments_pdf'),
]

urlpatterns += [
    path(f'caisse/', views.BalanceNavView.as_view(), name='balance'),
    path(f'caisse/depenses/', views.ExpensesListView.as_view(), name='expenses'),
    path(f'caisse/depenses/ajouter/', views.ExpenseCreateView.as_view(), name='expense_add'),
    path(f'caisse/depenses/<int:pk>/editer/', views.ExpenseUpdateView.as_view(), name='expense_edit'),
]

levels_url = 'classes'
urlpatterns += [
    path(f'{levels_url}/', views.LevelsListView.as_view(), name='levels'),
    path(f'{levels_url}/effectifs/', views.ActiveStudentsLevelStatisticsView.as_view(), name='levels_statistics'),
    path(f'{levels_url}/effectifs/exporter/', views.levels_export_view, name='levels_stats_export'),
    path(f'{levels_url}/ajouter/', views.LevelCreateView.as_view(), name='level_add'),
    path(f'{levels_url}/<int:pk>/modifier/', views.LevelUpdateView.as_view(), name='level_edit'),
    path(f'{levels_url}/<int:level_id>/liste-pdf/', views.students_list_pdf, name='level_list_pdf'),
    path(f'{levels_url}/attribuer/', views.level_attribution_view, name='attribute_level'),
    path(f'{levels_url}/matieres/', views.level_subjects_view, name='level_subjects'),
    path('classes-du-niveau/', views.sublevels_view, name='sublevels'),
    path(f'{levels_url}/ajouter-plusieurs/', views.LevelBatchCreateWizard.as_view(), name='level_batch_create'),
    path('classes-du-subschool/', views.subschool_levels_view, name='subschool_levels'),
]

students_url = 'eleves'
urlpatterns += [
    path(f'{students_url}/', views.ActiveStudentsListView.as_view(), name='active_students'),
    path(f'{students_url}/<int:pk>/detail/', views.StudentDetailView.as_view(), name='student_detail'),
    path(f'{students_url}/listes-de-classe/', 
         views.ActiveStudentsLevelListView.as_view(), 
         name='students_level_list'),
    path(f'{students_url}/attribution-de-classe/', views.LevelAttributionView.as_view(), 
         name='students_level_attribution'),
    path(f'{students_url}/photos/', views.photos_view, name='students_photos'),
    path(f'{students_url}/<int:student_id>/importer-photo/', views.photo_import_view, name='photo_import'),
    path(f'{students_url}/<int:student_id>/url-photo/', views.get_photo_url, name='photo_url'),
    path(f'{students_url}/edit/', views.StudentEditListView.as_view(), name='student_edit_list')
    
]

teachers_url = 'enseignants'
urlpatterns += [
    path(f'{teachers_url}/', views.TeachersListView.as_view(), name='teachers'),
    path(f'{teachers_url}/ajouter/', views.TeacherCreateView.as_view(), name='teacher_add'),
    path(f'{teachers_url}/<int:pk>/editer/', views.TeacherUpdateView.as_view(), name='teacher_edit'),
    path(f'{teachers_url}/<int:pk>/attribuer-cours/', views.TeacherLevelCreateView.as_view(), name='teacher_level_add'),
    path(f'{teachers_url}/<int:pk>/editer-attribution-de-cours/', 
         views.TeacherLevelUpdateView.as_view(), name='teacher_level_edit'),
    path(f'{teachers_url}/<int:pk>/annuler-attribution-de-cours/', 
         views.TeacherLevelDeleteView.as_view(), name='teacher_level_delete'),
    path(f'{teachers_url}/notes/', 
         views.TeacherStudentsView.as_view(), name='teacher_subjects_select')
]

urlpatterns += [
    path('import/', views.import_view, name='import'),
    path('import/student-template/', views.download_student_import_model, 
         name='student_import_model'),
    path('exportation/', views.custom_export_view, name='custom_export'),
]

urlpatterns += [
    path('exportations/fichiereleves/', 
         views.students_export_view, name='students_export'),
]

urlpatterns += [
    path('ecoles/ajouter/', views.SchoolCreationView.as_view(), name='test_school')
]

urlpatterns += [
    path('actions/eleves/', views.student_page_action, name='student_page_action')
]

urlpatterns += [
    path('parametres/', views.SchoolSettingsView.as_view(), name='settings'),
]

urlpatterns += [
    path('notifications/sms/solde/', views.SchoolSMSBalanceView.as_view(), name='sms_balance'),
]

urlpatterns += [
    path('rh/personnel/', views.StaffListView.as_view(), name='staff'),
    path('rh/personnel/ajouter/', views.StaffCreateView.as_view(), name='staff_add'),
    path('rh/personnel/<int:pk>/editer/', views.StaffUpdateView.as_view(), name='staff_edit'),
    path('rh/emplois/', views.StaffRoleListView.as_view(), name='staff_roles'),
    path('rh/emplois/ajouter', views.StaffRoleCreateView.as_view(), name='staff_role_create'),
    path('rh/emplois/<int:pk>/editer/', views.StaffRoleUpdateView.as_view(), name='staff_role_edit'),
    path('rh/rubriques-de-paie/', views.PaymentOptionsListView.as_view(), name='payment_options'),
    path('rh/rubriques-de-paie/ajouter/', views.PaymentOptionCreateView.as_view(), name='payment_option_add'),
    path('rh/rubriques-de-paie/<int:pk>/editer/', views.PaymentOptionUpdateView.as_view(), name='payment_option_edit'),
    path('rh/paiements/decision/ajouter/', views.SalaryPaymentDecisionCreateView.as_view(), name='staff_salary_decision_add'),
    path('rh/paiements/', views.StaffSalaryForMonthListView.as_view(), name='staff_payments'),
    path('rh/paiements/ajouter/', views.StaffSalaryPaymentCreateView.as_view(), name='staff_payment_add'),
    path('rh/paiements/bulletin-de-paie/', views.staff_salary_report, name='staff_payment_report'),
    # path('rh/bagdes/', views.StaffCardListView.as_view(), name='staff_badges'),
    path('rh/bagdes/nouveau/', views.StaffCardCreateView.as_view(), name='staff_card_create'),
    path('rh/bagdes/<int:pk>/editer/', views.StaffCardUpdateView.as_view(), name='staff_card_edit'),
]
