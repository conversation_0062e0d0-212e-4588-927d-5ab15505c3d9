# Generated by Django 5.1 on 2024-10-19 16:27

import cloudinary.models
import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('school', '0099_message_sms_type'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='StaffRole',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('code', models.CharField(max_length=10, unique=True)),
                ('choice_code', models.CharField(blank=True, max_length=10, null=True, unique=True)),
                ('school', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='school.school')),
            ],
            options={
                'verbose_name': 'emploi du personnel',
                'verbose_name_plural': 'emplois du personnel',
            },
        ),
        migrations.CreateModel(
            name='Staff',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('last_name', models.CharField(max_length=255, verbose_name='nom')),
                ('first_name', models.CharField(max_length=255, verbose_name='prénom')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='email')),
                ('education', models.CharField(choices=[('A', 'Arabe'), ('F', 'Français')], default='F', max_length=1)),
                ('gender', models.CharField(choices=[('M', 'Masculin'), ('F', 'Féminin')], default='M', max_length=1, verbose_name='sexe')),
                ('birth_day', models.PositiveSmallIntegerField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(31)], verbose_name='né le')),
                ('birth_month', models.PositiveSmallIntegerField(blank=True, choices=[(1, '1- Janvier'), (2, '2- Février'), (3, '3- Mars'), (4, '4- Avril'), (5, '5- Mai'), (6, '6- Juin'), (7, '7- Juillet'), (8, '8- Août'), (9, '9- Septembre'), (10, '10- Octobre'), (11, '11- Novembre'), (12, '12- Décembre')], null=True, verbose_name='mois')),
                ('birth_year', models.PositiveSmallIntegerField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(1920)], verbose_name='année')),
                ('birth_place', models.CharField(blank=True, max_length=255, null=True, verbose_name='lieu de naissance')),
                ('phone', models.CharField(blank=True, help_text='10 caractères sans espace', max_length=10, null=True, verbose_name='contact principal')),
                ('phone2', models.CharField(blank=True, max_length=30, null=True, verbose_name='autre contacts')),
                ('id_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='N° CNI/Att')),
                ('photo', cloudinary.models.CloudinaryField(blank=True, max_length=255, null=True, verbose_name="photo d'identité")),
                ('parent', models.CharField(blank=True, max_length=255, null=True, verbose_name="personne à contacter en cas d'urgence")),
                ('parent_phone', models.CharField(blank=True, max_length=255, null=True, verbose_name="contact d'urgence")),
                ('salary', models.PositiveIntegerField(default=75000, verbose_name='salaire')),
                ('cnps', models.CharField(blank=True, max_length=50, null=True, verbose_name='N° CNPS')),
                ('school', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='school.school', verbose_name='école')),
                ('teacher', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='school.teacher')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('role', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='school.staffrole', verbose_name='emploi')),
            ],
            options={
                'verbose_name': 'Personnel',
                'verbose_name_plural': 'Personnel',
                'ordering': ['last_name', 'first_name'],
            },
        ),
    ]
