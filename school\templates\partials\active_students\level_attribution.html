{% load widget_tweaks %}
{% load humanize %}

<div method="post" action="" class="dashboard-wrapper" hx-include="this" hx-get="{{ request.path }}?page={{ page }}{% if education %}&education={{ education}}{% endif %}" hx-trigger="saved from:body" hx-target="#app-content">
  {% include 'partials/active_students/filter_section.html' %}

  <!-- Table Section -->
  <div id="results-area"> 
    <div class="table-card">
      <form class="table-responsive" style="max-height: 500px; overflow: auto;">
        {% with education=user.school.education %}
        <table class="table table-striped mb-0" style="font-size: 11.5px;">
          <thead>
            <tr>
              <th width="40">
                <div class="checkbox-wrapper">
                  <input type="checkbox" id="select-all" class="custom-checkbox">
                  <span class="checkmark" id="select-all-checkmark"></span>
                </div>
              </th>
              <th class="sticky-column sticky-header sortable {% if sort_field == 'student__last_name' %}sort-active{% endif %}"
                  title="Cliquer pour ordonner"
                  hx-get="{{ request.path }}" 
                  hx-include=".form-control"
                  hx-vals='{"sort": "{% if sort_field == "student__last_name" and sort_direction == "asc" %}-{% endif %}student__last_name"}'>
                Nom et Prénoms
                <i data-feather="chevron-{% if sort_field == 'student__last_name' %}{% if sort_direction == 'asc' %}up{% else %}down{% endif %}{% else %}down{% endif %}" 
                   class="sort-icon feather-14"></i>
              </th>
              {% if is_arabic_school %}
              <th class="sticky-column sticky-header sortable {% if sort_field == 'student__last_name' %}sort-active{% endif %}"
                  title="Cliquer pour ordonner"
                  hx-get="{{ request.path }}" 
                  hx-include=".form-control"
                  hx-vals='{"sort": "{% if sort_field == "student__full_name_ar" and sort_direction == "asc" %}-{% endif %}student__full_name_ar"}'>
                Nom en arabe
                <i data-feather="chevron-{% if sort_field == 'student__full_name_ar' %}{% if sort_direction == 'asc' %}up{% else %}down{% endif %}{% else %}down{% endif %}" 
                   class="sort-icon feather-14"></i>
              </th>
              {% endif %}
              <th class="sortable {% if sort_field == 'student__student_id' %}sort-active{% endif %}"
                  title="Cliquer pour ordonner"  
                  hx-get="{{ request.path }}"
                  hx-include=".form-control"
                  hx-vals='{"sort": "{% if sort_field == "student__student_id" and sort_direction == "asc" %}-{% endif %}student__student_id"}'>
                Matricule
                <i data-feather="chevron-{% if sort_field == 'student__student_id' %}{% if sort_direction == 'asc' %}up{% else %}down{% endif %}{% else %}down{% endif %}"
                   class="sort-icon feather-14"></i>
              </th>
              <th>Sexe</th>
              <th>Niveau</th>
              {% if not request.GET.education or request.GET.education == 'fr' %}
              <th class="sortable {% if sort_field == 'level_fr' %}sort-active{% endif %}"
                  title="Cliquer pour ordonner"    
                  hx-get="{{ request.path }}"
                  hx-include=".form-control"  
                  hx-vals='{"sort": "{% if sort_field == "level_fr" and sort_direction == "asc" %}-{% endif %}level_fr"}'>
                Classe
                <i data-feather="chevron-{% if sort_field == 'level_fr' %}{% if sort_direction == 'asc' %}up{% else %}down{% endif %}{% else %}down{% endif %}"
                   class="sort-icon feather-14"></i>
              </th>
              {% else %}
              <th class="sortable {% if sort_field == 'level_ar' %}sort-active{% endif %}"
                  title="Cliquer pour ordonner"
                  hx-get="{{ request.path }}"
                  hx-include=".form-control"
                  hx-vals='{"sort": "{% if sort_field == "level_ar" and sort_direction == "asc" %}-{% endif %}level_ar"}'>
                Arabe
                <i data-feather="chevron-{% if sort_field == 'level_ar' %}{% if sort_direction == 'asc' %}up{% else %}down{% endif %}{% else %}down{% endif %}"
                   class="sort-icon feather-14"></i>
              </th>
              {% endif %}
              <th class="sortable {% if sort_field == 'paid' %}sort-active{% endif %}"
              title="Cliquer pour ordonner"
              hx-get="{{ request.path }}"
              hx-include=".form-control"  
              hx-vals='{"sort": "{% if sort_field == "age" and sort_direction == "asc" %}-{% endif %}age"}'>Né(e) le
                <i data-feather="chevron-{% if sort_field == 'age' %}{% if sort_direction == 'asc' %}up{% else %}down{% endif %}{% else %}down{% endif %}"
                   class="sort-icon feather-14"></i>
              </th>
              <th>Age</th>
              <th>Statut</th>
              <th class="sortable {% if sort_field == 'created_at' %}sort-active{% endif %}"
                hx-get="{{ request.path }}"
                title="Cliquer pour ordonner"
                hx-include=".form-control"  
                hx-vals='{"sort": "{% if sort_field == "created_at" and sort_direction == "asc" %}-{% endif %}created_at"}'>Inscrit le
                <i data-feather="chevron-{% if sort_field == 'created_at' %}{% if sort_direction == 'asc' %}up{% else %}down{% endif %}{% else %}down{% endif %}"
                   class="sort-icon feather-14"></i>
              </th>
            </tr>
          </thead>
          <tbody>
            {% for enrollment in enrollments %}
            <tr class="student-row {% if enrollment.selected %} selected {% endif %}" >
              <td>
                <div class="checkbox-wrapper">
                  <input type="checkbox" 
                         name="check-{{enrollment.id}}" 
                         id="check-{{enrollment.id}}" 
                         class="row-checkbox custom-checkbox"
                         {% if enrollment.selected %} checked="checked" {% endif %}> 
                  <span class="checkmark"></span>
                </div>
              </td>
              <td class="sticky-column">
                <a href="" hx-get="{% url 'school:student_detail' enrollment.pk %}" class="student-name linkEffect linkEffect--insideOut">{{ enrollment.student.get_full_name }}</a>
              </td>
              {% if is_arabic_school %}
              <td>
                {{ enrollment.student.full_name_ar|default:'-' }}
              </td>
              {% endif %}
              <td>{{ enrollment.student.student_id|default:"-" }}</td>
              <td>{{ enrollment.student.gender }}</td>
              
              {% if not request.GET.education or request.GET.education == 'fr' %}
              <td>{{ enrollment.generic_level_fr|default:'-' }}<span class="text-muted"></span></td>
              <td>{{ enrollment.level_fr|default:'' }} {% if not enrollment.level_fr %}<span class="text-muted" title="Classe non attribuée" data-toggle="tooltip">?</span> {% endif %}</td>
              {% else %}
              <td>{{ enrollment.generic_level_ar|default:'-' }}<span class="text-muted"></span></td>
              <td>{{ enrollment.level_ar|default:'' }} {% if not enrollment.level_ar %}<span class="text-muted" title="Classe arabe non attribuée" data-toggle="tooltip">?</span>{% endif %}</td>
              {% endif %}
              <td>
                {% if enrollment.student.birth_date_str %}
                  {{ enrollment.student.birth_date_str|default:'-'}}
                {% else %}
                  -
                {% endif %}
              </td>
              <td>
                <span class="text-muted">{% if enrollment.age %} {{ enrollment.age }} ans {% else %} - {% endif %}</span>
              </td>
              <td>
                {% if enrollment.status %}
                <span class="status-badge {% if enrollment.active %}status-active{% else %}status-inactive{% endif %}">
                  {{ enrollment.status }}
                </span>
                {% else %}
                -
                {% endif %}
              </td>
              <td class="text-muted">{{ enrollment.created_at|date:'d/m/Y' }}</td>
            </tr>
            {% endfor %}
          </tbody>
          <!-- <tfoot>
            <tr class="font-weight-bold bg-light">
              <td colspan="8" class="text-right">
              </td>
              <td class="text-right amount-cell amount-negative px-2">
                {{ total_debt|default:0|intcomma }}
              </td>
              <td class="text-right amount-cell px-2">
                {{ total_amount|default:0|intcomma }}
              </td>
              <td class="text-right amount-cell amount-positive">
                {{ total_paid|default:0|intcomma }}
              </td>
              <td class="text-right amount-cell px-2">
                {{ total_remaining|default:0|intcomma }}
              </td>
              <td></td>
            </tr>
          </tfoot> -->
        </table>
        {% endwith %}
        {% include 'partials/active_students/level_attribution_actions.html' %}

      </form>
    </div>
  </div>

  <!-- Pagination -->
  <div class="d-flex justify-content-between align-items-center mt-2">
    <select class="form-control form-control-sm" 
            style="width: auto"
            name="per_page"
            hx-get="{{ request.path }}"
            hx-target="#app-content">
      <option value="50" {% if per_page == '50' %} selected {% endif %}>50</option>
      <option value="100" {% if per_page == '100' %} selected {% endif %}>100</option>
    </select>
    
    {% include 'partials/pagination.html' with include_items='.form-control' %}
  </div>
</div>
{% include 'partials/active_students/students_list_js.html' %}