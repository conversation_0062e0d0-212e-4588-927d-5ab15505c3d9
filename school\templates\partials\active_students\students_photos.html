<div class="tile">
    {% if not show_latest %}
    <form action="" class="mb-3" hx-get="{{ request.path }}?education={{education}}" 
          hx-target="#table-container" 
          hx-push-url="{{ request.path }}?education={{education}}">
          
        <div class="form-row">
            <div class="col-6 form-group">
                <label for="">Niveau</label>
                <select name="short_name" id="generic_level" class="form-control" 
                        hx-get="{% url 'school:sublevels' %}?lang={{education}}"
                        hx-target="#level_container">
                    <option value="">--------</option>
                    {% for level in generic_levels %}
                    <option value="{{ level.id }}">{{ level }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-6 form-group" id="level_container">
                <label for="">Classe</label>
                <select name="level" id="level" class="form-control"></select>
            </div>
        </div>
        <button type="submit" class="btn btn-success">Afficher la liste</button>
    </form>
    {% endif %}
    <form id="table-container">
        {% if show_latest %}
            <p class="alert alert-info mb-3">
                > Liste des 10 élèves inscrits/édités récemments.
            </p>
            {% include 'partials/active_students/photos_table.html' with enrollments=enrollments show_latest=show_latest %}
        {% endif %}
    </form>
</div>

<script>
    document.addEventListener("DOMContentLoaded", function() {
        feather.replace();
    });
</script>