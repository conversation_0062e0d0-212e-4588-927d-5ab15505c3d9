<div class="tile" id="tile" hx-get="{% url 'school:levels' %}?education={{active_nav}}" hx-trigger="saved from:body" hx-target="#app-content">
    {% if perms.school.add_level %}
    <div class="tile-title-w-btn d-flex">
        <div class="btn-group">
            <a class="btn btn-success" href="" 
               hx-get="{% url 'school:level_add' %}?education={{ active_nav }}" 
               hx-target="#dialog">+ Ajouter une classe</a>
        </div>
        {% if update_action %}
        <div class="btn-group shadow" title="Appuyer pour générer les fiches de table">
            <a id="submit-btn" hx-get="{% url update_action %}?education={{ active_nav }}" 
               hx-target="#progress_container" class="btn btn-info text-white"
               hx-on="htmx:beforeRequest: if(event.detail.target.id == 'progress_container') $('#submit-btn').prop('disabled', true);">Actualiser</a>
        </div>
        {% endif %}
    </div>
    {% endif %}

    <div id="progress_container"></div>
 
    <div class="table-responsive">
        <table class="table table-striped table-sm" id="datatable">
            <thead class="bg-primary text-white">
            <tr>
                <th>Niveau</th>
                <th>Classe</th>
                <th>Effectif</th>
                <th>Action</th>
            </tr>
            </thead>
            <tbody>
            {% for level in level_list %}
                <tr>
                    <td class="align-middle">{{ level.generic_level }}</td>
                    <td class="align-middle">{{ level }}</td>
                    <td class="align-middle">{{ level.students }}</td>
                    <td class="align-middle">
                        <span class="feather-16 align-middle"></span>
                        {% if level.is_clean or not update_action %}
                            {% if update_action %}
                                <a href="{% url 'exams:file_download' level.file_id %}" class="btn btn-sm btn-warning" target="_blank">{{ action_name }}</a>
                            {% else %}
                                <a href="{{ action_url }}?level={{ level.id }}&doc_type={{ doc_type }}" class="btn btn-sm btn-warning" target="_blank">{{ action_name }}</a>
                                {% if has_template2 %}
                                    <a href="{{ action_url }}?level={{ level.id }}&doc_type={{ doc_type }}&template2" class="btn btn-sm btn-info" target="_blank">{{ action_name2 }}</a>
                                {% endif %}
                            {% endif %}
                        {% else %}
                            <a href="" class="btn btn-sm btn-danger disabled"><span data-feather="exclamation"></span> Actualiser</a>
                        {% endif %}
                    </td>
                </tr>
            {% endfor %}
            </tbody>
        </table>
    </div>
</div>