# Generated by Django 4.2.3 on 2023-07-24 14:34

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('school', '0012_alter_level_education_alter_levelpricing_education_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Expense',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('amount', models.PositiveIntegerField(validators=[django.core.validators.MinValueValidator(100)])),
                ('expense_type', models.CharField(choices=[('EQU', 'Achat Matériel/Equipement'), ('CON', 'Construction de Bâtiment'), ('REN', 'Renovation/Peinture etc.'), ('SAL', 'Paiement des salaires'), ('AID', 'Aide financière'), ('OTH', 'Autre dépenses')], max_length=3)),
                ('commentaire', models.CharField(blank=True, max_length=255, null=True)),
                ('agent', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('school', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='school.school')),
                ('year', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='school.year')),
            ],
            options={
                'verbose_name': 'dépense',
            },
        ),
    ]
