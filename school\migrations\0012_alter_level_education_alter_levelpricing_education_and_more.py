# Generated by Django 4.2.3 on 2023-07-21 15:30

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('school', '0011_alter_levelpricing_unique_together_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='level',
            name='education',
            field=models.CharField(choices=[('A', 'Arabe'), ('F', 'Français')], default='F', max_length=2, verbose_name='langue'),
        ),
        migrations.AlterField(
            model_name='levelpricing',
            name='education',
            field=models.CharField(choices=[('A', 'Arabe'), ('F', 'Français')], default='F', max_length=1),
        ),
        migrations.AlterField(
            model_name='levelpricing',
            name='student_status',
            field=models.CharField(blank=True, choices=[('Aff', 'Affecté'), ('Naff', 'Non-Affecté')], max_length=4, null=True, verbose_name='statut Aff/Naff'),
        ),
        migrations.AlterField(
            model_name='school',
            name='education',
            field=models.CharField(choices=[('A', 'Arabe'), ('F', 'Français')], default='A', max_length=1, verbose_name="type d'école"),
        ),
        migrations.AlterField(
            model_name='school',
            name='pricing_option',
            field=models.PositiveSmallIntegerField(choices=[(0, 'Selon la classe arabe'), (1, 'Selon la classe française'), (2, 'Selon le plus haut niveau'), (3, 'Selon le plus bas niveau'), (4, 'Combiner les frais des deux classes')], default=1, verbose_name='calculer le coût de la scolarité selon'),
        ),
    ]
