from django.contrib.auth import get_user_model
from django.test import TestCase
from django.urls import reverse
from model_bakery import baker

from main.utils import EDUCATION_ARABIC, EDUCATION_FRENCH
from .. import models, school_utils


class SchoolUtilsMethodsTests(TestCase):
    def test_get_current_year_method(self):
        active_year = baker.make(models.Year, active=True)
        years = baker.make(models.Year, 5, active=False)
        result = school_utils.get_current_year()
        self.assertEqual(result.name, active_year.name)

    def test_get_highest_level_method(self):
        level1 = baker.make(models.GenericLevel, order=2)
        level2 = baker.make(models.GenericLevel, order=1)

        result1 = school_utils.get_highest_level(level1, level2)
        self.assertEqual(result1.name, level1.name)
        result2 = school_utils.get_highest_level(level2, level1)
        self.assertEqual(result2.name, level1.name)

    def test_get_lowest_level_method(self):
        level1 = baker.make(models.GenericLevel, order=2)
        level2 = baker.make(models.GenericLevel, order=1)

        result1 = school_utils.get_lowest_level(level1, level2)
        self.assertEqual(result1.name, level2.name)
        result2 = school_utils.get_lowest_level(level2, level1)
        self.assertEqual(result2.name, level2.name)
    
    def init_fees_data(self):
        self.pricing_option = models.School.PRICING_BY_ARABIC
        self.school = baker.make(models.School, pricing_option=self.pricing_option)
        self.year = baker.make(models.Year, active=True)
        self.user = baker.make(get_user_model(), school=self.school)
        self.CP1 = baker.make(models.GenericLevel, short_name='CP1', order=1)
        self.CP2 = baker.make(models.GenericLevel, short_name='CP2', order=2)
        self.SIXIEME = baker.make(models.GenericLevel, short_name='6EME', 
                  name='SIXIEME', order=2)

        self.level_cp1_ar = baker.make(models.Level, generic_level=self.CP1, 
                            school=self.school, year=self.year, 
                            education=EDUCATION_ARABIC)
        self.level_cp1_fr = baker.make(models.Level, generic_level=self.CP1, 
                            school=self.school, year=self.year, 
                            education=EDUCATION_FRENCH)
        self.level_cp2_ar = baker.make(models.Level, generic_level=self.CP2,
                            number=1,
                            school=self.school, year=self.year, 
                            education=EDUCATION_ARABIC)
        self.level_6eme_ar = baker.make(models.Level, generic_level=self.SIXIEME,
                            number=1,
                            school=self.school, year=self.year, 
                            education=EDUCATION_ARABIC)
        self.level_6eme_fr = baker.make(models.Level, generic_level=self.SIXIEME,
                            number=1,
                            school=self.school, year=self.year, 
                            education=EDUCATION_FRENCH)

        self.pricing_CP1_ar = baker.make(
            models.LevelPricing, year=self.year, school=self.school,
            generic_level=self.CP1, education=EDUCATION_ARABIC, 
            inscription=1000, scolarite=3500, 
            student_status=models.Enrollment.STATUS_NAFF)
        self.pricing_CP1_fr = baker.make(
            models.LevelPricing, year=self.year, school=self.school,
            generic_level=self.CP1, education=EDUCATION_FRENCH, 
            inscription=1500, scolarite=3500,
            student_status=None)
        self.pricing_CP2_ar = baker.make(
            models.LevelPricing, year=self.year, school=self.school,
            generic_level=self.CP2, education=EDUCATION_ARABIC, 
            inscription=13000, scolarite=23500)
        self.pricing_6eme_aff_fr = baker.make(
            models.LevelPricing, year=self.year, school=self.school,
            generic_level=self.SIXIEME, education=EDUCATION_FRENCH, 
            inscription=4500, scolarite=5000, 
            student_status=models.Enrollment.STATUS_AFF)
        self.pricing_6eme_naff_fr = baker.make(
            models.LevelPricing, year=self.year, school=self.school,
            generic_level=self.SIXIEME, education=EDUCATION_FRENCH, 
            inscription=4500, scolarite=5000, 
            student_status=models.Enrollment.STATUS_NAFF)
        self.pricing_6eme_ar = baker.make(
            models.LevelPricing, year=self.year, school=self.school,
            generic_level=self.SIXIEME, education=EDUCATION_ARABIC, 
            inscription=3300, scolarite=3800, 
            student_status=models.Enrollment.STATUS_NAFF)


    def test_get_fees_method_for_arabic_option(self):
        self.init_fees_data()
        # Test EDUCATION_ARABIC option
        self.school.pricing_option = models.School.PRICING_BY_ARABIC
        self.school.save()
        result = school_utils.get_fees(self.user, self.CP1.id, self.CP1.id)
        self.assertEqual(result['inscription'], 1000)
        self.assertEqual(result['scolarite'], 3500)

    def test_get_fees_method_for_primary_french_option(self):
        # Test EDUCATION_FRENCH option for PRIMARY SCHOOL
        self.init_fees_data()
        self.school.pricing_option = models.School.PRICING_BY_FRENCH
        self.school.save()
        result = school_utils.get_fees(self.user, self.CP1.id)
        self.assertEqual(result['inscription'], 
            self.pricing_CP1_fr.inscription)
        self.assertEqual(result['scolarite'], self.pricing_CP1_fr.scolarite)

    def test_get_fees_method_for_secondary_french_option(self):
        # Test EDUCATION_FRENCH option for SECONDARY school AFF
        self.init_fees_data()
        self.school.pricing_option = models.School.PRICING_BY_FRENCH
        self.school.save()
        result = school_utils.get_fees(self.user, self.SIXIEME.id, 
                 status=models.Enrollment.STATUS_AFF)
        self.assertEqual(result['inscription'], 
            self.pricing_6eme_aff_fr.inscription)
        self.assertEqual(result['scolarite'], 
            self.pricing_6eme_aff_fr.scolarite)
        
        # Test EDUCATION_FRENCH option for SECONDARY school NAFF
        result = school_utils.get_fees(self.user, self.SIXIEME.id, 
                 status=models.Enrollment.STATUS_NAFF)
        self.assertEqual(result['inscription'], 
            self.pricing_6eme_naff_fr.inscription)
        self.assertEqual(result['scolarite'], 
            self.pricing_6eme_naff_fr.scolarite)

    def test_education_by_both_option(self):
        # Test EDUCATION_BY_BOTH option with BOTH level passed
        self.init_fees_data()
        self.school.pricing_option = models.School.PRICING_BY_BOTH
        self.school.save()
        result = school_utils.get_fees(self.user, self.CP1.id, 
                 self.CP1.id)
        self.assertEqual(result['inscription'], 2500)
        self.assertEqual(result['scolarite'], 7000)
        
        # Test EDUCATION_BY_BOTH option with only one level passed
        result = school_utils.get_fees(self.user, self.CP1.id)
        self.assertEqual(result['inscription'], 1500)
        self.assertEqual(result['scolarite'], 3500)

        # Test that status does not matter for arabic levels
        result = school_utils.get_fees(self.user, self.SIXIEME.id,
                 self.SIXIEME.id, 
                 status=models.Enrollment.STATUS_AFF)
        self.assertEqual(result['inscription'], 
            self.pricing_6eme_aff_fr.inscription 
            + self.pricing_6eme_ar.inscription)
        self.assertEqual(result['scolarite'], 
            self.pricing_6eme_aff_fr.scolarite
            + self.pricing_6eme_ar.scolarite)
        

    def test_education_by_highest_option(self):
        # Test EDUCATION_HIGHEST option
        self.init_fees_data()
        self.school.pricing_option = models.School.PRICING_BY_HIGHEST_LEVEL
        self.school.save()
        result = school_utils.get_fees(self.user, self.CP1.id, 
                 self.CP2.id)
        self.assertEqual(result['inscription'], 13000)
        self.assertEqual(result['scolarite'], 23500)

    def test_get_fees_method_for_lowest_option(self):        
        # Test EDUCATION_LOWEST option
        self.init_fees_data()
        self.school.pricing_option = models.School.PRICING_BY_LOWEST_LEVEL
        self.school.save()
        result = school_utils.get_fees(self.user, self.CP1.id, 
                 self.CP2.id)
        self.assertEqual(result['inscription'], 1500)
        self.assertEqual(result['scolarite'], 3500)
