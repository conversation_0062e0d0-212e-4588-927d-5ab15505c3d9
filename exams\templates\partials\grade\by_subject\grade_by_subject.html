{% load widget_tweaks %}
<div class="row" hx-get="{% url 'exams:grade_by_subject' %}?type={{type}}&lang={{lang}}" 
     hx-trigger="saved from:body" 
     hx-target="#table_container"
     hx-include="[name=short_name], [name=level], [name=term], [name=subject]">
    <div class="col">
        <div class="tile">
            <form class="mb-3" hx-get="{% url 'exams:grade_by_subject' %}?type={{type}}&lang={{ lang }}" hx-target="#table_container">
                {% csrf_token %}
                <div class="form-row">
                    <div class="col-6 col-md-3 form-group">
                        <label for="">Niveau</label>
                        <select name="short_name" id="generic_level" class="form-control" 
                                hx-get="{% url 'exams:sublevels' %}?lang={{lang}}"
                                hx-target="#level_container">
                            <option value="">--------</option>
                            {% for level in generic_levels %}
                            <option value="{{ level.id }}">{{ level }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-6 col-md-3 form-group" id="level_container">
                        <label for="">Classe</label>
                        <select name="level" id="level" class="form-control"></select>
                    </div>
                    <div class="col-6 col-md-3 form-group" id="subject_container">
                        <label for="">Matière</label>
                        <select name="subject" id="id_subject" class="form-control" hx-swap="outerHTML"></select>
                    </div>

                    <div class="col-6 col-md-3 form-group" id="term_container">
                        <label for="">Période</label>
                        <select name="term" id="id_term" class="form-control" 
                                required="required" hx-swap="outerHTML">
                        </select>
                    </div>

                </div>
                <div class="d-flex justify-content-between">
                    <button type="submit" class="btn btn-success btn-sm">
                        <span data-feather="eye" class="feather-16 align-middle"></span> 
                        Voir la liste</button>
                    <button type="submit" class="btn btn-warning btn-sm" 
                            hx-post="{% url 'exams:compute_term_results' %}">
                            <span data-feather="refresh-ccw" class="feather-16 align-middle"></span>
                            Relancer le calcul moyenne
                    </button>
                </div>
            </form>
            <form method="post" action="{% url 'exams:subject_grade_export' %}" 
                  class="table-responsive" id="table_container">
            </form>
        </div>
    </div>
</div>