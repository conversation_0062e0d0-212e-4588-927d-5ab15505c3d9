# Generated by Django 5.0.2 on 2024-03-04 13:28

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('exams', '0049_alter_studentsubjectgroupaverage_group_and_more'),
        ('school', '0055_enrollment_cherifla'),
    ]

    operations = [
        migrations.CreateModel(
            name='PDFFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('path', models.CharField(max_length=255)),
                ('is_clean', models.BooleanField(default=True)),
                ('category', models.CharField(choices=[('RS', 'Matrice des moyennes'), ('R2', 'Matrice des moyennes simplifiée'), ('RP', 'Relevé de notes'), ('MS', 'Fiche de notation'), ('FT', 'Fiche de table'), ('CL', 'Liste de classe')], max_length=2)),
                ('level', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='school.level')),
                ('term', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='exams.schoolterm')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
