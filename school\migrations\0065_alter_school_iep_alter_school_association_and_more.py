# Generated by Django 5.0.2 on 2024-06-08 09:05

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('school', '0064_school_director_secondary'),
    ]

    operations = [
        migrations.AlterField(
            model_name='school',
            name='IEP',
            field=models.CharField(blank=True, help_text='pour les écoles primaires', max_length=255, null=True, verbose_name='IEPP'),
        ),
        migrations.AlterField(
            model_name='school',
            name='association',
            field=models.CharField(blank=True, choices=[('OE', 'OEECI'), ('CH', 'CHERIFLA'), ('LE', 'LECIM')], help_text='pour les écoles islamiques', max_length=2, null=True, verbose_name='Association Islamique'),
        ),
        migrations.AlterField(
            model_name='school',
            name='secteur_p',
            field=models.CharField(blank=True, help_text='pour les écoles primaires', max_length=255, null=True, verbose_name='secteur pédagogique'),
        ),
        migrations.AlterField(
            model_name='school',
            name='translation',
            field=models.CharField(blank=True, help_text='pour les écoles islamiques', max_length=255, null=True, verbose_name='traduction en arabe'),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='level',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='school.genericlevel', verbose_name='classe à gérer'),
        ),
    ]
