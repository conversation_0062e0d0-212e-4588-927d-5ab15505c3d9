{% load widget_tweaks %}
{% load humanize %}

<div method="post" action="" class="dashboard-wrapper" hx-include="this" hx-get="{{ request.path }}?page={{ page }}&statut={{ request.GET.statut }}{% if education %}&education={{ education}}{% endif %}" hx-trigger="saved from:body" hx-target="#app-content">
  <!-- Filters Section -->
  {% include 'partials/active_students/filter_section.html' %}

  <!-- Table Section -->
  <div id="results-area"> 
    <div class="table-card">
      <form class="table-responsive" style="overflow: auto;">
        {% with education=user.school.education %}
        <table class="table table-striped mb-0" style="font-size: 11.5px;">
          <thead>
            <tr>
              <th width="40">
                <div class="checkbox-wrapper">
                  <input type="checkbox" id="select-all" class="custom-checkbox">
                  <span class="checkmark" id="select-all-checkmark"></span>
                </div>
              </th>
              <th width="60">Photo</th>
              <th class="sortable {% if sort_field == 'student__last_name' %}sort-active{% endif %} sticky-header sticky-column"
                  hx-get="{{ request.path }}" 
                  hx-include="[name='per_page']"
                  hx-vals='{"sort": "{% if sort_field == "student__last_name" and sort_direction == "asc" %}-{% endif %}student__last_name"}'>
                Nom et Prénoms
                <i data-feather="chevron-{% if sort_field == 'student__last_name' %}{% if sort_direction == 'asc' %}up{% else %}down{% endif %}{% else %}down{% endif %}" 
                   class="sort-icon feather-14"></i>
              </th>
              <th class="sortable {% if sort_field == 'student__student_id' %}sort-active{% endif %}"
                  hx-get="{{ request.path }}"
                  hx-include="[name='per_page']"
                  hx-vals='{"sort": "{% if sort_field == "student__student_id" and sort_direction == "asc" %}-{% endif %}student__student_id"}'>
                Matricule
                <i data-feather="chevron-{% if sort_field == 'student__student_id' %}{% if sort_direction == 'asc' %}up{% else %}down{% endif %}{% else %}down{% endif %}"
                   class="sort-icon feather-14"></i>
              </th>
              <th class="sortable {% if sort_field == 'level_fr' %}sort-active{% endif %}"
                  hx-get="{{ request.path }}"
                  hx-include="[name='per_page']"  
                  hx-vals='{"sort": "{% if sort_field == "level_fr" and sort_direction == "asc" %}-{% endif %}level_fr"}'>
                Classe
                <i data-feather="chevron-{% if sort_field == 'level_fr' %}{% if sort_direction == 'asc' %}up{% else %}down{% endif %}{% else %}down{% endif %}"
                   class="sort-icon feather-14"></i>
              </th>
              {% if is_arabic_school %}
              <th class="sortable {% if sort_field == 'level_ar' %}sort-active{% endif %}"
                  hx-get="{{ request.path }}"
                  hx-include="[name='per_page']"
                  hx-vals='{"sort": "{% if sort_field == "level_ar" and sort_direction == "asc" %}-{% endif %}level_ar"}'>
                Arabe
                <i data-feather="chevron-{% if sort_field == 'level_ar' %}{% if sort_direction == 'asc' %}up{% else %}down{% endif %}{% else %}down{% endif %}"
                   class="sort-icon feather-14"></i>
              </th>
              {% endif %}
              <th>Sexe</th>
              <th>Statut</th>
              <th class="text-right sortable" class="sortable {% if sort_field == 'debt' %}sort-active{% endif %}"
                hx-get="{{ request.path }}"
                hx-include="[name='per_page']"  
                hx-vals='{"sort": "{% if sort_field == "debt" and sort_direction == "asc" %}-{% endif %}debt"}'>Arriéré
                <i data-feather="chevron-{% if sort_field == 'debt' %}{% if sort_direction == 'asc' %}up{% else %}down{% endif %}{% else %}down{% endif %}"
                   class="sort-icon feather-14"></i>
              </th>
              <th class="text-right sortable"
              class="sortable {% if sort_field == 'amount' %}sort-active{% endif %}"
              hx-get="{{ request.path }}"
              hx-include="[name='per_page']"  
              hx-vals='{"sort": "{% if sort_field == "amount" and sort_direction == "asc" %}-{% endif %}amount"}'>À payer
                <i data-feather="chevron-{% if sort_field == 'amount' %}{% if sort_direction == 'asc' %}up{% else %}down{% endif %}{% else %}down{% endif %}"
                   class="sort-icon feather-14"></i>
              </th>
              <th class="text-right sortable" class="sortable {% if sort_field == 'paid' %}sort-active{% endif %}"
              hx-get="{{ request.path }}"
              hx-include="[name='per_page']"  
              hx-vals='{"sort": "{% if sort_field == "paid" and sort_direction == "asc" %}-{% endif %}paid"}'>Payé
                <i data-feather="chevron-{% if sort_field == 'paid' %}{% if sort_direction == 'asc' %}up{% else %}down{% endif %}{% else %}down{% endif %}"
                   class="sort-icon feather-14"></i>
              </th>
              <th class="text-right sortable" class="sortable {% if sort_field == 'remaining' %}sort-active{% endif %}"
              hx-get="{{ request.path }}"
              hx-include="[name='per_page']"  
              hx-vals='{"sort": "{% if sort_field == "remaining" and sort_direction == "asc" %}-{% endif %}remaining"}'>Reste
                <i data-feather="chevron-{% if sort_field == 'remaining' %}{% if sort_direction == 'asc' %}up{% else %}down{% endif %}{% else %}down{% endif %}"
                   class="sort-icon feather-14"></i>
              </th>
              <th width="120">Actions</th>
            </tr>
          </thead>
          <tbody>
            {% for enrollment in enrollments %}
            <tr class="student-row {% if enrollment.selected %} selected {% endif %}" >
              <td>
                <div class="checkbox-wrapper">
                  <input type="checkbox" 
                         name="check-{{enrollment.id}}" 
                         id="check-{{enrollment.id}}" 
                         class="row-checkbox custom-checkbox"
                         {% if enrollment.selected %} checked="checked" {% endif %}> 
                  <span class="checkmark"></span>
                </div>
              </td>
              <td>
                {% if enrollment.student.photo %}
                  <img data-original="{{ enrollment.student.photo.url }}" 
                       alt="Photo" 
                       class="student-photo lazy">
                {% elif enrollment.is_second_cycle_fr and enrollment.student.student_id %}
                  <img data-original="{{ enrollment.student.government_photo }}" 
                       alt="Photo" 
                       class="student-photo lazy"
                       id="{{ enrollment.id }}"
                       onload="if (this.src.endsWith('CC')) {
                         this.src = '{{ enrollment.student.blank_photo }}'
                       }">
                {% else %}
                  <img data-original="{{ enrollment.student.blank_photo }}" 
                       alt="Photo" 
                       class="student-photo lazy">
                {% endif %}
              </td>
              <td class="sticky-column">
                <div class="student-name">{{ enrollment.student.get_full_name }}
                  {% if is_arabic_school and enrollment.student.full_name_ar %}<br> <span class="text-muted">{{ enrollment.student.full_name_ar }}</span>{% endif %}
                </div>
              </td>
              <td>{{ enrollment.student.student_id|default:"-" }}</td>
              <td>{% if enrollment.level_fr or enrollment.generic_level_fr %} {{ enrollment.level_fr|default:enrollment.generic_level_fr }} {% elif not enrollment.level_fr %}<span class="text-muted" title="Classe non attribuée" data-toggle="tooltip">?</span> {% else %} - {% endif %}</td>
              {% if is_arabic_school %}
              <td>{% if enrollment.level_ar or enrollment.generic_level_ar %} {{ enrollment.level_ar|default:enrollment.generic_level_ar }} {% elif not enrollment.level_ar %}<span class="text-muted" title="Classe arabe non attribuée" data-toggle="tooltip">?</span> {% else %} - {% endif %}</td>
              {% endif %}
              <td>{{ enrollment.student.gender }}</td>
              <td>
                {% if enrollment.status %}
                <span class="status-badge {% if enrollment.active %}status-active{% else %}status-inactive{% endif %}">
                  {{ enrollment.status }}
                </span>
                {% else %}
                -
                {% endif %}
              </td>
              <td class="amount-cell amount-negative">
                {{ enrollment.debt|intcomma|default:"0" }}
              </td>
              <td class="amount-cell">
                {{ enrollment.amount|intcomma }}
              </td>
              <td class="amount-cell {% if enrollment.paid %}amount-positive{% endif %}">
                {% if enrollment.paid %} {{ enrollment.paid|intcomma|default:"0" }} {% else %} - {% endif %}
              </td>
              <td class="amount-cell {% if enrollment.remaining == 0 %}amount-positive{% elif enrollment.remaining > 0 %}amount-warning{% endif %}">
                {% if enrollment.remaining == 0 and enrollment.get_fees_total > 0 %}
                  Soldé
                {% else %}
                  {{ enrollment.remaining|intcomma|default:"-" }}
                {% endif %}
              </td>
              <td>
                <div class="d-flex justify-content-between" style="max-width: 180px !important;">
                  {% if enrollment.active %}
                  <a href="#" hx-get="{% url 'school:student_edit_wizard' enrollment.id %}?page={{ page }}"
                  hx-target="#dialog" class="show-on-phone btn btn-sm mr-1 bg-light action-button" title="Modifier infos" data-toggle="tooltip">
                  <span data-feather="edit-2" class="feather-16"></span>
                  </a>
                  <a href="#" hx-get="{% url 'school:student_edit' enrollment.id %}?page={{ page }}"
                      hx-target="#dialog-xl" class="show-on-pc btn btn-sm mr-1 bg-light action-button" title="Modifier infos" data-toggle="tooltip">
                      <span data-feather="edit-2" class="feather-16"></span>
                  </a>
                  <button class="btn btn-sm mr-1 btn-light action-button"
                          hx-get="{% url 'school:payment_add' %}?student_id={{ enrollment.student.identifier }}"
                          hx-target="#dialog-xl" data-toggle="tooltip" title="Ajouter/Modifier un paiement">
                    <i class="feather-16" data-feather="dollar-sign"></i>
                  </button>
                <div class="dropdown show-on-pc mr-1" data-toggle="tooltip" title="Reçu Résumé par Rubrique">
                  <button class="btn action-button btn-xs bg-info text-white dropdown" style="background-color: inherit;" type="button" data-toggle="dropdown" aria-expanded="false">
                      <i data-feather="file-text" class="feather-16"></i> 1
                  </button>
                  <div class="dropdown-menu" style="font-size: 0.8rem;">
                      <a class="dropdown-item"
                        target="_blank"
                        href="{% url 'school:student_payments_pdf' enrollment.id %}?template=2&copies=1"
                        onclick="Pace.restart()">
                        1 copie
                      </a>
                      <a class="dropdown-item" onclick="Pace.restart()"
                        target="_blank"
                        href="{% url 'school:student_payments_pdf' enrollment.id %}?template=2&copies=2">
                        2 copies
                      </a>
                  </div>
                </div>
                <div class="dropdown show-on-pc mr-1" data-toggle="tooltip" title="Reçu avec liste des paiements">
                  <button class="btn action-button btn-xs bg-info text-white dropdown" style="background-color: inherit;" type="button" data-toggle="dropdown" aria-expanded="false">
                      <i data-feather="file-text" class="feather-16"></i> 2
                  </button>
                  <div class="dropdown-menu" style="font-size: 0.8rem;">
                      <a class="dropdown-item"
                        target="_blank"
                        href="{% url 'school:student_payments_pdf' enrollment.id %}?template=1&copies=1"
                        onclick="Pace.restart()">
                        1 copie
                      </a>
                      <a class="dropdown-item" onclick="Pace.restart()"
                        target="_blank"
                        href="{% url 'school:student_payments_pdf' enrollment.id %}?template=1&copies=2">
                        2 copies
                      </a>
                  </div>
                </div>
                <div class="show-on-phone mr-1">
                  <button class="btn btn-xs action-button dropdown" style="background-color: inherit;" type="button" data-toggle="dropdown" aria-expanded="false">
                      <i data-feather="more-vertical" class="feather-16"></i>
                  </button>
                  <div class="dropdown-menu" style="font-size: 0.8rem;">
                    <a class="dropdown-item" 
                    href="" hx-get="{% url 'school:payment_add' %}?student_id={{ enrollment.student.identifier }}" hx-target="#dialog">
                    Paiements 
                  </a>
                  <div class="dropdown-divider"></div>
                  <a class="dropdown-item"
                    target="_blank"
                    href="{% url 'school:student_payments_pdf' enrollment.id %}?template=2&copies=1"
                    onclick="Pace.restart()">
                    Reçu Modèle 1
                  </a>
                  <a class="dropdown-item" onclick="Pace.restart()"
                    target="_blank"
                    href="{% url 'school:student_payments_pdf' enrollment.id %}?copies=1&template=1">
                    Reçu Modèle 2
                  </a>
                  <div class="dropdown-divider"></div>
                    <a class="dropdown-item" onclick="Pace.restart()"
                    href=""
                    hx-get="{% url 'school:student_delete' enrollment.id %}?page={{ page }}&{% if level_fr %}level_fr={{ level_fr }}{% elif level_ar %}level_ar={{ level_ar}}{% endif %}"
                    hx-target="#dialog">
                    Supprimer élève 
                    </a>
                  </div>
                </div>
                <a href="#" hx-get="{% url 'school:student_delete' enrollment.id %}?page={{ page }}&{% if level_fr %}level_fr={{ level_fr }}{% elif level_ar %}level_ar={{ level_ar}}{% endif %}"
                  hx-target="#dialog" class="btn btn-danger btn-xs action-button show-on-pc" title="Supprimer élève" data-toggle="tooltip">
                  <span data-feather="trash-2" class="feather-16"></span>
                </a>
              </div>

                  {% else %}
                    <a href="" hx-get="{% url 'school:student_edit' enrollment.id %}?page={{ page }}&{% if level_fr %}level_fr={{ level_fr }}{% elif level_ar %}level_ar={{ level_ar}}{% endif %}" 
                    hx-target="#dialog-xl" class="btn btn-sm mr-1 btn-success action-button show-on-pc"> 
                    <span data-feather="user-plus" class="feather-16 align-middle"></span> Inscrire</a> 
                    <a href="" hx-get="{% url 'school:student_edit_wizard' enrollment.id %}?page={{ page }}&{% if level_fr %}level_fr={{ level_fr }}{% elif level_ar %}level_ar={{ level_ar}}{% endif %}" 
                    hx-target="#dialog" class="btn btn-sm mr-1 btn-success action-button show-on-phone">
                    <span data-feather="user-plus" class="feather-16 align-middle"></span> Inscrire</a> 
                  {% endif %}
                </div>
              </td>
            </tr>
            {% endfor %}
          </tbody>
          <!-- <tfoot>
            <tr class="font-weight-bold bg-light">
              <td colspan="8" class="text-right">
              </td>
              <td class="text-right amount-cell amount-negative px-2">
                {{ total_debt|default:0|intcomma }}
              </td>
              <td class="text-right amount-cell px-2">
                {{ total_amount|default:0|intcomma }}
              </td>
              <td class="text-right amount-cell amount-positive">
                {{ total_paid|default:0|intcomma }}
              </td>
              <td class="text-right amount-cell px-2">
                {{ total_remaining|default:0|intcomma }}
              </td>
              <td></td>
            </tr>
          </tfoot> -->
        </table>
        {% endwith %}
        {% include 'partials/student/actions.html' %}
      </form>
    </div>
  </div>

  <!-- Pagination -->
  <div class="d-flex justify-content-between align-items-center mt-4">
    <select class="form-control form-control-sm" 
            style="width: auto"
            name="per_page"
            hx-get="{{ request.path }}"
            hx-target="#app-content"
            hx-vals='{"statut": "{{ statut_inscrit }}"}'>
      <option value="10">10</option>
      <option value="25" {% if per_page == '25' %} selected {% endif %}>25</option>
      <option value="50" {% if per_page == '50' %} selected {% endif %}>50</option>
      <option value="100" {% if per_page == '100' %} selected {% endif %}>100</option>
    </select>
    
    {% include 'partials/pagination.html' with include_items='.form-control' %}
  </div>
</div>
{% include 'partials/active_students/students_list_js.html' %}
