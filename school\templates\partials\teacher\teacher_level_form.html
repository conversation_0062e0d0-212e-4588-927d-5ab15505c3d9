{% extends 'partials/modal.html' %}
{% load i18n %}
{% load widget_tweaks %}

{% block modal_title %}
Attribution de cours/classe
{% endblock %}

{% block modal_body %}
<div class="form-row">
    <div class="form-group col-md-6">
        <label for="{{ form.teacher.id_for_label }}">Enseignant</label>
        {% render_field form.teacher class='form-control' %}
    </div>
    <div class="form-group col-md-6">
        <label for="{{ form.level.id_for_label }}">Classe</label>
        {% render_field form.level class='form-control' hx-get='/classes/matieres/' hx-target='#id_subjects' %}
    </div>
</div>

<div class="form-row">
    <div class="form-group form-check ml-2">
        {% render_field form.is_main_teacher class='form-check-input' %}
        <label class="form-check-label" for="{{ form.is_main_teacher.id_for_label }}" style="font-size: larger;">{{ form.is_main_teacher.label }}</label>
    </div>
</div>
<div class="form-row">
    <div class="form-group col-6">
        <label for="{{ form.subjects.id_for_label }}">Matières enseignées</label>
        {% render_field form.subjects class='form-control' style='height: 200px' hx-swap='outerHTML' %}
        <div class="form-help mt-2">Appuyez sur la touche CTRL pour sélectionner plusieurs matières</div>
    </div>
    <div class="col-6">
        {% if teacherlevel2.subjects.exists %}
            <p class="text-muted">Matières sélectionnées actuellement:</p>
            <ul>
                {% for subject in teacherlevel2.subjects.all %}
                <li>{{ subject }}</li>
                {% endfor %}
            </ul>
        {% endif %}
    </div>
</div>


{% endblock %}

{% block modal_footer %}
<input type="submit" class="btn btn-success" value="{% trans 'Valider' %}" id="submit-btn" />
{% endblock %}