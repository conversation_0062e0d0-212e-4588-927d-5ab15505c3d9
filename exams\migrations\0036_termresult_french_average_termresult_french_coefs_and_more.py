# Generated by Django 4.2.4 on 2023-11-08 10:35

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('exams', '0035_educationyearresult_admission_average_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='termresult',
            name='french_average',
            field=models.DecimalField(decimal_places=2, max_digits=5, null=True),
        ),
        migrations.AddField(
            model_name='termresult',
            name='french_coefs',
            field=models.PositiveSmallIntegerField(default=3),
        ),
        migrations.AddField(
            model_name='termresult',
            name='french_rank',
            field=models.PositiveSmallIntegerField(default=None),
        ),
        migrations.AlterField(
            model_name='termresult',
            name='average_with_coef',
            field=models.DecimalField(blank=True, decimal_places=2, default=0, max_digits=5, null=True, verbose_name='moyenne coefficientée'),
        ),
    ]
