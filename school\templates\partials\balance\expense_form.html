{% extends 'partials/modal.html' %}
{% load widget_tweaks %}

{% block modal_title %} 
    Sortie/<PERSON>pense
{% endblock %}

{% block modal_body %} 
    <div class="form-row">
        {% for field in form %}
            <div class="mb-3 col-md-6">
                <label for="{{ field.id_for_label }}">{{ field.label }}</label>
                <td>{% render_field field class='form-control' %}</td>
            </div>
        {% endfor %}
    </div>
    <div class="form-row">
        {% if form.errors %}
            <div class="invalid-feedback">{{ form.errors|first }}</div>
        {% endif %}

        {% if form.non_field.erros %}
        <div class="invalid-feedback">{{ form.non_field.errors|first }}</div>
        {% endif %}
    </div>
{% endblock %}

{% block modal_footer %}
    <input type="submit" class="btn btn-success" value="Enregistrer">
{% endblock %}