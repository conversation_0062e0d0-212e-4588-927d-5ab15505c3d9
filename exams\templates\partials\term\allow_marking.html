<form class="tile" method="post" action="{{ request.path }}?lang={{ lang }}" hx-post="{{ request.path }}?lang={{ lang }}">
    {% csrf_token %}
    <div class="alert alert-warning font-weight-bold">Cocher pour autoriser la notation et décocher pour verrouiller les notes</div>
    {% if not user.school.cycle == 'S'  %}
        <h5>Primaire</h5>
        <ul class="list-group"></ul>
        {% for term in terms_primary %}
        <li class="list-group-item">
            <div class="media">
                <input type="checkbox" name="term-{{ term.id }}" id="term-{{ term.id }}" class="w-auto mr-3 form-control align-middle w-50" {% if term.allow_marking %} checked {% endif %}> 
                <div class="media-body">
                    <label for="term-{{ term.id }}" class="align-middle w-50">{{ term }}</label>
                </div>
            </div>
        </li>
        {% endfor %}
        </ul>
    {% endif %}
    {% if not user.school.cycle == 'P'  %}
        <h5 class="mt-3">Secondaire</h5>
        {% for term in terms_secondary %}
        <li class="list-group-item">
            <div class="media">
                <input type="checkbox" name="term-{{ term.id }}" id="term-{{ term.id }}" class="w-auto mr-3 form-control align-middle w-50" {% if term.allow_marking %} checked {% endif %}> 
                <div class="media-body">
                    <label for="term-{{ term.id }}" class="align-middle w-50">{{ term }}</label>
                </div>
            </div>
        </li>
        {% endfor %}
    {% endif %}

    <input type="submit" value="Enregistrer" class="btn btn-success mt-2">
</form>