# Generated by Django 4.2.4 on 2023-09-28 16:31

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('school', '0033_alter_school_cycle'),
        ('exams', '0018_alter_levelsubject_school_alter_levelsubject_year'),
    ]

    operations = [
        migrations.AlterField(
            model_name='completecyclesubjectkit',
            name='cycle',
            field=models.CharField(choices=[('P', 'Primaire'), ('S', 'Secondaire'), ('B', 'Les deux (Groupe Scolaire)')], max_length=1, null=True, verbose_name='cycle'),
        ),
        migrations.AlterField(
            model_name='schoolterm',
            name='cycle',
            field=models.CharField(choices=[('P', 'Primaire'), ('S', 'Secondaire'), ('B', 'Les deux (Groupe Scolaire)')], max_length=1),
        ),
        migrations.AlterField(
            model_name='schoolterm',
            name='school',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='school.school'),
        ),
        migrations.AlterField(
            model_name='schoolterm',
            name='year',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='school.year'),
        ),
        migrations.AlterField(
            model_name='subject',
            name='cycle',
            field=models.CharField(choices=[('P', 'Primaire'), ('S', 'Secondaire'), ('B', 'Les deux (Groupe Scolaire)')], max_length=1, null=True, verbose_name='cycle'),
        ),
        migrations.AlterField(
            model_name='subjectkit',
            name='cycle',
            field=models.CharField(choices=[('P', 'Primaire'), ('S', 'Secondaire'), ('B', 'Les deux (Groupe Scolaire)')], max_length=1, null=True, verbose_name='cycle'),
        ),
        migrations.AlterField(
            model_name='term',
            name='cycle',
            field=models.CharField(blank=True, choices=[('P', 'Primaire'), ('S', 'Secondaire'), ('B', 'Les deux (Groupe Scolaire)')], default='P', max_length=1, null=True),
        ),
        migrations.CreateModel(
            name='TermKit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255)),
                ('cycle', models.CharField(choices=[('P', 'Primaire'), ('S', 'Secondaire'), ('B', 'Les deux (Groupe Scolaire)')], max_length=1, null=True, verbose_name='cycle')),
                ('education', models.CharField(choices=[('A', 'Arabe'), ('F', 'Français')], max_length=1, verbose_name='éducation')),
                ('levels', models.ManyToManyField(to='school.genericlevel')),
                ('terms', models.ManyToManyField(to='exams.schoolterm')),
            ],
            options={
                'verbose_name': 'Kit de périodes',
            },
        ),
        migrations.CreateModel(
            name='CompleteTermKit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('cycle', models.CharField(choices=[('P', 'Primaire'), ('S', 'Secondaire'), ('B', 'Les deux (Groupe Scolaire)')], max_length=1, null=True, verbose_name='cycle')),
                ('education', models.CharField(choices=[('A', 'Arabe'), ('F', 'Français')], max_length=1, verbose_name='éducation')),
                ('term_kits', models.ManyToManyField(to='exams.termkit', verbose_name='kit de périodes')),
            ],
            options={
                'verbose_name': 'Kits complet de périodes',
            },
        ),
    ]
