<table class="table table-striped table-sm table-hover table-bordered" id="datatable">
    {% if active_nav == 'ar' or active_nav == 'fr' %} id="datatable" {% endif %}>
    <thead class="bg-primary text-white">
    <tr>
        <th>
            <input type="checkbox" name="select-all" id="select-all" onclick="checkAll(this)">
        </th>
        <th>Matricule</th>
        <th style="min-width: 120px;">Nom et Prénoms</th>
        <th style="min-width: 120px;">Sexe</th>
        <th style="min-width: 120px;">Né le</th>
    </tr>
    </thead>
    <tbody>
        {% for enrollment in enrollments %}
            <tr>
                <td>
                    <input type="checkbox" name="check-{{enrollment.id}}" id="check-{{enrollment.id}}">
                </td>
                <td><span class="text-muted">{{ enrollment.student.student_id|default_if_none:''}}</span></td>
                <td class="align-middle">{{ enrollment }}</td>
                <td class="align-middle">{{ enrollment.student.gender }}</td>
                <td class="align-middle">{{ enrollment.student.birth_date_str }}</td>
            </tr>
        {% endfor %}
</tbody>
</table>
<div class="form-row ml-3 mb-3">
    <button type="submit" class="btn btn-warning btn-sm"><span data-feather="file-text" class="feather-16 align-middle"></span> Imprimer la fiche</button>
</div>

<script>
  function checkAll(checkbox) {
    var checkboxes = document.getElementsByTagName('input');
    for (var i = 0; i < checkboxes.length; i++) {
      if (checkboxes[i].type === 'checkbox') {
        checkboxes[i].checked = checkbox.checked;
      }
    }
  }

  document.addEventListener("DOMContentLoaded", function(ev) {
      feather.replace();
      $("img.lazy").lazyload();
    })

  if (typeof(feather) != "undefined") {
      feather.replace();
      $("img.lazy").lazyload();
  }
</script>