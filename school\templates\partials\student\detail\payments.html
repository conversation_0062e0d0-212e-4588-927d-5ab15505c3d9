<div class="tile">
    <h5>Paiement de la Scolarité</h5>

    <table class="table table-striped table-sm">
        <thead>
            <tr>
                <th>Date</th>
                <th>Montant versé</th>
            </tr>
        </thead>
        <tbody>
            {% for payment in enrollment.payment_set.all %}
            <tr>
                <td>{{ payment.created_at }}</td>
                <td>{{ payment.get_total }}</td>
            </tr>
            {% endfor %}
        </tbody>
        <tfoot class="bg-warning">
            <tr>
                <td>Total versé</td>
                <td>{{ total_paid }}</td>
            </tr>
        </tfoot>
    </table>

    <div class="container-fluid">
        {% if perms.school.add_payment %}
        <a href="" class="btn btn-success" hx-get="{% url 'school:payment_add' %}?student_id={{enrollment.student.identifier}}" hx-target="#dialog">
          Nouveau versement</a>
        {% endif %}

        {% if perms.school.view_payment %}
        <a href="{% url 'school:student_payments_pdf' enrollment.id %}?template=2" class="btn btn-warning">
          Relevé des versements</a>
        {% endif %}
    </div>
</div>