<div class="tile" id="tile" hx-get="{% url 'school:levels' %}?education={{active_nav}}" hx-trigger="saved from:body" hx-target="#app-content">
    {% if subschools_count >= 2 %}
    <div class="form-group">
      <label for="subschool">Filtrer par Ecole</label>
      <select id="subschool" name="subschool" class="form-control" 
              hx-get="{% url 'school:levels' %}" hx-target="#app-content" hx-trigger="change">
        <option value="">Toutes</option>
        {% for subschool in subschools %}
          <option value="{{ subschool.id }}" {% if request.GET.subschool == subschool.id|stringformat:"s" %}selected{% endif %}>
            {{ subschool.name }}
          </option>
        {% endfor %}
      </select>
    </div>
  {% endif %}
    {% if perms.school.add_level %}
    <div class="tile-title-w-btn">
        <div class="btn-group">
            <a class="btn btn-success" href="" 
               hx-get="{% url 'school:level_add' %}?education={{ active_nav }}" 
               hx-target="#dialog">
               <span data-feather="plus-square" class="align-middle"></span> Ajouter une classe</a>
        </div>
        <div class="btn-group">
            <a class="btn btn-info" href="" 
               hx-get="{% url 'school:level_batch_create' %}?education={{ active_nav }}" 
               hx-target="#app-content">
               <span data-feather="chevron-right" class="align-middle"></span> Ajouter plusieurs</a>
        </div>
    </div>
    {% endif %}
    <form class="table-responsive" action="{% url 'school:levels_stats_export' %}">
        <table class="table table-striped table-sm checkbox-table" id="datatable">
            <thead class="bg-primary text-white">
            <tr>
                <th><input type="checkbox" name="select-all" id="select-all"></th>
                {% if subschools_count >= 2 %}
                    <th>Ecole</th>
                {% endif %}
                <th>Niveau</th>
                <th>Classe</th>
                <th>Places Maxi</th>
                <th>Effectif</th>
                <th>Places dispo</th>
                {% if perms.school.add_level %}
                <th>Actions</th>
                {% endif %}
            </tr>
            </thead>
            <tbody>
            {% for level in level_list %}
            <tr>
                <td class="align-middle"><input type="checkbox" name="check-{{ level.id }}" id="check-{{ level.id }}"></td>
                {% if subschools_count >= 2 %}
                    <td class="align-middle">{{ level.subschool.school|default:main_school }}</td>
                {% endif %}
                <td class="align-middle">{{ level.generic_level }}</td>
                <td class="align-middle">{{ level }}</td>
                <td class="align-middle">{{ level.max }}</td>
                <td class="align-middle">{{ level.students }}</td>
                <td class="align-middle">{{ level.remaining }}</td>
                {% if perms.school.add_level %}
                <td class="align-middle">
                    <div class="dropdown">
                        <button class="btn btn-sm dropdown" style="background-color: inherit;" type="button" data-toggle="dropdown" aria-expanded="false">
                            <i data-feather="more-vertical" class="feather-16"></i>
                        </button>
                        <div class="dropdown-menu">
                          <a class="dropdown-item" href="#"
                          hx-get="{% url 'school:level_edit' level.id %}?education={{active_nav}}"
                          hx-target="#dialog">Modifier</a>
                          <a class="dropdown-item"
                            href="{% url 'school:level_list_pdf' level.id %}" target="_blank">Impr. Liste NB</a>
                          <a class="dropdown-item"
                            href="{% url 'school:level_list_pdf' level.id %}?filles_en_rouge=oui" target="_blank">Impr. Liste Couleur</a>
                        </div>
                    </div>
                </td>
                {% endif %}
            </tr>
            {% endfor %}
            </tbody>
        </table>
        {% include 'partials/level/stats_actions.html' %}
    </form>
</div>
<script>
    $(document).ready(function(){
        $(".checkbox-table").simpleCheckboxTable();
    });
</script>