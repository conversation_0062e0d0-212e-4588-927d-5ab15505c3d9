<div class="tile">
    <h5>Résultats obtenu</h5>
    {% if active_nav == 'moyennes' %}
        <span class="font-weight-bold">Niveau: {{ enrollment.generic_level_fr }}</span> | <span class="font-weight-bold">Classe: {{ enrollment.level_fr }}</span> 
    {% elif active_nav == 'moyennes_arabes' %}
        <span class="font-weight-bold">Niveau: {{ enrollment.generic_level_ar }}</span> | <span class="font-weight-bold">Classe: {{ enrollment.level_ar }}</span> 
    {% endif %}
    <table class="table table-sm table-striped">
        <thead>
            <tr>
                <th>Période</th>
                <th>Moyenne</th>
                <th>Rang</th>
                <th>Bulletin</th>
            </tr>
        </thead>
        <tbody>
            {% for term, result in results.items %}
            <tr>
                <td class="font-weight-bold">{{ term }}</td>
                <td>{{ result.1 }}</td>
                <td>{{ result.2 }}</td>
                <td>{% if result.0 %}<a href="{% url 'exams:student_report' %}?eleve={{ enrollment.id }}&periode={{ result.0 }}&report_type=RW"
                    class="btn btn-sm btn-info">Bulletin</a> {% endif %}</td>
            </tr>
            {% endfor %}
        </tbody>
        <tfoot style="background-color: aquamarine;" class="font-weight-bold">
            <tr>
                <td>DECISION DE FIN D'ANNEE</td>
                <td colspan="3">{% if dfa == 'A' %} ADMIS {% elif not dfa %} NON DISPONIBLE {% else %} NON-ADMIS {% endif %}</td>
            </tr>
        </tfoot>
    </table>
</div>