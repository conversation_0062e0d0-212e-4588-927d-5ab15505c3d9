# Generated by Django 5.1.4 on 2024-12-22 06:41

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('school', '0110_salarypaymentoptions_active'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='staffsalaryformonth',
            options={'verbose_name': 'Décision de paie', 'verbose_name_plural': 'Décisions de paie'},
        ),
        migrations.AddField(
            model_name='staff',
            name='work_hours',
            field=models.PositiveSmallIntegerField(blank=True, null=True, verbose_name="nbre d'heures de travail"),
        ),
        migrations.AddField(
            model_name='staffsalaryformonth',
            name='net_pay',
            field=models.PositiveIntegerField(default=0, verbose_name='salaire NET'),
        ),
        migrations.AlterField(
            model_name='staffsalaryformonth',
            name='salary',
            field=models.PositiveIntegerField(verbose_name='salaire de base'),
        ),
    ]
