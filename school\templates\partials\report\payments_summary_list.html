{% load humanize %}
<div class="tile" id="tile">
    <div class="table-responsive">
        {% if active_nav == 'period_payments' %}
            {% include 'partials/period_form.html' with print_actions=False action_url='/versements/rapports-pdf/?type=period_payments' %}
        {% endif %}
        <table class="table table-striped table-sm" id="datatable">
            <thead class="bg-primary text-white">
            <tr>
                <th>Agent</th>
                <th>Montant total encaissé</th>
                <th>Actions</th>
            </tr>
            </thead>
            <tbody>
            {% for accountant in accountants %}
            <tr>
                <td class="align-middle">{{ accountant.get_full_name }}</td>
                <td class="align-middle">{% if accountant.payments_total %} {{ accountant.payments_total|intcomma }} {% else %} 0 {% endif %} F</td>
                <td class="align-middle">
                    <div class="dropdown">
                        <button class="btn btn-sm dropdown" style="background-color: inherit;" type="button" data-toggle="dropdown" aria-expanded="false">
                            <i data-feather="more-vertical" class="feather-16"></i>
                        </button>
                        <div class="dropdown-menu">
                          {% if active_nav == 'period_payments' %}
                          <a class="dropdown-item" href="{% url 'school:payments_list_pdf' %}?type={{ active_nav }}&user_id={{accountant.id}}&report_type=I&start={{start}}&end={{end}}" onclick="Pace.restart()">Versements inscription</a>
                          <a class="dropdown-item" href="{% url 'school:payments_list_pdf' %}?type={{ active_nav }}&user_id={{accountant.id}}&report_type=S&start={{start}}&end={{end}}" onclick="Pace.restart()">Versements scolarité</a>
                          <a class="dropdown-item" href="{% url 'school:payments_list_pdf' %}?type={{ active_nav }}&user_id={{accountant.id}}&report_type=A&start={{start}}&end={{end}}" onclick="Pace.restart()">Versements annexe</a>
                          <a class="dropdown-item" href="{% url 'school:payments_list_pdf' %}?type={{ active_nav }}&user_id={{accountant.id}}&start={{start}}&end={{end}}" onclick="Pace.restart()">Tous les versements</a>
                          {% else %}
                          <a class="dropdown-item" href="{% url 'school:payments_list_pdf' %}?type={{ active_nav }}&user_id={{accountant.id}}&report_type=I" onclick="Pace.restart()">Versements inscription</a>
                          <a class="dropdown-item" href="{% url 'school:payments_list_pdf' %}?type={{ active_nav }}&user_id={{accountant.id}}&report_type=S" onclick="Pace.restart()">Versements scolarité</a>
                          <a class="dropdown-item" href="{% url 'school:payments_list_pdf' %}?type={{ active_nav }}&user_id={{accountant.id}}&report_type=A" onclick="Pace.restart()">Versements annexe</a>
                          <a class="dropdown-item" href="{% url 'school:payments_list_pdf' %}?type={{ active_nav }}&user_id={{accountant.id}}" onclick="Pace.restart()">Tous les versements</a>

                          {% endif %}
                          <!-- <a class="dropdown-item" href="#" hx-get="" hx-target="#dialog">Exporter vers Excel</a> -->
                        </div>
                    </div>
                </td>
            </tr>
            {% endfor %}
            </tbody>
            <tfoot class="bg-secondary text-white">
                <tr>
                    <td>TOTAL</td>
                    <td>{% if payments_total %} {{ payments_total|intcomma }} {% else %} 0 {% endif %} F</td>
                    <td></td>
                </tr>
            </tfoot>
        </table>
    </div>
</div>