{% load widget_tweaks %}
<div class="row" hx-get="{% url 'exams:results' %}?type={{type}}&lang={{lang}}&option={{ request.GET.option }}"
     hx-trigger="saved from:body"
     hx-target="#table_container"
     hx-include="this">
    <div class="col">
        <!-- Main Card -->
        <div class="card shadow-sm mb-4">

            <!-- Card Body -->
            <div class="card-body">
                <!-- Instructions Panel -->

                <!-- Period Selection Form -->
                <form action="" method="post" class="mb-4">
                    <div class="form-row d-flex justify-content-center align-items-center">
                        <div class="col-md-6 col-lg-4 form-group">
                            <label for="level" class="font-weight-bold">
                                <span data-feather="calendar" class="feather-16 align-middle mr-1"></span>
                                Sélectionnez une classe
                            </label>
                            <div class="input-group">
                                <select name="level" id="level" class="form-control"
                                        hx-get="{% url 'exams:combined_results_ar_fr' %}?type={{type}}&lang={{lang}}&option={{ request.GET.option }}"
                                        hx-target="#table_container"
                                        hx-indicator="#loading-indicator"
                                        onchange="if(!this.value) {document.body.querySelector('#print-pdf-btn').classList.add('d-none')} else {document.body.querySelector('#print-pdf-btn').classList.remove('d-none');}">
                                    <option value="">-- Choisir une classe --</option>
                                    {% for level in levels %}
                                    <option value="{{ level.id }}">{{ level }}</option>
                                    {% endfor %}
                                </select>
                                <div class="input-group-append">
                                    <span class="input-group-text bg-light text-primary">
                                        <span id="loading-indicator" class="htmx-indicator spinner-border spinner-border-sm" role="status"></span>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <a href="" class="btn btn-outline-primary mt-3 d-none" style="max-height: fit-content; transition: .2s ease-in-out;" id="print-pdf-btn">
                            <span data-feather="printer" class="feather-16 align-middle mr-1"></span>
                            Imprimer PDF
                        </a>
                    </div>
                </form>

                <!-- Results Container -->
                <div class="results-container">
                    <div id="table_container" class="table-responsive">
                        <!-- Table will be loaded here via HTMX -->
                         {% include 'partials/content_placehoder.html' %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize feather icons
        if (typeof feather !== 'undefined') {
            feather.replace();
        }

        // Add event listener to level select
        const levelSelect = document.getElementById('level');
        if (levelSelect) {
            levelSelect.addEventListener('change', function() {
                if (this.value) {
                    // Show loading state
                    document.getElementById('loading-indicator').style.display = 'inline-block';
                }
            });
        }
    });
</script>