<!-- Mobile View -->
 {% load humanize %}
<div class="d-md-none">
  {% for enrollment in enrollments %}
  <div class="student-card">
    <div class="student-card-header">
      <img src="{{ enrollment.student.photo.url|default:enrollment.student.blank_photo }}"
           class="student-photo lazy"
           alt="Photo">
      <div class="student-info">
        <div class="student-name">{{ enrollment.student.get_full_name }}</div>
        <small class="text-muted">{{ enrollment.student.student_id|default:enrollment.student.identifier }}</small>
      </div>
    </div>
    <div class="student-details">
      <div>Classe:</div>
      <div>{{ enrollment.level_fr|default:"-" }}</div>
      
      <div>Statut:</div>
      <div>
        <span class="status-badge {% if enrollment.active %}status-active{% else %}status-inactive{% endif %}">
          {{ enrollment.get_status_display }}
        </span>
      </div>
      
      <div>À payer:</div>
      <div class="amount-cell">{{ enrollment.amount|intcomma }}</div>
      
      <div>Payé:</div>
      <div class="amount-cell {% if enrollment.paid %}amount-positive{% endif %}">
        {{ enrollment.paid|intcomma|default:"0" }}
      </div>
      
      <div>Reste:</div>
      <div class="amount-cell {% if enrollment.remaining == 0 %}amount-positive{% elif enrollment.remaining > 0 %}amount-warning{% endif %}">
        {% if enrollment.remaining == 0 and enrollment.get_fees_total > 0 %}
          Soldé
        {% else %}
          {{ enrollment.remaining|intcomma|default:"-" }}
        {% endif %}
      </div>
    </div>
    <div class="mobile-actions">
      <button class="btn btn-sm btn-primary"
              hx-get="{% url 'school:student_edit' enrollment.student.id %}"
              hx-target="#dialog">
        <i data-feather="edit-2"></i>
      </button>
      <button class="btn btn-sm btn-light"
              hx-get="{% url 'school:payment_add' %}?student_id={{ enrollment.student.identifier }}"
              hx-target="#dialog">
        <i data-feather="dollar-sign"></i>
      </button>
      <div class="dropdown">
        <button class="btn btn-sm btn-info dropdown-toggle"
                type="button"
                data-toggle="dropdown">
          <i data-feather="file-text"></i>
        </button>
        <div class="dropdown-menu dropdown-menu-right">
          <a class="dropdown-item" href="#">Relevé de notes</a>
          <a class="dropdown-item" href="#">Certificat de scolarité</a>
          <a class="dropdown-item" href="#">Fiche d'inscription</a>
        </div>
      </div>
    </div>
  </div>
  {% endfor %}
</div>