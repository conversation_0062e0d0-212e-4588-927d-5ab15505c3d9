# Generated by Django 5.1 on 2024-11-11 11:35

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('school', '0106_staffsalaryformonth_status'),
    ]

    operations = [
        migrations.AlterField(
            model_name='payment',
            name='created_at',
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
        migrations.AlterField(
            model_name='staffsalaryformonth',
            name='staff',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='school.staff', verbose_name='employé'),
        ),
        migrations.AlterUniqueTogether(
            name='enrollment',
            unique_together={('student', 'year', 'school')},
        ),
    ]
