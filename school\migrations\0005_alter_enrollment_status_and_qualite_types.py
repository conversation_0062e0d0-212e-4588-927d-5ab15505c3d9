# Generated by Django 4.2.3 on 2023-07-13 14:47

import cloudinary.models
import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('school', '0004_add_cycle_field_to_generic_level_and_alter_fees_fields_on_enrollment'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='enrollment',
            options={'ordering': ['-created_at'], 'verbose_name': 'inscription'},
        ),
        migrations.AlterModelOptions(
            name='payment',
            options={'ordering': ['-updated_at'], 'verbose_name': 'versement'},
        ),
        migrations.RemoveField(
            model_name='enrollment',
            name='stays_down',
        ),
        migrations.AddField(
            model_name='enrollment',
            name='qualite',
            field=models.CharField(blank=True, choices=[('Aff', 'Rédoublant'), ('Naff', 'Non-Rédoublant')], default='Nred', max_length=4, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='enrollment',
            name='status',
            field=models.CharField(blank=True, choices=[('Aff', 'Affecté'), ('Naff', 'Non-Affecté')], default='Naff', max_length=4, null=True),
        ),
        migrations.AlterField(
            model_name='payment',
            name='amount',
            field=models.IntegerField(validators=[django.core.validators.MinValueValidator(100)], verbose_name='montant versé'),
        ),
        migrations.AlterField(
            model_name='student',
            name='birth_day',
            field=models.PositiveSmallIntegerField(validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(31)], verbose_name='né le'),
        ),
        migrations.AlterField(
            model_name='student',
            name='certificate_img',
            field=cloudinary.models.CloudinaryField(blank=True, max_length=255, null=True, verbose_name='extrait de naissance'),
        ),
        migrations.AlterField(
            model_name='student',
            name='photo',
            field=cloudinary.models.CloudinaryField(blank=True, max_length=255, null=True, verbose_name="photo d'identité"),
        ),
    ]
