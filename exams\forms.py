from django import forms
from django.db import connection 
from django.db.models import Q, Prefetch, OuterRef
from school.models import Level, Enrollment, TeacherLevel2
from . import models
from main import utils

class LevelForm(forms.Form):
    level = forms.ModelChoiceField(queryset=None) 
    subject = forms.ModelChoiceField(queryset=None) 
    term = forms.ModelChoiceField(queryset=None) 

    def __init__(self, level_qs, subject_qs=None, term_qs=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['level'].queryset = level_qs
        self.fields['subject'].queryset = subject_qs
        self.fields['term'].queryset = term_qs


class GradeInputForm(forms.ModelForm):
    class Meta:
        model = models.Grade
        fields = ['enrollment', 'school_term', 'subject', 'grade']


class TermsForm(forms.Form):
    term = forms.ModelChoiceField(queryset=None)
    choices = (
        (utils.CYCLE_PRIMARY, 'Primaire'),
        (utils.CYCLE_SECONDARY, 'Secondaire'),
    )
    cycle = forms.ChoiceField(choices=choices, required=False)

    def __init__(self, user, term_qs=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['term'].queryset = term_qs

        if user.school.cycle == utils.CYCLE_PRIMARY:
            self.fields['cycle'].choices = (
                (utils.CYCLE_PRIMARY, 'Primaire'),
            )
        elif user.school.cycle == utils.CYCLE_SECONDARY:
            self.fields['cycle'].choices = (
                (utils.CYCLE_SECONDARY, 'Secondaire'),
            )

        if term_qs.exists():
            self.fields['term'].initial = term_qs.first()


class GradeBatchEditForm(forms.Form):
    student_id = forms.CharField(
        max_length=15, initial='', 
        label='Matricule/Identifiant',
        required=False)
    full_name = forms.CharField(required=False, label='Nom et prénoms')

    def __init__(self, request, level=None, term=None, student_id=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        year = level.year
        self.term = term
        self.request = request
        user = request.user
        
        self.subjects = []

        if user.role != utils.ROLE_TEACHER:
            self.subjects = list(models.LevelSubject.objects.for_school(
                user=user, level_name=level.generic_level,
                education=level.education, year=year)\
                .filter(active=True) \
                .select_related('subject') \
                .only(
                    'subject__abbreviation', 
                    'subject__translation', 
                    'order', 'id', 'max', 'active')
                )
        else:
            teacher_level = TeacherLevel2.objects.filter(teacher__user__id=user.id, level=level).first()
            if level.generic_level.cycle == utils.CYCLE_PRIMARY:
                self.subjects = list(models.LevelSubject.objects.filter(
                    level=level.generic_level, year=level.year,
                    active=True, subject__education=level.education,
                    school=level.school).select_related('subject') \
                    .only(
                            'subject__abbreviation', 
                            'subject__translation', 
                            'order', 'id', 'max', 'active')
                )
            else:
                self.subjects = list(teacher_level.subjects.select_related('subject') \
                    .only(
                            'subject__abbreviation', 
                            'subject__translation', 
                            'order', 'id', 'max', 'active') \
                    .order_by('active'))
        
        queryset = Enrollment.objects.for_user(user, year=year)\
            .select_related('student') \
            .prefetch_related(
                Prefetch('grade_set', queryset=models.Grade.objects \
                    .filter(
                        school_term=term, 
                        subject__active=True, 
                        subject__in=self.subjects) \
                    .select_related('school_term', 'subject__subject') \
                    .order_by('subject__order')\
                    .only(
                        'enrollment__id', 'grade', 'subject__order', 
                        'subject__subject__id', 
                        'subject__subject__abbreviation', 
                        'subject__subject__translation', 
                        'school_term__id'),
                to_attr='filtered_grades')
            ).filter(active=True)
        
        self.next_student = ''
        self.previous_student = request.GET.get('previous', '')
        self.students_count = 0
        self.marked_index = 1
        queryset = utils.filter_enrollment_by_level(level, queryset)
        
        education = level.education
        if education == utils.EDUCATION_FRENCH:
            queryset = queryset.order_by('student__last_name', 'student__first_name')
        else:
            queryset = queryset.order_by('student__full_name_ar')

        if student_id:
            data = queryset.values('student__student_id', 'student__identifier')
            current_index = None
            for i, item in enumerate(data):
                if item.get('student__identifier') == student_id \
                    or item.get('student__student_id') == student_id:
                    current_index = i
                    self.marked_index = i +1
                    break
            
            if len(data) > (current_index + 1):
                next_id = data[current_index + 1].get('student__student_id') or \
                    data[current_index + 1].get('student__identifier')
                self.next_student = next_id 
                self.students_count = len(data)
            if len(data) > 1 and current_index > 0:
                previous_id = ''
                try:
                    previous_id = data[current_index - 1].get('student__student_id') or \
                        data[current_index - 1].get('student__identifier')
                    self.previous_student = previous_id
                except:
                    pass

            queryset = queryset.filter(
                    Q(student__student_id=student_id) | \
                    Q(student__identifier=student_id)
                )

        queryset = queryset.only(
            'student__last_name', 'student__first_name',
            'student__full_name_ar',
            'active', 'student__id', 'student__student_id', 
            'student__identifier')

        obj = queryset.first()

        if queryset.count() > 1:
            second = queryset[1]
            self.next_student = second.student.student_id or second.student.identifier
            
        self.enrollment = obj
        if not student_id:
            student_id = obj.student.student_id or obj.student.identifier

        self.fields['student_id'].initial = student_id
        self.fields['student_id'].widget.attrs['value'] = student_id
        self.fields['student_id'].widget.attrs['disabled'] = True

        if (education == utils.EDUCATION_FRENCH):
            self.fields['full_name'].initial = str(obj)
            self.fields['full_name'].widget.attrs['value'] = str(obj)
        else:
            self.fields['full_name'].initial = obj.student.full_name_ar or str(obj)
            self.fields['full_name'].widget.attrs['value'] = obj.student.full_name_ar or str(obj)
        self.fields['full_name'].widget.attrs['disabled'] = True

        grades = {}
        if self.enrollment:
            for grade in self.enrollment.filtered_grades:
                grades[str(grade.subject.id)] = grade.grade

        for i, subject in enumerate(self.subjects):
            self.fields[f'subject_{subject.id}'] = forms.DecimalField(
                initial=grades.get(str(subject.id), ''), 
                label=subject.subject.abbreviation if education == utils.EDUCATION_FRENCH else subject.subject.name, 
                help_text=subject.subject.translation if education == utils.EDUCATION_ARABIC else '',
                max_value=subject.max, min_value=0, max_digits=4,
                decimal_places=2, required=False
            )
            self.fields[f'subject_{subject.id}'].initial = grades.get(str(subject.id), '')
            if i == 0:
                self.fields[f'subject_{subject.id}'].widget.attrs['setfocus'] = 'setfocus'
    
    def save_grades(self):
        grades = {}

        for key, value in self.cleaned_data.items():
            if str(key).startswith('subject'):
                subject_id = str(key).split('_')[1]
                grades[str(subject_id)] = value

        objs_to_update = []
        objs_to_create = []
        total = 0
        if self.enrollment.filtered_grades:
            for grade in self.enrollment.filtered_grades:
                if str(grade.subject.id) in grades:
                    grade_value = grades.get(str(grade.subject.id), '')
                    grade.grade = grade_value
                    if grade_value:
                        total += grade_value
                    objs_to_update.append(grade)
        else:
            for subject in self.subjects:
                if str(subject.id) in grades:
                    grade_value = grades.get(str(subject.id), '')
                    if grade_value:
                        total += grade_value
                    objs_to_create.append(
                        models.Grade(enrollment=self.enrollment, 
                            school_term=self.term,
                            grade=grade_value,
                            subject=subject))

        
        if objs_to_update:
            models.Grade.objects.bulk_update(objs_to_update, fields=['grade'])
        
        if objs_to_create:
            models.Grade.objects.bulk_create(objs_to_create)
        
        qs = models.TermResult.objects.filter(
            enrollment=self.enrollment, 
            school_term=self.term)
        
        if qs.exists():
            qs.update(total=grade_value or 0)
        else:
            models.TermResult.objects.create(
            enrollment=self.enrollment, 
            school_term=self.term, total=grade_value or 0)