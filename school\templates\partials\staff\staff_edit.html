{% extends 'partials/modal.html' %}
{% load widget_tweaks %}
{% load i18n %}

{% block modal_title %} {{ form_title }} {% endblock %}

{% block modal_body %}
    {% csrf_token %}
    <div class="container">
        <div class="row">
            <!-- Informations Générales -->
            <div class="col-md-7">
                <h3>{% trans "Informations Générales" %}</h3>
                <div class="row">
                    <div class="form-group col-md-4">
                        <label class="{% if form.last_name.field.required %} font-weight-bold {% endif %}" for="id_last_name">{% trans "Nom" %} {% if form.last_name.field.required %} <span class="text-danger">*</span>{% endif %}</label>
                        {% render_field form.last_name class="form-control" %}
                    </div>
                    <div class="form-group col-md-5">
                        <label class="{% if form.first_name.field.required %} font-weight-bold {% endif %}" for="id_first_name">{% trans "Prénom" %} {% if form.first_name.field.required %} <span class="text-danger">*</span>{% endif %}</label>
                        {% render_field form.first_name class="form-control" %}
                    </div>
                    <div class="form-group col-md-3">
                        <label class="{% if form.gender.field.required %} font-weight-bold {% endif %}" for="id_gender">{% trans "Sexe" %} {% if form.gender.field.required %} <span class="text-danger">*</span>{% endif %}</label>
                        {% render_field form.gender class="form-control" %}
                    </div>
                </div>
                <div class="row">
                    <div class="form-group col-4">
                        <label class="{% if form.birth_day.field.required %} font-weight-bold {% endif %}" for="id_birth_day">{% trans "Jour Naiss." %} {% if form.birth_day.field.required %} <span class="text-danger">*</span>{% endif %}</label>
                        {% render_field form.birth_day class="form-control" %}
                    </div>
                    <div class="form-group col-4">
                        <label class="{% if form.birth_month.field.required %} font-weight-bold {% endif %}" for="id_birth_month">{% trans "Mois" %} {% if form.birth_month.field.required %} <span class="text-danger">*</span>{% endif %}</label>
                        {% render_field form.birth_month class="form-control" %}
                    </div>
                    <div class="form-group col-4">
                        <label class="{% if form.birth_year.field.required %} font-weight-bold {% endif %}" for="id_birth_year">{% trans "Année" %} {% if form.birth_year.field.required %} <span class="text-danger">*</span>{% endif %}</label>
                        {% render_field form.birth_year class="form-control" %}
                    </div>
                </div>
                <div class="form-group">
                    <label class="{% if form.birth_place.field.required %} font-weight-bold {% endif %}" for="id_birth_place">{% trans "Lieu de Naissance" %} {% if form.birth_place.field.required %} <span class="text-danger">*</span>{% endif %}</label>
                    {% render_field form.birth_place class="form-control" %}
                </div>
                <div class="row">
                    <div class="form-group col-md-4">
                        <label class="{% if form.phone.field.required %} font-weight-bold {% endif %}" for="id_phone">{% trans "Contact Principal" %} {% if form.phone.field.required %} <span class="text-danger">*</span>{% endif %}</label>
                        {% render_field form.phone class="form-control" %}
                    </div>
                    <div class="form-group col-md-4">
                        <label class="{% if form.phone2.field.required %} font-weight-bold {% endif %}" for="id_phone2">{% trans "Autre Contacts" %} {% if form.phone2.field.required %} <span class="text-danger">*</span>{% endif %}</label>
                        {% render_field form.phone2 class="form-control" %}
                    </div>
                    <div class="form-group col-md-4">
                        <label class="{% if form.email.field.required %} font-weight-bold {% endif %}" for="id_email">{% trans "E-mail" %} {% if form.email.field.required %} <span class="text-danger">*</span>{% endif %}</label>
                        {% render_field form.email class="form-control" %}
                    </div>
                </div>
                <div class="row">
                    <div class="form-group col">
                        <label class="{% if form.parent.field.required %} font-weight-bold {% endif %}" for="id_parent">{% trans "Personne à Contacter en Cas d'Urgence" %} {% if form.parent.field.required %} <span class="text-danger">*</span>{% endif %}</label>
                        {% render_field form.parent class="form-control" %}
                    </div>
                    <div class="form-group col">
                        <label class="{% if form.parent_phone.field.required %} font-weight-bold {% endif %}" for="id_parent_phone">{% trans "Contact d'Urgence" %} {% if form.parent_phone.field.required %} <span class="text-danger">*</span>{% endif %}</label>
                        {% render_field form.parent_phone class="form-control" %}
                    </div>
                </div>
                <div class="row">
                    <div class="form-group col">
                        <label class="{% if form.id_number.field.required %} font-weight-bold {% endif %}" for="id_id_number">{% trans "N° CNI/Att" %} {% if form.id_number.field.required %} <span class="text-danger">*</span>{% endif %}</label>
                        {% render_field form.id_number class="form-control" %}
                    </div>
                </div>
            </div>

            <!-- Emploi -->
            <div class="col-md-3">
                <h3>{% trans "Emploi" %}</h3>
                <div class="form-group">
                    <label class="{% if form.salary.field.required %} font-weight-bold {% endif %}" for="id_salary">{% trans "Salaire" %} {% if form.salary.field.required %} <span class="text-danger">*</span>{% endif %}</label>
                    {% render_field form.salary class="form-control" %}
                </div>
                <div class="form-group">
                    <label class="{% if form.work_hours.field.required %} font-weight-bold {% endif %}" for="id_work_hours">{% trans "Nbre d'heures de travail/mois" %} {% if form.work_hours.field.required %} <span class="text-danger">*</span>{% endif %}</label>
                    {% render_field form.work_hours class="form-control" %}
                </div>
                <div class="form-group">
                    <label class="{% if form.cnps.field.required %} font-weight-bold {% endif %}" for="id_cnps">{% trans "N° CNPS" %} {% if form.cnps.field.required %} <span class="text-danger">*</span>{% endif %}</label>
                    {% render_field form.cnps class="form-control" %}
                </div>
                <div class="form-group">
                    <label class="{% if form.date_enlisted.field.required %} font-weight-bold {% endif %}" for="id_date_enlisted">{% trans "Embauché le" %} {% if form.date_enlisted.field.required %} <span class="text-danger">*</span>{% endif %}</label>
                    {% render_field form.date_enlisted class="form-control" %}
                </div>
                <div class="form-group">
                    <label class="{% if form.role.field.required %} font-weight-bold {% endif %}" for="id_role">{% trans "Emploi" %} {% if form.role.field.required %} <span class="text-danger">*</span>{% endif %}</label>
                    {% render_field form.role class="form-control" %}
                </div>
                <div class="form-group">
                    <label class="{% if form.education.field.required %} font-weight-bold {% endif %}" for="id_education">{% trans "Education" %} {% if form.education.field.required %} <span class="text-danger">*</span>{% endif %}</label>
                    {% render_field form.education class="form-control" %}
                </div>
            </div>
            <!-- Photo -->
            <div class="col-md-2">
                <h3>{% trans "Photo" %}</h3>
                <div class="form-group text-center">
                    {% if staff and staff.photo %}
                        <img alt="Photo de l'élève" height="100px" width="90px" 
                        src="{{ staff.photo.url }}" id="photo" loading="lazy" class="rounded">
                    {% else %}
                        <img alt="Photo de l'élève" height="100px" width="90px" 
                            src="{{ blank_photo }}" id="photo" loading="lazy" 
                            class="rounded">
                    {% endif %}
                </div>
                <div class="form-group">
                    <input type="file" class="form-control" name="photo" 
                    id="{{ form.photo.id_for_label }}"
                    accept=".jpg, .png, .jpeg" onchange="
                        const selectedImage = document.getElementById('photo');
                        const reader = new FileReader();
    
                        if (this.files && this.files[0]) {
                            reader.onload = function(e) {
                                selectedImage.src = e.target.result;
                            };
                            reader.readAsDataURL(this.files[0]);
                        } else {
                            selectedImage.src = '/static/img/avatar.jpg';
                        }
                    ">    
                </div>
            </div>
        </div>
    </div>

    <script>
        flatpickr("[name*='date']", {
        dateFormat: "d/m/Y",
        enableTime: false,
        allowInput: true,
        timeZone: "Africa/Abidjan",
        disableMobile: "true",
        locale: "fr",
        maxDate: 'today'
    });
</script>
{% endblock %}


{% block modal_footer %}
    <button type="submit" href="" class="btn btn-success">
        <span class="feather-16 align-middle" data-feather="check-circle"></span>
        Enregistrer
    </button>
{% endblock %}
