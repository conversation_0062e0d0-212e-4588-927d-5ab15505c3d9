{% load widget_tweaks %}
{% csrf_token %}
<div class="bulk-actions d-flex justify-content-center align-items-center">
    <div class="container-fluid d-flex flex-column align-items-center justify-content-center">
        <span class="text-center">Sélectionner la classe à attribuer</span>
        <div class="d-flex">
          {% if subschools %}
            <select name="subschool" id="subschool" class="form-control mr-2" style="height: 32px; min-width: 120px !important;"
            hx-get="{% url 'school:subschool_levels' %}?input_name=action&education={{ request.GET.education}}"
            hx-target="#action"
            hx-swap="outerHTML">
                <option value="">-- Sélectionner école</option>
                {% for subschool in subschools %}
                <option value="{{ subschool.id }}">{{ subschool }}</option>
                {% endfor %}
            </select>
          {% endif %}
            <select name="action" id="action" class="form-control mr-2" style="height: 32px;" hx-swap="outerHTML">
                {% for level in education_levels %}
                <option value="{{ level.id }}">{{ level }}</option>
                {% endfor %}
            </select>
            <a href="{{ request.path }}" hx-post="{{ request.path }}" hx-vals='{"education": "{{ request.GET.education }}"}' class="btn btn-sm btn-secondary" style="min-width: 70px !important;">Attribuer</a>
            <span class="selected-count bg-light text-info ml-3 align-middle" style="min-width: fit-content;">
              <span id="selected-count" class="align-middle">0</span> élèves
            </span>
        </div>
      </div>
    </div>
</div>