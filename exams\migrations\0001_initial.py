# Generated by Django 4.2.4 on 2023-08-11 16:01

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('school', '0028_alter_related_names_for_level_and_generic_level_fields'),
    ]

    operations = [
        migrations.CreateModel(
            name='SchoolTerm',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('coefficient', models.PositiveSmallIntegerField(default=1)),
                ('start', models.DateField(blank=True, null=True)),
                ('end', models.DateField(blank=True, null=True)),
                ('active', models.BooleanField(default=True)),
                ('school', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='school.school')),
            ],
            options={
                'verbose_name': 'examen par école',
                'verbose_name_plural': 'examens par école',
            },
        ),
        migrations.CreateModel(
            name='Term',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255, verbose_name='libellé')),
                ('abbreviation', models.CharField(max_length=12)),
                ('education', models.CharField(choices=[('A', 'Arabe'), ('F', 'Français')], default='F', max_length=255)),
                ('cycle', models.CharField(choices=[('P', 'Primaire'), ('S', 'Secondaire')], default='P', max_length=1)),
                ('translation', models.CharField(blank=True, max_length=255, null=True, verbose_name='traduction en arabe')),
                ('code', models.CharField(max_length=10, null=True, verbose_name='code période')),
            ],
            options={
                'verbose_name': 'examen',
            },
        ),
        migrations.CreateModel(
            name='YearResult',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('total', models.PositiveSmallIntegerField(default=0, verbose_name='total')),
                ('average', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='moyenne')),
                ('rank', models.PositiveSmallIntegerField(default=0)),
                ('enrollment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='school.enrollment')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='TermResult',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total', models.PositiveSmallIntegerField(default=0, verbose_name='total')),
                ('average', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='moyenne')),
                ('rank', models.PositiveSmallIntegerField(default=0)),
                ('enrollment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='school.enrollment')),
                ('school_term', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='exams.schoolterm')),
            ],
        ),
        migrations.CreateModel(
            name='Subject',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, unique=True, verbose_name='libellé')),
                ('translation', models.CharField(blank=True, max_length=255, null=True, verbose_name='traduction')),
                ('cycle', models.CharField(choices=[('P', 'Primaire'), ('S', 'Secondaire')], max_length=1, null=True, verbose_name='cycle')),
                ('abbreviation', models.CharField(max_length=10, verbose_name='abbréviation')),
                ('education', models.CharField(choices=[('A', 'Arabe'), ('F', 'Français')], default='A', max_length=1, verbose_name='éducation')),
                ('code', models.CharField(max_length=25, unique=True, verbose_name='code matière')),
                ('school', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='school.school')),
            ],
            options={
                'verbose_name': 'matière',
            },
        ),
        migrations.AddField(
            model_name='schoolterm',
            name='term',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='exams.term'),
        ),
        migrations.AddField(
            model_name='schoolterm',
            name='year',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='school.year'),
        ),
        migrations.CreateModel(
            name='LevelSubject',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('coefficient', models.PositiveIntegerField(default=1, verbose_name='coefficient')),
                ('active', models.BooleanField(default=True, verbose_name='utilisé?')),
                ('order', models.PositiveIntegerField(default=1, verbose_name="ordre d'apparution")),
                ('max', models.PositiveIntegerField(default=1, verbose_name='matière sur')),
                ('level', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='school.genericlevel', verbose_name='niveau')),
                ('school', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='school.school')),
                ('subject', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='exams.subject')),
            ],
            options={
                'verbose_name': 'matière par niveau',
                'ordering': ['order'],
            },
        ),
        migrations.CreateModel(
            name='Grade',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('grade', models.PositiveSmallIntegerField()),
                ('enrollment', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='school.enrollment')),
                ('school_exam', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='exams.schoolterm')),
                ('subject', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='exams.subject')),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'note',
            },
        ),
    ]
