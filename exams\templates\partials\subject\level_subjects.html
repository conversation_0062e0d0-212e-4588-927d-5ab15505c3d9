<div class="row" hx-get="{{ request.path }}?lang={{lang}}&niveau={{active_nav}}" hx-trigger="saved from:body" hx-target="#app-content">
    <form class="col" method="post" hx-post="{% url 'exams:level_subject_reorder' %}?level={{ active_nav }}&lang={{ lang }}" hx-include="this">
        {% csrf_token %}
        <div class="tile">
            {% if active_nav %}
            <div class="tile-title-w-btn d-flex justify-content-between">
                <div class="btn-group">
                    <a class="btn btn-success btn-sm" href="" 
                       hx-get="{% url 'exams:level_subject_add' active_nav lang %}" 
                       hx-target="#dialog">+ Ajouter une matière</a>
                </div>
                <div class="btn-group">
                    <a class="btn btn-warning btn-sm" href="" 
                       hx-get="{% url 'exams:subject_add' %}?lang={{lang}}" 
                       hx-target="#dialog">+ Enregistrer nouvelle matière</a>
                </div>
            </div>
            {% endif %}
            <div class="alert alert-info">
                <span data-feather="info" class="feather-16 align-middle"></span>
                Vous pouvez maintenir et déplacer les icones 
                <span data-feather="move" class="feather-16 align-middle"></span> pour reorganiser les matières
            </div>
            <div class="table-responsive">
                <table class="table table-striped table-sm" id="this-table">
                    <thead class="bg-primary text-white">
                    <tr>
                        <th>ORDRE</th>
                        <th class="d-none">HIDDEN</th>
                        <th>LIBELLE</th>
                        <th>ABBREVIATION</th>
                        {% if lang == 'A' %}
                        <th>TRADUCTION</th>
                        {% endif %}
                        <th>CODE</th>
                        <th>MATIERE<br>SUR</th>
                        <th>COEF.</th>
                        <th>PRIS EN <br>COMPTE</th>
                        <th>ACTIONS</th>
                    </tr>
                    </thead>
                    <tbody>
                    {% for level_subject in level_subjects %}
                    <tr>
                        <td class="align-middle" title="Maintenir et déplacer pour reordonner" data-toggle="tooltip">
                           <span data-feather="move" class="feather-16 align-middle"></span> 
                           {{ forloop.counter }} 
                        </td>
                        <td class="align-middle d-none"><input type="number" name="order_{{ level_subject.id }}" id="order_{{ level_subject.id }}" value="{{ level_subject.order }}" class="align-middle d-none" style="width: 30px;"></td>
                        <td class="align-middle">{{ level_subject.subject.name }}</td>
                        <td class="align-middle">{{ level_subject.subject.abbreviation }}</td>
                        {% if lang == 'A' %}
                        <td class="align-middle">{{ level_subject.subject.translation|default_if_none:'' }}</td>
                        {% endif %}
                        <td class="align-middle">{{ level_subject.subject.code|default_if_none:'-' }}</td>
                        <td class="align-middle">{{ level_subject.max }}</td>
                        <td class="align-middle"><a href="" class="btn btn-sm border"
                            hx-get="{% url 'exams:level_subject_edit' level_subject.pk %}"
                            hx-target="#dialog">{{ level_subject.coefficient }}
                         </a></td>
                        <td class="align-middle">
                            <a href="" class="btn btn-sm btn-{% if level_subject.active %}success{% else %}danger{% endif %}"
                               hx-get="{% url 'exams:level_subject_status' level_subject.pk %}"
                               hx-target="#dialog">
                                <span class="feather-16 align-middle" data-feather="{% if level_subject.active %}check{% else %}x{% endif %}">
                                </span> {% if level_subject.active %}OUI{% else %}NON{% endif %}
                            </a>
                        </td>
                        <td>
                            <a href="" class="btn btn-sm btn-warning" hx-get="{% url 'exams:level_subject_edit' level_subject.pk %}" hx-target="#dialog">
                                <span data-feather="edit" class="feather-16"></span>
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                    </tbody>
                </table>
            </div>
            <div class="">
                <button type="submit" href="" class="btn btn-success">
                    <span data-feather="check-square" class="feather-16"></span>
                    Enregistrer
                </button>
            </div>
        </div>
    </form>
</div>

<script>
    $(document).ready(function() {
        $('#this-table').DataTable({
            // dom: 'Bfrtip',
            lengthMenu: [
                [ 25, 50, 100, 200, 300],
                [ '25', '50', '100', '200', '300']
            ],
            rowReorder: true,
            drawCallback: function() {
                feather.replace();
                htmx.process(document.body);
            },
        });
        $('[data-toggle="tooltip"]').tooltip();

        $('#this-table').on('row-reorder.dt', function (e, details, edit) {
            $('#this-table tbody tr').each(function(index) {
                // Assuming your input element is the first input in each row
                var inputElement = $(this).find('input:first');

                // Update the input element value to the new position
                inputElement.val(index + 1); // Adding 1 because index is zero-based
            });
        });
    })
</script>