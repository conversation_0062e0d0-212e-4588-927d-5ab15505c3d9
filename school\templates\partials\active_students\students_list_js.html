<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize components
    if (typeof(feather) !== 'undefined') {
      feather.replace();
        // Initialize lazy loading
      $("img.lazy").lazyload();
    }
    
  
    
    // Initialize checkboxes
    initializeCheckboxes();
    
    // Initialize tooltips
    initializeRowClicks()
  
  });
  
    if (typeof(feather) !== 'undefined') {
      feather.replace();
        // Initialize lazy loading
      $("img.lazy").lazyload(
        // {container: $(".table-responsive")}
      );
      initializeFilters();
      initializeCheckboxes();
      initializeRowClicks();
    }
    
    function initializeRowClicks() {
    const rows = document.querySelectorAll('.student-row');
    
    rows.forEach(row => {
      row.addEventListener('click', (e) => {
        // Don't toggle if clicking action buttons
        if (e.target.closest('.action-button') || e.target.closest('.dropdown')) {
          return;
        }
        
        const checkbox = row.querySelector('.row-checkbox');
        if (checkbox) {
          checkbox.checked = !checkbox.checked;
          checkbox.dispatchEvent(new Event('change'));
        }
      });
    });
  
    var checkmark = document.querySelector('#select-all-checkmark');
      checkmark.addEventListener('click', function() {
        const checkbox = this.previousElementSibling;
        if (checkbox && checkbox.classList.contains('custom-checkbox')) {
          checkbox.checked = !checkbox.checked;
          checkbox.dispatchEvent(new Event('change'));
        }
    });
  }
  
  function initializeCheckboxes() {
    const selectAll = document.getElementById('select-all');
    const rowCheckboxes = document.querySelectorAll('.row-checkbox');
    const selectedCountElement = document.getElementById('selected-count');
    const bulkActions = document.querySelector('.bulk-actions');
    
    function updateSelectedCount() {
      const checkedCount = document.querySelectorAll('.row-checkbox:checked').length;
      selectedCountElement.textContent = checkedCount;
      bulkActions.classList.toggle('visible', checkedCount > 0);
    }
  
    if (selectAll) {
      selectAll.addEventListener('change', function() {
        const isChecked = this.checked;
        rowCheckboxes.forEach(checkbox => {
          checkbox.checked = isChecked;
          updateRowSelection(checkbox);
        });
        updateSelectedCount();
      });
    }
    
    rowCheckboxes.forEach(checkbox => {
      checkbox.addEventListener('change', function() {
        updateRowSelection(this);
        updateSelectAllState();
        updateSelectedCount();
      });
    });
  
  }
  
  
  function updateRowSelection(checkbox) {
    const row = checkbox.closest('tr');
    if (row) {
      row.classList.toggle('selected', checkbox.checked);
    }
  }
  
  function updateSelectAllState() {
    const selectAll = document.getElementById('select-all');
    const rowCheckboxes = document.querySelectorAll('.row-checkbox');
    const checkedBoxes = document.querySelectorAll('.row-checkbox:checked');
    
    if (selectAll) {
      selectAll.checked = rowCheckboxes.length === checkedBoxes.length;
      selectAll.indeterminate = checkedBoxes.length > 0 && 
                               checkedBoxes.length < rowCheckboxes.length;
    }
  }
  
  
  function checkIfImageExists(url, callback) {
    const img = new Image();
    img.src = url;
    
    if (img.complete) {
      callback(true);
    } else {
      img.onload = () => callback(true);
      img.onerror = () => callback(false);
    }
  }
  
  document.addEventListener("DOMContentLoaded", function() {
    initializeFilters();
  });
  
  function initializeFilters() {
      // Toggle filter panel
      document.getElementById('filterToggle').addEventListener('click', function(e) {
          e.stopPropagation();
          this.classList.toggle('active');
          document.getElementById('filterPanel').classList.toggle('show');
      });
  
      // Close filter panel when clicking outside
      document.addEventListener('click', function(e) {
          var filterDropdown = document.querySelector('.filter-dropdown');
          if (!filterDropdown.contains(e.target)) {
              document.getElementById('filterPanel').classList.remove('show');
              document.getElementById('filterToggle').classList.remove('active');
          }
      });
  }

  $('[data-toggle="tooltip"]').tooltip();

</script>