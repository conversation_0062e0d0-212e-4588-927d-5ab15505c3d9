{% csrf_token %}

{% if subject %}
<select name="selected-subject" id="selected-subject" class="d-none">
    <option value="{{ subject }}">Matière</option>
</select>
{% endif %}

{% if term %}
<select name="selected-term" id="selected-term" class="d-none">
    <option value="{{ term }}">Période</option>
</select>
{% endif %}

<table class="table table-striped table-bordered table-sm table-hover" id="table">
    <thead class="bg-primary text-white">
    <tr>
        <th><input type="checkbox" name="select-all" id="select-all" onclick="checkAll(this)"></th>
        <th>NOM ET PRENOMS</th>
        {% if lang == 'A' %}
        <th>NOM EN ARABE</th>
        {% endif %}
        <th>MOD</th>
    </tr>
    </thead>
    <tbody>
    {% for enrollment in enrollments %}
    <tr>
        <td>
            <input type="checkbox" name="{{ enrollment.id }}" id="{{ enrollment.id }}">
        </td> 
        <td class="align-middle" style="min-width: 120px;">{{ enrollment }} <br> 
            <span class="text-muted">{{ enrollment.student.student_id|default_if_none:enrollment.student.identifier }}</span></td>
        {% if lang == 'A' %}
        <td class="align-middle text-right" style="min-width: 80px;">{{ enrollment.student.full_name_ar|default_if_none:'' }}</td>
        {% endif %}
        <td class="align-middle {% if not enrollment.grade_value %} text-danger {% endif %}" hx-swap="outerHTML" id="_{{ subject }}_{{ term }}_{{ enrollment.id }}">
            <a href="" hx-get="{% url 'exams:grade_edit' %}?enrollment={{ enrollment.id }}&subject={{ subject }}&term={{ term }}&level={{level}}&education={{lang}}&grade={{enrollment.grade_value}}" 
            hx-target="#dialog" hx-swap="innerHTML">
                {% if enrollment.grade_value %} {{ enrollment.grade_value|stringformat:'.2f' }} {% else %} NC {% endif %}
            </a>
        </td>
    </tr>
    {% endfor %}
    </tbody>
</table>
<hr>
<div class="my-2 row bg-success p-3 rounded text-white">
    <div class="col-md-6">
        <label for="action">ACTIONS</label>
        <select name="action" id="action" class="form-control">
            <option value="export_grade_by_subject">Exporter vers Excel</option>
        </select>
    </div>
    <div class="col-12 mt-2">
        <input type="submit" value="Exporter" class="btn btn-warning">
    </div>
</div>


<script>
function checkAll(checkbox) {
  var checkboxes = document.getElementsByTagName('input');
  for (var i = 0; i < checkboxes.length; i++) {
    if (checkboxes[i].type === 'checkbox') {
      checkboxes[i].checked = checkbox.checked;
    }
  }
}

$.fn.dataTable.ext.errMode = 'none';
		$('#table').DataTable({
		// dom: 'Bfrtip',
		lengthMenu: [
			[ 100, 200, 300],
			[ '100', '200', '300']
        ],
        drawCallback: function() {
            htmx.process(document.body.querySelector('#table'))
            feather.replace();
        }
    })


</script>