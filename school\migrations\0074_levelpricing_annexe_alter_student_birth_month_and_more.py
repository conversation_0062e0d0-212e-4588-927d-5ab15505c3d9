# Generated by Django 5.0.2 on 2024-06-22 13:39

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('school', '0073_alter_student_birth_place_ar'),
    ]

    operations = [
        migrations.AddField(
            model_name='levelpricing',
            name='annexe',
            field=models.PositiveSmallIntegerField(default=0, verbose_name='frais annexes'),
        ),
        migrations.AlterField(
            model_name='student',
            name='birth_month',
            field=models.PositiveSmallIntegerField(blank=True, choices=[(1, '1- Janvier'), (2, '2- Février'), (3, '3- <PERSON>'), (4, '4- Avril'), (5, '5- Mai'), (6, '6- Juin'), (7, '7- <PERSON><PERSON><PERSON>'), (8, '8- A<PERSON><PERSON><PERSON>'), (9, '9- Septembre'), (10, '10- Octobre'), (11, '11- Novembre'), (12, '12- <PERSON><PERSON><PERSON><PERSON>')], null=True, verbose_name='mois'),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name='teacher',
            name='birth_month',
            field=models.PositiveSmallIntegerField(blank=True, choices=[(1, '1- <PERSON><PERSON>'), (2, '2- <PERSON><PERSON>vrier'), (3, '3- <PERSON>'), (4, '4- Avril'), (5, '5- <PERSON>'), (6, '6- Juin'), (7, '7- Juillet'), (8, '8- Août'), (9, '9- Septembre'), (10, '10- Octobre'), (11, '11- Novembre'), (12, '12- Décembre')], null=True, verbose_name='mois'),
        ),
    ]
