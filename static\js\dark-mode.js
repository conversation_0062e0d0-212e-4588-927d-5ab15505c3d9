// Dark Mode Toggle Functionality

document.addEventListener('DOMContentLoaded', function() {
    // Initialize Feather icons
    if (typeof feather !== 'undefined') {
        feather.replace();
    }

    // Get the dark mode toggle button
    const darkModeToggle = document.getElementById('darkModeToggle');

    // Check if dark mode is enabled in localStorage
    const isDarkMode = localStorage.getItem('darkMode') === 'enabled';

    // Apply dark mode if it's enabled
    if (isDarkMode) {
        document.body.classList.add('dark-mode');
        updateDarkModeIcon(true);
    }

    // Add click event listener to the dark mode toggle button
    if (darkModeToggle) {
        darkModeToggle.addEventListener('click', function(e) {
            e.preventDefault();

            // Toggle dark mode class on body
            const isDarkModeEnabled = document.body.classList.toggle('dark-mode');

            // Update localStorage
            if (isDarkModeEnabled) {
                localStorage.setItem('darkMode', 'enabled');
            } else {
                localStorage.setItem('darkMode', 'disabled');
            }

            // Update the icon
            updateDarkModeIcon(isDarkModeEnabled);
        });
    }

    // Function to update the dark mode icon
    function updateDarkModeIcon(isDarkMode) {
        if (!darkModeToggle) return;

        const iconElement = darkModeToggle.querySelector('[data-feather]');
        if (iconElement) {
            if (isDarkMode) {
                iconElement.setAttribute('data-feather', 'sun');
            } else {
                iconElement.setAttribute('data-feather', 'moon');
            }

            // Re-initialize Feather icons
            if (typeof feather !== 'undefined') {
                feather.replace();
            }
        }
    }
});

// Make sure Feather icons are initialized when the script loads
document.addEventListener('htmx:load', function() {
    if (typeof feather !== 'undefined') {
        feather.replace();
    }
});
