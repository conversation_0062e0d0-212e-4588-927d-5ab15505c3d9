
    <!-- User Offcanvas -->
    <div class="offcanvas-overlay" id="offcanvas-overlay">
        <div class="offcanvas" id="user-offcanvas">
            <div class="offcanvas-header">
                <div class="offcanvas-title">Menu Utilisateur</div>
                <button class="offcanvas-close" id="close-user-offcanvas">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="offcanvas-content">
                <!-- User Profile Section -->
                <div class="offcanvas-profile">
                    <div class="profile-avatar">
                        <span class="material-icons">account_circle</span>
                    </div>
                    <div class="profile-info">
                        <div class="profile-name">{{ user.get_full_name|default:'Inconnu' }}</div>
                        <div class="profile-email">{{ user.get_role_display }}</div>
                    </div>
                </div>

                <!-- User Menu -->
                <div class="offcanvas-menu">
                    <div class="offcanvas-menu-item">
                        <span class="material-icons">person</span>
                        <div class="menu-item-content">
                            <div class="menu-item-title">Profile</div>
                            <div class="menu-item-subtitle">Gérer mon compte</div>
                        </div>
                        <span class="material-icons menu-item-arrow">chevron_right</span>
                    </div>
                    <div class="offcanvas-menu-item">
                        <span class="material-icons">settings</span>
                        <div class="menu-item-content">
                            <div class="menu-item-title">Paramètres</div>
                            <div class="menu-item-subtitle">Préférences de l'application</div>
                        </div>
                        <span class="material-icons menu-item-arrow">chevron_right</span>
                    </div>
                    <div class="offcanvas-divider"></div>
                    <div class="offcanvas-menu-item logout">
                        <span class="material-icons">logout</span>
                        <div class="menu-item-content">
                            <div class="menu-item-title">Déconnexion</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>