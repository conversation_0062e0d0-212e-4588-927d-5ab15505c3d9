# Generated by Django 4.2.4 on 2023-11-08 14:31

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('school', '0037_student_nationality'),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name='teacherlevel',
            unique_together={('teacher', 'level')},
        ),
        migrations.AddField(
            model_name='teacherlevel',
            name='is_main_teacher',
            field=models.BooleanField(default=False, verbose_name='Est professeur principal (PP)'),
        ),
        migrations.AlterField(
            model_name='student',
            name='nationality',
            field=models.CharField(choices=[('80', '<PERSON><PERSON><PERSON>ise'), ('75', 'Burkinab<PERSON>'), ('76', '<PERSON><PERSON><PERSON><PERSON><PERSON>'), ('77', '<PERSON><PERSON><PERSON><PERSON>'), ('70', 'Ivoirienne'), ('78', 'Libérienne'), ('79', '<PERSON><PERSON>'), ('84', '<PERSON><PERSON><PERSON><PERSON>'), ('83', '<PERSON><PERSON><PERSON><PERSON>'), ('82', '<PERSON><PERSON><PERSON><PERSON>'), ('85', 'Sénégalaise'), ('86', 'Sierra-Léonaise'), ('94', '<PERSON>ine'), ('94', 'Française'), ('93', 'Asiatique'), ('89', 'Autres pays africains'), ('89', 'Autres pays européen'), ('99', 'Sans Nationalité')], default='70', max_length=3, verbose_name='nationalité'),
        ),
        migrations.AlterUniqueTogether(
            name='teacherlevel',
            unique_together={('teacher', 'level'), ('level', 'is_main_teacher')},
        ),
    ]
