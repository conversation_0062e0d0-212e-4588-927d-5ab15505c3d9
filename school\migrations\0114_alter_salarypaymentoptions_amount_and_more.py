# Generated by Django 5.1.4 on 2024-12-22 09:30

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('school', '0113_alter_salarypaymentoptions_amount_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterField(
            model_name='salarypaymentoptions',
            name='amount',
            field=models.PositiveIntegerField(blank=True, help_text='Montant de la rubrique. Laisser vide si taux défini.', null=True, verbose_name='montant'),
        ),
        migrations.AlterField(
            model_name='salarypaymentoptions',
            name='name',
            field=models.CharField(help_text='Exemple: CNPS, CMU, Primes etc.', max_length=255, verbose_name='rubrique'),
        ),
        migrations.AlterField(
            model_name='salarypaymentoptions',
            name='operation',
            field=models.PositiveSmallIntegerField(choices=[(1, 'Ajout'), (2, 'Retrait')], default=1, help_text="'Ajout' va ajouter le montant sur le salaire, 'Retrait' va le soustraire", verbose_name='opération'),
        ),
        migrations.AlterField(
            model_name='salarypaymentoptions',
            name='rate',
            field=models.PositiveIntegerField(blank=True, help_text='Taux de la rubrique. Laisser vide si montant défini.', null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='taux'),
        ),
        migrations.CreateModel(
            name='SalaryPaymentDecision',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('month', models.PositiveSmallIntegerField(choices=[(1, 'Janvier (1)'), (2, 'Février (2)'), (3, 'Mars (3)'), (4, 'Avril (4)'), (5, 'Mai (5)'), (6, 'Juin (6)'), (7, 'Juillet (7)'), (8, 'Août (8)'), (9, 'Septembre (9)'), (10, 'Octobre (10)'), (11, 'Novembre (11)'), (12, 'Décembre (12)')], verbose_name='mois')),
                ('payment_date', models.DateField(verbose_name='date de paiement')),
                ('status', models.BooleanField(default=True, verbose_name='payé')),
                ('decision_number', models.CharField(blank=True, max_length=20, null=True, verbose_name='N° décision')),
                ('agent', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('school', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='school.school')),
                ('year', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='school.year')),
            ],
            options={
                'verbose_name': 'Décision de paie',
                'verbose_name_plural': 'Décisions de paie',
                'unique_together': {('month', 'year', 'decision_number')},
            },
        ),
    ]
