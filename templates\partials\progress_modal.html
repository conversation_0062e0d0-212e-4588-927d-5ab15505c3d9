{% extends 'partials/modal.html' %}

{% block modal_title %}
<span data-feather="refresh-cw" class="feather-20 align-middle mr-2"></span>
Génération en cours
{% endblock %}

{% block modal_body %}
<style>
    .progress-container .task-header {
        text-align: center;
        margin-bottom: 2rem;
    }

    .progress-container .task-title {
        color: #495057;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .progress-container .task-subtitle {
        color: #6c757d;
        font-size: 0.9rem;
    }

    .progress-container .progress {
        height: 20px;
        border-radius: 10px;
        background: #e9ecef;
        overflow: hidden;
        box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .progress-container .progress-bar {
        background: linear-gradient(90deg, #28a745, #20c997);
        transition: width 0.6s ease;
        border-radius: 10px;
        position: relative;
        overflow: hidden;
    }

    .progress-container .progress-bar::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
            45deg,
            rgba(255, 255, 255, 0.2) 25%,
            transparent 25%,
            transparent 50%,
            rgba(255, 255, 255, 0.2) 50%,
            rgba(255, 255, 255, 0.2) 75%,
            transparent 75%,
            transparent
        );
        background-size: 20px 20px;
        animation: progress-stripes 1s linear infinite;
    }

    @keyframes progress-stripes {
        0% { background-position: 0 0; }
        100% { background-position: 20px 0; }
    }

    .progress-container .status-icon {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background: #28a745;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.75rem;
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }

    .progress-container .task-details {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1rem;
        font-size: 0.85rem;
        color: #6c757d;
    }
</style>

<div class="progress-container">
    <div class="task-header text-center mb-4">
        <h5 class="task-title text-primary">Génération des bulletins</h5>
        <p class="task-subtitle text-muted">Veuillez patienter pendant que nous générons vos bulletins</p>
    </div>

    <div class="progress-wrapper mb-4">
        <div class="progress" style="height: 20px; border-radius: 10px; background: #e9ecef; overflow: hidden; box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);">
            <div id="progress-bar" class="progress-bar progress-bar-striped progress-bar-animated"
                 style="background: linear-gradient(90deg, #28a745, #20c997); width: 0%; border-radius: 10px; transition: width 0.6s ease;"
                 role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
            </div>
        </div>

        <div class="progress-info d-flex justify-content-between align-items-center mt-3">
            <div class="status-indicator d-flex align-items-center">
                <div class="status-icon mr-2" style="width: 24px; height: 24px; border-radius: 50%; background: #28a745; color: white; display: flex; align-items: center; justify-content: center; font-size: 0.75rem;">
                    <span data-feather="refresh-cw" class="feather-12"></span>
                </div>
                <span id="status-text" class="text-muted">Initialisation...</span>
            </div>
            <div class="progress-percentage font-weight-bold text-primary" id="progress-percentage">0%</div>
        </div>
    </div>

    <div class="eta-info text-center mb-3">
        <small class="text-muted" id="eta-info">Temps estimé: --</small>
    </div>

    <div class="task-details bg-light rounded p-3 mb-3">
        <div class="row">
            <div class="col-md-6">
                <div><strong>Tâche:</strong> <span id="task-name">Génération des bulletins</span></div>
                <div><strong>Démarré:</strong> <span id="start-time">--</span></div>
            </div>
            <div class="col-md-6">
                <div><strong>Éléments traités:</strong> <span id="items-processed">0</span> / <span id="total-items">--</span></div>
                <div><strong>Statut:</strong> <span id="task-status" class="badge badge-primary">En cours</span></div>
            </div>
        </div>
    </div>

    <!-- Success section (hidden initially) -->
    <div id="success-section" class="text-center" style="display: none;">
        <div class="mb-3">
            <span data-feather="check-circle" class="feather-48 text-success"></span>
        </div>
        <h4 class="text-success mb-3">Génération terminée avec succès!</h4>
        <p class="text-muted mb-4">Vos bulletins ont été générés et sont prêts à être téléchargés.</p>

        <div id="download-links" class="btn-group-vertical">
            <!-- Download links will be populated here -->
        </div>
    </div>

    <!-- Error section (hidden initially) -->
    <div id="error-section" class="text-center" style="display: none;">
        <div class="mb-3">
            <span data-feather="alert-circle" class="feather-48 text-danger"></span>
        </div>
        <h4 class="text-danger mb-3">Erreur lors de la génération</h4>
        <p class="text-muted mb-4">Une erreur s'est produite pendant la génération des bulletins.</p>
    </div>
</div>

<script>
$(document).ready(function() {
    // Initialize feather icons
    if (typeof feather !== 'undefined') {
        feather.replace();
    }

    var progressUrl = "{% url 'celery_progress:task_status' task_id %}";
    var startTime = Date.now();
    var isCompleted = false;

    // Update start time display
    document.getElementById('start-time').textContent = new Date().toLocaleTimeString();

    // Custom progress handler
    function updateProgress(progressBarElement, progressBarMessageElement, progress) {
        var percentage = Math.round(progress.percent || 0);
        var progressBar = document.getElementById('progress-bar');
        var progressPercentage = document.getElementById('progress-percentage');
        var statusText = document.getElementById('status-text');
        var itemsProcessed = document.getElementById('items-processed');
        var totalItems = document.getElementById('total-items');
        var taskStatus = document.getElementById('task-status');
        var etaInfo = document.getElementById('eta-info');

        // Update progress bar
        progressBar.style.width = percentage + '%';
        progressBar.setAttribute('aria-valuenow', percentage);
        progressPercentage.textContent = percentage + '%';

        // Update status text
        if (progress.description) {
            statusText.textContent = progress.description;
        } else if (progress.current && progress.total) {
            statusText.textContent = `Traitement en cours: ${progress.current} sur ${progress.total}`;
        }

        // Update items processed
        if (progress.current !== undefined) {
            itemsProcessed.textContent = progress.current;
        }
        if (progress.total !== undefined) {
            totalItems.textContent = progress.total;
        }

        // Update ETA
        if (progress.current > 0 && progress.total > 0) {
            var elapsed = (Date.now() - startTime) / 1000;
            var rate = progress.current / elapsed;
            var remaining = (progress.total - progress.current) / rate;

            if (remaining > 60) {
                var minutes = Math.ceil(remaining / 60);
                etaInfo.textContent = `Temps estimé: ${minutes} minute${minutes > 1 ? 's' : ''}`;
            } else {
                etaInfo.textContent = `Temps estimé: ${Math.ceil(remaining)} secondes`;
            }
        }

        // Update task status
        if (percentage >= 100) {
            taskStatus.textContent = 'Terminé';
            taskStatus.className = 'badge badge-success';
        } else if (percentage > 0) {
            taskStatus.textContent = 'En cours';
            taskStatus.className = 'badge badge-primary';
        }
    }

    // Success handler
    function onSuccess(progressBarElement, progressBarMessageElement, result) {
        isCompleted = true;

        // Hide progress section
        document.querySelector('.progress-wrapper').style.display = 'none';
        document.querySelector('.eta-info').style.display = 'none';
        document.querySelector('.task-details').style.display = 'none';

        // Show success section
        document.getElementById('success-section').style.display = 'block';

        // Check for generated file and show download link
        {% if level_id and term and report_type %}
        setTimeout(function() {
            checkForGeneratedFile();
        }, 2000); // Wait a bit for file to be fully written
        {% endif %}

        // Trigger a refresh of the table to get the updated file links
        setTimeout(function() {
            // Find the term select and trigger a change to refresh the table
            var termSelect = document.querySelector('select[name="term"]');
            if (termSelect && termSelect.value) {
                // Trigger HTMX request to refresh the table
                htmx.trigger(termSelect, 'change');
            }
        }, 3000);

        // Add initial download links
        var downloadLinks = document.getElementById('download-links');
        downloadLinks.innerHTML = `
            <div id="file-check-status" class="mb-3">
                <div class="spinner-border spinner-border-sm mr-2" role="status"></div>
                Vérification du fichier généré...
            </div>
            <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                <span data-feather="x" class="feather-16 align-middle mr-1"></span>
                Fermer
            </button>
        `;

        // Re-initialize feather icons
        if (typeof feather !== 'undefined') {
            feather.replace();
        }
    }

    // Function to check for generated file
    function checkForGeneratedFile() {
        {% if level_id and term and report_type %}
        fetch('{% url "exams:get_generated_file_link" %}?level_id={{ level_id }}&term_id={{ term }}&report_type={{ report_type }}')
            .then(response => response.json())
            .then(data => {
                var downloadLinks = document.getElementById('download-links');
                var fileCheckStatus = document.getElementById('file-check-status');

                if (data.success && data.download_url) {
                    // File is ready, show download button
                    downloadLinks.innerHTML = `
                        <a href="${data.download_url}" class="btn btn-success btn-lg mb-2">
                            <span data-feather="download" class="feather-16 align-middle mr-1"></span>
                            Télécharger ${data.filename || 'le fichier'}
                        </a>
                        <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                            <span data-feather="x" class="feather-16 align-middle mr-1"></span>
                            Fermer
                        </button>
                    `;
                } else {
                    // File not ready yet, show refresh option
                    downloadLinks.innerHTML = `
                        <button type="button" class="btn btn-warning btn-lg mb-2" onclick="location.reload()">
                            <span data-feather="refresh-cw" class="feather-16 align-middle mr-1"></span>
                            Actualiser la page
                        </button>
                        <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                            <span data-feather="x" class="feather-16 align-middle mr-1"></span>
                            Fermer
                        </button>
                    `;
                }

                // Re-initialize feather icons
                if (typeof feather !== 'undefined') {
                    feather.replace();
                }
            })
            .catch(error => {
                console.error('Error checking file:', error);
                var downloadLinks = document.getElementById('download-links');
                downloadLinks.innerHTML = `
                    <button type="button" class="btn btn-warning btn-lg mb-2" onclick="location.reload()">
                        <span data-feather="refresh-cw" class="feather-16 align-middle mr-1"></span>
                        Actualiser la page
                    </button>
                    <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                        <span data-feather="x" class="feather-16 align-middle mr-1"></span>
                        Fermer
                    </button>
                `;

                if (typeof feather !== 'undefined') {
                    feather.replace();
                }
            });
        {% endif %}
    }

    // Error handler
    function onError(progressBarElement, progressBarMessageElement, excMessage) {
        isCompleted = true;

        // Hide progress section
        document.querySelector('.progress-wrapper').style.display = 'none';
        document.querySelector('.eta-info').style.display = 'none';
        document.querySelector('.task-details').style.display = 'none';

        // Show error section
        document.getElementById('error-section').style.display = 'block';
    }

    // Initialize progress bar
    CeleryProgressBar.initProgressBar(progressUrl, {
        onProgress: updateProgress,
        onSuccess: onSuccess,
        onError: onError,
        progressBarElement: document.getElementById('progress-bar'),
        progressBarMessageElement: document.getElementById('status-text')
    });
});
</script>
{% endblock %}
