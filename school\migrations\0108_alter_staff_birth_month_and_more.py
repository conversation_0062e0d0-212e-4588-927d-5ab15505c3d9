# Generated by Django 4.2.1 on 2024-12-11 15:18

import cloudinary.models
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('school', '0107_alter_payment_created_at_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='staff',
            name='birth_month',
            field=models.PositiveSmallIntegerField(blank=True, choices=[(1, '<PERSON><PERSON> (1)'), (2, '<PERSON><PERSON><PERSON><PERSON> (2)'), (3, '<PERSON> (3)'), (4, 'A<PERSON>ril (4)'), (5, '<PERSON> (5)'), (6, '<PERSON><PERSON> (6)'), (7, '<PERSON><PERSON><PERSON> (7)'), (8, '<PERSON><PERSON><PERSON><PERSON> (8)'), (9, 'Septembre (9)'), (10, 'Octobre (10)'), (11, 'Novembre (11)'), (12, '<PERSON><PERSON><PERSON><PERSON> (12)')], null=True, verbose_name='mois'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='staffsalaryformonth',
            name='month',
            field=models.PositiveSmallIntegerField(choices=[(1, '<PERSON><PERSON> (1)'), (2, '<PERSON><PERSON><PERSON><PERSON> (2)'), (3, '<PERSON> (3)'), (4, 'Avril (4)'), (5, '<PERSON> (5)'), (6, 'Juin (6)'), (7, 'Juillet (7)'), (8, 'Août (8)'), (9, 'Septembre (9)'), (10, 'Octobre (10)'), (11, 'Novembre (11)'), (12, 'Décembre (12)')], verbose_name='mois'),
        ),
        migrations.AlterField(
            model_name='student',
            name='birth_month',
            field=models.PositiveSmallIntegerField(blank=True, choices=[(1, 'Janvier (1)'), (2, 'Février (2)'), (3, 'Mars (3)'), (4, 'Avril (4)'), (5, 'Mai (5)'), (6, 'Juin (6)'), (7, 'Juillet (7)'), (8, 'Août (8)'), (9, 'Septembre (9)'), (10, 'Octobre (10)'), (11, 'Novembre (11)'), (12, 'Décembre (12)')], null=True, verbose_name='mois'),
        ),
        migrations.AlterField(
            model_name='teacher',
            name='birth_month',
            field=models.PositiveSmallIntegerField(blank=True, choices=[(1, 'Janvier (1)'), (2, 'Février (2)'), (3, 'Mars (3)'), (4, 'Avril (4)'), (5, 'Mai (5)'), (6, 'Juin (6)'), (7, 'Juillet (7)'), (8, 'Août (8)'), (9, 'Septembre (9)'), (10, 'Octobre (10)'), (11, 'Novembre (11)'), (12, 'Décembre (12)')], null=True, verbose_name='mois'),
        ),
        migrations.CreateModel(
            name='StaffCard',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('matricule', models.CharField(max_length=20, unique=True, verbose_name='matricule')),
                ('last_name', models.CharField(max_length=255, verbose_name='nom')),
                ('first_name', models.CharField(max_length=255, verbose_name='prénoms')),
                ('gender', models.CharField(choices=[('M', 'Masculin'), ('F', 'Féminin')], max_length=255, verbose_name='sexe')),
                ('birth_date', models.DateField(verbose_name='date de naissance')),
                ('birth_place', models.CharField(max_length=255, verbose_name='lieu de naissance')),
                ('phone', models.CharField(max_length=20, verbose_name='contact')),
                ('status', models.CharField(choices=[('P', 'EN COURS'), ('M', 'IMPRIMEE'), ('S', 'LIVREE')], default='P', max_length=1, verbose_name='statut')),
                ('authorization_number', models.CharField(blank=True, max_length=20, null=True, verbose_name='N° autorisation')),
                ('photo', cloudinary.models.CloudinaryField(max_length=255, null=True, verbose_name='image')),
                ('school', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='school.school')),
                ('year', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='school.year')),
            ],
            options={
                'verbose_name': 'carte professionnel',
                'verbose_name_plural': 'cartes professionnels',
            },
        ),
    ]
