{% extends 'partials/modal.html' %}
{% load widget_tweaks %}
{% load humanize %}

{% block modal_title %} Paiements des frais d'écolage {% endblock %}

{% block modal_body %}
<div class="d-flex justify-content-between">
    <div class="w-75">
        <div class="form-row">
            <div class="form-group col-12">
                <label for="{{ form.student_id.id_for_label }}">{{ form.student_id.label }}</label>
                <div class="d-flex">
                    {% render_field form.student_id class='form-control' %}
                    <span class="invalid-feedback">{{ form.student_id.errors|first }}</span>
                    <a class="btn btn-warning mx-1" id="search" 
                    style="height: 35px; width: 50px;"
                    hx-get="{% url 'school:payment_add' %}" hx-include="[name=student_id]">
                    <span data-feather="search"></span></a>
                </div>
            </div>
        </div>
        
        <div class="form-row">
            <div class="form-group col-6 col-lg-3">
                <label for="{{ form.last_name.id_for_label }}">{{ form.last_name.label }}</label>
                {% render_field form.last_name class='form-control' %}
                <span class="invalid-feedback">{{ form.last_name.errors|first }}</span>
            </div>
            <div class="form-group col-6 col-lg-3">
                <label for="{{ form.first_name.id_for_label }}">{{ form.first_name.label }}</label>
                {% render_field form.first_name class='form-control' %}
                <span class="invalid-feedback">{{ form.first_name.errors|first }}</span>
            </div>
            {% if user.school.education == EDUCATION_ARABIC %}
            <div class="form-group col-6 col-lg-3">
                <label for="{{ form.level_fr.id_for_label }}">{{ form.level_fr.label }}</label>
                {% render_field form.level_fr class='form-control' %}
                <span class="invalid-feedback">{{ form.level_fr.errors|first }}</span>
            </div>
            <div class="form-group col-6 col-lg-3">
                <label for="{{ form.level_ar.id_for_label }}">{{ form.level_ar.label }}</label>
                {% render_field form.level_ar class='form-control' %}
                <span class="invalid-feedback">{{ form.level_ar.errors|first }}</span>
            </div>
            {% else %}
            <div class="form-group col-md-6 col-lg-3">
                <label for="{{ form.level_fr.id_for_label }}">{{ form.level_fr.label }}</label>
                {% render_field form.level_fr class='form-control' %}
                <span class="invalid-feedback">{{ form.level_fr.errors|first }}</span>
            </div>
            {% endif %}
        </div>
    </div>
    <div>
        <img src="{% if enrollment.photo_url %}{{ enrollment.photo_url }}{% else %}{{ enrollment.student.blank_photo }}{% endif %}" 
        height="100px" alt="Photo de l'élève" style="border-radius: 50%;" loading="lazy">
    </div>
</div>
<span class="invalid-feedback">{{ form.errors.0 }}</span>
<h5 class="text-success"><span data-feather="square" class="feather-16"></span> Montant à payer: {{ enrollment.get_fees_total|intcomma }} F</h5>
{% include 'partials/payment/payments_component.html' with width='col-md-9 px-0' form=form with_agent=True %}
{% if form.errors %}
<div class="alert alert-danger">Veuillez entrer le montant du paiement svp.</div>
{% endif %}
{% endblock %}

{% block modal_footer %}
    <span style="position: absolute; left: 5;"><span style="background-color: rgb(181, 240, 181);">Nouveau</span> = Ajouter nouveau paiement</span>
    <button type="submit" href="" class="btn btn-success">Enregistrer</button>
{% endblock %}