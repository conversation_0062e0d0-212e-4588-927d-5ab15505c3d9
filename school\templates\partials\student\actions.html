{% csrf_token %}
<div class="bulk-actions d-flex justify-content-center align-items-center">
    <div class="container-fluid d-flex align-items-center justify-content-center">
      <div class="d-flex" style="min-width: fit-content;">
        <select name="action" id="action" class="form-control mr-2" style="height: 32px;">
            <option value="excel_export">Export infos élèves vers Excel</option>
            {% if perms.exams.view_grade %}
                {% if user.school.education == 'A' %}
                <option value="excel_export_for_certificate">Export infos élèves pour diplôme</option>
                {% endif %}
                <option value="excel_export_for_dfa">Exporter DFA</option>
            {% endif %}
            <!-- <option value="excel_export">Export Moyennes Trim 1</option>
            <option value="excel_export">Export Moyennes Trim 2</option>
            <option value="excel_export">Export Moyennes Trim 3</option>
            <option value="excel_export">Export Moyennes Compo 4</option>
            <option value="excel_export">Export Photos ZIP</option>
            <option value="excel_export">Export Moyennes Generales</option>
            <option value="excel_export">Export Moy. Croisées Fran/Arabe</option>
            <option value="excel_export">Export Rapport de scolarité Excel</option>
            <option value="pdf_export">Générer rapport de scolarité PDF</option> -->
        </select>
        <input type="submit"  formmethod="post" formaction="{% url 'school:student_page_action' %}" value="Valider" class="submit" class="btn btn-sm btn-outline-secondary pr-3 ml-1">
        <span class="selected-count bg-light text-info ml-3 align-middle" style="min-width: fit-content;">
          <span id="selected-count" class="align-middle">0</span> sélectionnés
        </span>
      </div>
    </div>
</div>