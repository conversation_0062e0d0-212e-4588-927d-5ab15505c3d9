{% load widget_tweaks %}

<div class="import-container tile">
  <div class="row">
    <!-- Left Column - Main Content -->
    <div class="col-md-7 col-lg-8 order-2 order-md-1">
      <!-- Header Section -->
      <div class="import-header mb-2 show-on-pc">
        <div class="d-flex align-items-center mb-2">
          <div class="import-icon-wrapper mr-3">
            <i data-feather="upload-cloud" class="text-primary"></i>
          </div>
          <h4 class="mb-0">Importer des élèves</h4>
        </div>
        <p class="text-muted">Suivez ces étapes pour importer facilement vos données élèves</p>
      </div>
      
      <!-- Steps Section -->
      <div class="card shadow-sm mb-4 show-on-pc">
        <div class="card-body p-0">
          <div class="steps-container">
            <div class="step active" id="step1">
              <div class="step-number">1</div>
              <div class="step-content">
                <h6 class="step-title">Préparer votre fichier</h6>
                <p class="step-description">Assurez-vous que votre fichier est au bon format</p>
              </div>
            </div>
            <div class="step" id="step2">
              <div class="step-number">2</div>
              <div class="step-content">
                <h6 class="step-title">Téléverser le fichier</h6>
                <p class="step-description">Sélectionnez et importez votre fichier</p>
              </div>
            </div>
            <div class="step" id="step3">
              <div class="step-number">3</div>
              <div class="step-content">
                <h6 class="step-title">Vérification</h6>
                <p class="step-description">Vos données seront vérifiées et importées</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Upload Form -->
      <form method="post" action="{{ request.path }}" enctype="multipart/form-data" id="importForm">
        {% csrf_token %}
        
        <div class="card shadow-sm mb-4">
          <div class="card-header d-flex align-items-center">
            <i data-feather="upload" class="text-primary mr-2 align-middle"></i>
            <h5 class="mb-0">Chargez le fichier à importer</h5>
          </div>
          <div class="card-body">
            <div class="file-upload-container" id="fileUploadContainer">
              <div class="file-upload-area">
                <input type="file" name="{{ form.file.html_name }}" id="{{ form.file.id_for_label }}" class="file-input" accept=".xlsx,.xls,.csv">
                <div class="file-upload-placeholder">
                  <div class="file-upload-icon">
                    <i data-feather="file-plus" class="file-icon-placeholder"></i>
                  </div>
                  <div class="file-upload-text">
                    <h6>Glissez votre fichier ici</h6>
                    <p>ou <span class="text-primary">cliquez pour parcourir</span></p>
                  </div>
                </div>
                <div class="file-upload-success d-none">
                  <div class="file-upload-icon">
                    <i data-feather="check-circle" class="text-success"></i>
                  </div>
                  <div class="file-upload-text">
                    <h6 id="selectedFileName">Fichier sélectionné</h6>
                    <p class="text-muted" id="selectedFileSize">Taille: 0 KB</p>
                  </div>
                  <button type="button" class="btn btn-link text-danger p-0 ml-3" id="removeFile">
                    <i data-feather="x-circle"></i>
                  </button>
                </div>
              </div>
              
              <div class="upload-arrow-animation" id="uploadArrow">
                <div class="arrow">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
              </div>
            </div>

            {% if user.role == 'AC' %}
            <div class="custom-control custom-switch mt-4">
              {% render_field form.active class='custom-control-input' %}
              <label for="{{ form.active.id_for_label }}" class="custom-control-label">
                <strong>Marquer les nouveaux élèves comme inscrits</strong>
              </label>
            </div>
            <small class="form-text text-muted ml-4">Cela n'affectera pas les élèves déjà dans la base de données</small>
            {% endif %}
          </div>
          <div class="card-footer bg-light">
            <button type="submit" class="btn btn-primary px-4" id="importButton" disabled>
              <span class="spinner-border spinner-border-sm d-none mr-2" id="importSpinner" role="status" aria-hidden="true"></span>
              <i data-feather="upload-cloud" class="mr-2 import-icon align-middle"></i>
              Importer les données
            </button>
          </div>
        </div>
      </form>
    </div>
    
    <!-- Right Column - Instructions -->
    <div class="col-md-5 col-lg-4 order-1 order-md-2 mb-4 mb-md-0">
      <!-- Instructions Section -->
      <div class="template-download mt-4">
        <div class="d-flex align-items-center justify-content-between">
          <div>
            <h6 class="mb-1">Télécharger le modèle</h6>
            <p class="text-muted mb-0">Utilisez notre modèle prêt à l'emploi</p>
          </div>
          <a href="{% url 'school:student_import_model' %}" class="btn btn-outline-primary">
            <i data-feather="download" class="mr-1 align-middle"></i> Télécharger
          </a>
        </div>
      </div>
      <div class="card shadow-sm sticky-top sticky-offset mt-3 show-on-pc" id="instructionsCard">
        <div class="card-header d-flex align-items-center">
          <i data-feather="info" class="text-primary mr-2"></i>
          <h5 class="mb-0">Instructions importantes</h5>
        </div>
        <div class="card-body">
          <div class="alert alert-light p-0">
            <p>Avant d'importer un fichier, veuillez vous assurer qu'il respecte les critères suivants:</p>
          </div>
          
          <div class="import-requirements">
            <div class="requirement">
              <div class="requirement-icon">
                <i data-feather="file" class="text-primary"></i>
              </div>
              <div class="requirement-content">
                <h6>Format du fichier</h6>
                <p><span class="badge badge-danger">Excel (.xls, .xlsx)</span> ou <span class="badge badge-danger">CSV</span></p>
              </div>
            </div>
            
            <div class="requirement">
              <div class="requirement-icon">
                <i data-feather="star" class="text-warning"></i>
              </div>
              <div class="requirement-content">
                <h6>Champs obligatoires</h6>
                <div class="tags-container">
                  {% for col in required_cols %} 
                  <span class="badge badge-danger">{{ col }}</span>
                  {% endfor %}
                </div>
              </div>
            </div>
            
            <div class="requirement">
              <div class="requirement-icon">
                <i data-feather="plus-circle" class="text-success"></i>
              </div>
              <div class="requirement-content">
                <h6>Champs optionnels</h6>
                <div class="tags-container">
                  {% for col in all_cols %} 
                  <span class="badge badge-secondary">{{ col }}</span>
                  {% endfor %}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  .import-container {
    margin: 0 auto;
  }

  /* Make the instruction card sticky on scroll */
  .sticky-offset {
    top: 20px;
  }

  .import-icon-wrapper {
    background-color: rgba(0, 123, 255, 0.1);
    border-radius: 50%;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .import-icon-wrapper svg {
    width: 24px;
    height: 24px;
  }

  /* Steps styling */
  .steps-container {
    display: flex;
    padding: 1.5rem;
  }

  .step {
    display: flex;
    align-items: flex-start;
    position: relative;
    flex: 1;
  }

  .step:not(:last-child):after {
    content: '';
    position: absolute;
    top: 16px;
    left: 32px;
    width: calc(100% - 40px);
    height: 2px;
    background-color: #e9ecef;
    z-index: 1;
  }

  .step.active:not(:last-child):after {
    background-color: #007bff;
    animation: progressLine 1s ease-out forwards;
    transform-origin: left;
  }

  .step-number {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: #e9ecef;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 12px;
    z-index: 2;
    transition: all 0.3s ease;
  }

  .step.active .step-number {
    background-color: #007bff;
    color: white;
    box-shadow: 0 0 0 4px rgba(0, 123, 255, 0.2);
  }

  .step-content {
    padding-right: 10px;
  }

  .step-title {
    margin-bottom: 0.25rem;
    font-weight: 600;
  }

  .step-description {
    color: #6c757d;
    font-size: 0.875rem;
    margin-bottom: 0;
  }

  /* Requirements styling */
  .import-requirements {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .requirement {
    display: flex;
    align-items: flex-start;
  }

  .requirement-icon {
    margin-right: 1rem;
    width: 40px;
    height: 40px;
    background-color: #f8f9fa;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }

  .requirement-content {
    flex: 1;
  }

  .requirement-content h6 {
    margin-bottom: 0.5rem;
    font-weight: 600;
  }

  .tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .template-download {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 0.5rem;
    border-left: 4px solid #007bff;
  }

  /* File upload styling */
  .file-upload-container {
    position: relative;
  }

  .file-upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 0.5rem;
    background-color: #f8f9fa;
    padding: 2.5rem 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
  }

  .file-upload-area:hover {
    border-color: #007bff;
    background-color: rgba(0, 123, 255, 0.05);
  }

  .file-upload-area.drag-over {
    border-color: #007bff;
    background-color: rgba(0, 123, 255, 0.1);
    transform: scale(1.01);
  }

  .file-input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
    z-index: 3;
  }

  .file-upload-icon {
    margin-bottom: 1rem;
  }

  .file-icon-placeholder {
    width: 48px;
    height: 48px;
    color: #6c757d;
  }

  .file-upload-success {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .file-upload-success .file-upload-icon {
    margin-bottom: 0;
    margin-right: 1rem;
  }

  .file-upload-success .file-upload-icon svg {
    width: 36px;
    height: 36px;
  }

  .file-upload-success .file-upload-text {
    text-align: left;
  }

  /* Upload arrow animation */
  .upload-arrow-animation {
    position: absolute;
    top: 10px;
    right: 10px;
    opacity: 0;
    transition: opacity 0.5s ease;
  }

  .upload-arrow-animation.visible {
    opacity: 1;
    animation: bounce 2s infinite;
  }

  .arrow {
    position: relative;
    width: 40px;
    height: 40px;
  }

  .arrow span {
    display: block;
    width: 20px;
    height: 20px;
    border-bottom: 5px solid #007bff;
    border-right: 5px solid #007bff;
    transform: rotate(45deg);
    margin: -10px;
    animation: animate 2s infinite;
  }

  .arrow span:nth-child(2) {
    animation-delay: -0.2s;
  }

  .arrow span:nth-child(3) {
    animation-delay: -0.4s;
  }

  @keyframes animate {
    0% {
      opacity: 0;
      transform: rotate(45deg) translate(-20px, -20px);
    }
    50% {
      opacity: 1;
    }
    100% {
      opacity: 0;
      transform: rotate(45deg) translate(20px, 20px);
    }
  }

  @keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
      transform: translateY(0);
    }
    40% {
      transform: translateY(-10px);
    }
    60% {
      transform: translateY(-5px);
    }
  }

  @keyframes progressLine {
    0% {
      transform: scaleX(0);
    }
    100% {
      transform: scaleX(1);
    }
  }
</style>

<script>
    var fileInput = document.getElementById('{{ form.file.id_for_label }}');
    var fileUploadArea = document.querySelector('.file-upload-area');
    var fileUploadPlaceholder = document.querySelector('.file-upload-placeholder');
    var fileUploadSuccess = document.querySelector('.file-upload-success');
    var selectedFileName = document.getElementById('selectedFileName');
    var selectedFileSize = document.getElementById('selectedFileSize');
    var removeFileButton = document.getElementById('removeFile');
    var importButton = document.getElementById('importButton');
    var uploadArrow = document.getElementById('uploadArrow');
    var importForm = document.getElementById('importForm');
    var importSpinner = document.getElementById('importSpinner');
    var importIcon = document.querySelector('.import-icon');
    
    // Show upload arrow animation after a delay
    setTimeout(() => {
      uploadArrow.classList.add('visible');
    }, 1500);
    
    // Handle file selection
    fileInput.addEventListener('change', (e) => {
      if (fileInput.files.length > 0) {
        const file = fileInput.files[0];
        selectedFileName.textContent = file.name;
        
        // Format file size
        let size = file.size;
        let sizeStr = '';
        if (size < 1024) {
          sizeStr = size + ' bytes';
        } else if (size < 1024 * 1024) {
          sizeStr = (size / 1024).toFixed(2) + ' KB';
        } else {
          sizeStr = (size / (1024 * 1024)).toFixed(2) + ' MB';
        }
        
        selectedFileSize.textContent = 'Taille: ' + sizeStr;
        
        // Show success state
        fileUploadPlaceholder.classList.add('d-none');
        fileUploadSuccess.classList.remove('d-none');
        importButton.disabled = false;
        uploadArrow.classList.remove('visible');
        
        // Activate step 2
        document.getElementById('step2').classList.add('active');
        
        // Scroll to submit button
        setTimeout(() => {
          importButton.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }, 300);
      }
    });
    
    // Handle drag events
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
      fileUploadArea.addEventListener(eventName, preventDefaults, false);
    });
    
    function preventDefaults(e) {
      e.preventDefault();
      e.stopPropagation();
    }
    
    ['dragenter', 'dragover'].forEach(eventName => {
      fileUploadArea.addEventListener(eventName, highlight, false);
    });
    
    ['dragleave', 'drop'].forEach(eventName => {
      fileUploadArea.addEventListener(eventName, unhighlight, false);
    });
    
    function highlight() {
      fileUploadArea.classList.add('drag-over');
    }
    
    function unhighlight() {
      fileUploadArea.classList.remove('drag-over');
    }
    
    // Handle file drop
    fileUploadArea.addEventListener('drop', handleDrop, false);
    
    function handleDrop(e) {
      const dt = e.dataTransfer;
      const files = dt.files;
      
      if (files.length > 0) {
        fileInput.files = files;
        const event = new Event('change');
        fileInput.dispatchEvent(event);
      }
    }
    
    // Remove file
    removeFileButton.addEventListener('click', () => {
      fileInput.value = '';
      fileUploadPlaceholder.classList.remove('d-none');
      fileUploadSuccess.classList.add('d-none');
      importButton.disabled = true;
      
      // Show arrow again
      setTimeout(() => {
        uploadArrow.classList.add('visible');
      }, 500);
      
      // Deactivate step 2
      document.getElementById('step2').classList.remove('active');
    });
    
    // Form submit handling
    importForm.addEventListener('submit', (e) => {
      importButton.disabled = true;
      importSpinner.classList.remove('d-none');
      importIcon.classList.add('d-none');
      
      // Activate step 3
      document.getElementById('step3').classList.add('active');
      
      // Pace is already integrated in your code
      if (typeof Pace !== 'undefined') {
        Pace.restart();
      }
    });
    
    // // Initialize feather icons
    // if (typeof feather !== 'undefined') {
    //   feather.replace();
    // }
</script>