# Generated by Django 4.2.3 on 2023-07-20 10:39

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('school', '0010_alter_payment_annexe_category_and_more'),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name='levelpricing',
            unique_together=set(),
        ),
        migrations.AddField(
            model_name='levelpricing',
            name='education',
            field=models.CharField(choices=[('A', 'Franco-Arabe'), ('F', 'Française')], default='F', max_length=1),
        ),
        migrations.AlterUniqueTogether(
            name='levelpricing',
            unique_together={('generic_level', 'school', 'year', 'student_status', 'education')},
        ),
    ]
