{% load humanize %}
<div class="tile" id="tile" hx-get="{% url 'school:staff' %}" hx-trigger="saved from:body" hx-target="#app-content">
    <div class="tile-title-w-btn">
        <div class="btn-group">
            <a class="btn btn-success" href="" 
               hx-get="{% url 'school:staff_add' %}" 
               hx-target="#dialog-xl">+ Ajouter un employé</a>
        </div>
        <div class="btn-group bg-light p-2">
            <h5>Budget Mensuel: {{ budget|intcomma }} F CFA</h5>
        </div>
    </div>
    <div class="table-responsive">
        <table class="table table-striped table-sm" id="datatable">
            <thead class="bg-primary text-white">
            <tr>
                <th>Nom</th>
                <th>Prénoms</th>
                <th>Sexe</th>
                <th>Né(e) le</th>
                <th>Education</th>
                <th>Emploi</th>
                <th>Date d'embauche</th>
                <th>He<PERSON> de travail</th>
                <th>Salaire</th>
                <th>Actions</th>
            </tr>
            </thead>
            <tbody>
                {% for staff in staff_list %}
                <tr>
                    <td class="align-middle">{{ staff.last_name }}</td>
                    <td class="align-middle">{{ staff.first_name }}</td>
                    <td class="align-middle">{{ staff.gender }}</td>
                    <td class="align-middle">{{ staff.birth_date_str }}</td>
                    <td class="align-middle">{{ staff.education }}</td>
                    <td class="align-middle">{{ staff.role }}</td>
                    <td class="align-middle">{{ staff.date_enlisted|default:'' }}</td>
                    <td class="align-middle">{{ staff.work_hours }}</td>
                    <td class="align-middle">{{ staff.salary }}</td>
                    <td class="align-middle">
                        <a href="" hx-get="{% url 'school:staff_edit' staff.pk %}" 
                            hx-target="#dialog-xl"
                            class="btn btn-sm btn-primary">
                            <span data-feather="edit" class="feather-16"></span>
                        </a>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>