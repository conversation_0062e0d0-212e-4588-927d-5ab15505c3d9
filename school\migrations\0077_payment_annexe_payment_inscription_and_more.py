# Generated by Django 5.0.2 on 2024-06-25 10:15

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('school', '0076_enrollment_pcs'),
    ]

    operations = [
        migrations.AddField(
            model_name='payment',
            name='annexe',
            field=models.PositiveIntegerField(default=0, verbose_name='annexe'),
        ),
        migrations.AddField(
            model_name='payment',
            name='inscription',
            field=models.PositiveIntegerField(default=0, verbose_name='inscription'),
        ),
        migrations.AlterField(
            model_name='payment',
            name='amount',
            field=models.IntegerField(validators=[django.core.validators.MinValueValidator(100)], verbose_name='scolarité'),
        ),
    ]
