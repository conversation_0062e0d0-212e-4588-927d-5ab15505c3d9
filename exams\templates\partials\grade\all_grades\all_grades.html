{% load widget_tweaks %}
<div class="row" hx-get="{% url 'exams:all_grades' %}?type=tout&lang={{lang}}" 
     hx-trigger="saved from:body" 
     hx-target="#table_container"
     hx-include="[name=short_name], [name=level], [name=term], [name=ordering]"
     id="grade_list">
    <div class="col">
        <div class="tile">
            <form action="{{ request.path }}" method="post" class="mb-3">
                {% csrf_token %}
                <div class="form-row">
                    <div class="col-3 col-md-3 form-group">
                        <label for="generic_level">Niveau</label>
                        <select name="short_name" id="generic_level" class="form-control" 
                                hx-get="{% url 'exams:sublevels' %}?lang={{lang}}"
                                hx-target="#level_container">
                            <option value="">--------</option>
                            {% for level in generic_levels %}
                            <option value="{{ level.id }}">{{ level }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-4 col-md-3 form-group" id="level_container">
                        <label for="level">Classe</label>
                        <select name="level" id="level" class="form-control"></select>
                    </div>
                    <div class="col-5 col-md-3 form-group" id="term_container">
                        <label for="id_term">Période</label>
                        <select name="term" id="id_term" class="form-control" 
                                required="required" hx-swap="outerHTML">
                        </select>
                    </div>

                </div>
                <div class="d-flex justify-content-between">
                    <div class="dropdown show-on-phone">
                        <button class="btn btn-success dropdown-toggle" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <span data-feather="chevrons-down" class="feather-16 align-middle"></span> ACTIONS
                        </button>
                        <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                            <a class="dropdown-item" href="#" 
                                hx-get="{% url 'exams:all_grades' %}?type={{type}}&lang={{ lang }}" 
                                hx-target="#table_container">Voir les notes</a>
                            <div class="dropdown-divider"></div>
                            <input type="submit" formaction="{% url 'exams:grade_action' %}?action=export_grade&for_all_level_students" class="dropdown-item" value="Exportation Excel - Classe">
                            <input type="submit" formaction="{% url 'exams:grade_action' %}?action=export_grade_for_all_levels" class="dropdown-item" value="Exportation Excel - Niveau">
                            <div class="dropdown-divider"></div>
                            <button hx-post="{% url 'exams:compute_term_results' %}" class="dropdown-item">Actualiser Moyennes</button>
                        </div>
                    </div>
                    <a href="" class="btn btn-success show-on-pc"
                        hx-get="{% url 'exams:all_grades' %}?type={{type}}&lang={{ lang }}" 
                        hx-target="#table_container">
                        <span data-feather="list" class="feather-16 align-middle"></span>Afficher les notes
                    </a>
                    <div class="dropdown show-on-pc">
                        <button class="btn btn-primary dropdown-toggle" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <span data-feather="chevrons-down" class="feather-16 align-middle"></span> Exporter vers Excel
                        </button>
                        <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                            <input type="submit" formaction="{% url 'exams:grade_action' %}?action=export_grade&for_all_level_students" class="dropdown-item" value="Pour la Classe">
                            <div class="dropdown-divider"></div>
                            <input type="submit" formaction="{% url 'exams:grade_action' %}?action=export_grade_for_all_levels" class="dropdown-item" value="Pour tout le niveau">
                        </div>
                    </div>
                    <button type="submit" class="btn btn-info show-on-pc" hx-post="{% url 'exams:compute_term_results' %}">
                        <span data-feather="refresh-cw" class="feather-16 align-middle"></span> Actualiser les Moyennes
                    </button>
                    <div class="dropdown">
                        <button class="btn btn-warning dropdown-toggle" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <span data-feather="chevrons-down" class="feather-16 align-middle"></span> Noter les élèves
                        </button>
                        <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                            <a type="submit" class="dropdown-item" 
                                hx-get="{% url 'exams:grade_batch_edit' %}?level={{ level.id }}" 
                                hx-target="#app-content" hx-include="[name=term], [name=level]">
                                <span data-feather="refresh-cw" class="feather-16 align-middle"></span>
                                Commencer
                            </a>
                            <a type="submit" class="dropdown-item" 
                                hx-get="{% url 'exams:grade_batch_edit' %}?level={{ level.id }}&continue" 
                                hx-target="#app-content" hx-include="[name=term], [name=level]">
                                <span data-feather="arrow-right" class="feather-16 align-middle"></span>
                                Continuer
                            </a>
                        </div>
                    </div>
                </div>
            </form>
            <form method="post" class="table-responsive" id="table_container" 
                  action="{% url 'exams:grade_export' %}">
            </form>
        </div>
    </div>
</div>