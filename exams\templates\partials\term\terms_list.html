<div class="row" hx-get="{{ request.path }}?cycle={{cycle}}&lang={{lang}}&niveau={{ level }}&cycle={{ cycle }}" hx-trigger="saved from:body" hx-target="#app-content">
    <div class="col">
        <div class="tile">
            <div class="alert alert-success">
                <span data-feather="info" class="feather feather-16 align-middle"></span> 
                <span>
                    {% if cycle %}
                    Modifier la moyenne maximale ainsi que le coefficient par trimestre pour tous les niveaux du cycle {% if cycle == 'P' %} Primaire {% else %} Secondaire {% endif %}
                    {% else %}
                    Modifier la moyenne maximale ainsi que le coefficient pour les classes du niveau {{ request.GET.niveau }}
                    {% endif %}
                </span> <br>
            </div>
            <div class="table-responsive">
                <table class="table table-striped mb-0" id="datatable" style="font-size: 11.5px;">
                    <thead class="bg-primary text-white">
                    <tr>
                        <th>ANNEE SCOLAIRE</th>
                        <th style="min-width: 120px;" class="sticky-column sticky-header">LIBELLE</th>
                        <th>CODE</th>
                        <th>COEF</th>
                        <th>PERIODE SUR</th>
                        <th>PRIS EN COMPTE</th>
                        <th>ACTIONS</th>
                    </tr>
                    </thead>
                    <tbody>
                    {% for term in terms %}
                    <tr>
                        <td class="align-middle">{{ term.year }}</td>
                        <td class="align-middle sticky-column">{{ term.term }}</td>
                        <td class="align-middle">{{ term.term.code }}</td>
                        <td class="align-middle">{% if term.active %} {{ term.coefficient|stringformat:'02d' }} {% else %} 00 {% endif %}</td>
                        <td class="align-middle">{{ term.max|stringformat:'02d' }}</td>
                        <td class="align-middle">
                            <a href="" hx-get="{% url 'exams:term-status' term.pk %}?niveau={{ level }}&cycle={{ cycle }}" hx-target="#dialog"
                                class="btn btn-sm {% if term.active %}btn-success{% else %}btn-danger{% endif %}">
                                <span data-feather="{% if term.active %}check{% else %}x{% endif %}" 
                                      class="feather-16 align-middle">
                                </span>
                                {% if term.active %}OUI{% else %}NON{% endif %}
                            </a>
                        </td>
                        <td>
                            <a href="" class="btn btn-sm btn-warning" 
                               hx-get="{% url 'exams:term_edit' term.id %}?niveau={{ level }}&cycle={{ cycle }}"
                               hx-target="#dialog">
                                <span data-feather="edit" style="width: 14px; height: 14px;"></span> COEF
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>