# Generated by Django 4.2.4 on 2023-10-13 11:36

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('school', '0033_alter_school_cycle'),
        ('exams', '0027_subject_category'),
    ]

    operations = [
        migrations.CreateModel(
            name='StudentSubjectGroupAverage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('group', models.CharField(choices=[('L', 'Lettres'), ('S', 'Sciences'), ('A', 'Autres')], max_length=1)),
                ('average', models.DecimalField(decimal_places=2, default=0, max_digits=4)),
                ('rank', models.PositiveSmallIntegerField(default=0)),
                ('enrollment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='school.enrollment')),
                ('term', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='exams.schoolterm')),
            ],
            options={
                'unique_together': {('enrollment', 'term', 'group')},
            },
        ),
    ]
