<style>
.progress-button {
    position: relative;
    overflow: hidden;
    min-width: 120px;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.progress-button.active {
    border-color: rgba(40, 167, 69, 0.3);
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.1);
    animation: pulse-border 2s infinite;
}

@keyframes pulse-border {
    0% { box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.1); }
    50% { box-shadow: 0 0 0 0.3rem rgba(40, 167, 69, 0.15); }
    100% { box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.1); }
}

.progress-button .progress-overlay {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: linear-gradient(90deg, rgba(40, 167, 69, 0.35), rgba(32, 201, 151, 0.35));
    transition: width 0.6s ease;
    z-index: 1;
    border-radius: inherit;
    width: 0%;
    min-width: 0;
}

.progress-button .progress-bar-bottom {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 4px;
    background: linear-gradient(90deg, #28a745, #20c997);
    transition: width 0.6s ease;
    z-index: 3;
    border-radius: 0 0 4px 4px;
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.4);
    width: 0%;
    min-width: 0;
    display: block;
}

.progress-button.active .progress-bar-bottom {
    animation: glow-pulse 1.5s ease-in-out infinite alternate;
}

@keyframes glow-pulse {
    from { box-shadow: 0 1px 3px rgba(40, 167, 69, 0.3), 0 0 8px rgba(40, 167, 69, 0.2); }
    to { box-shadow: 0 1px 3px rgba(40, 167, 69, 0.5), 0 0 12px rgba(40, 167, 69, 0.4); }
}

.progress-button .progress-overlay::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        45deg,
        rgba(255, 255, 255, 0.1) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255, 255, 255, 0.1) 50%,
        rgba(255, 255, 255, 0.1) 75%,
        transparent 75%,
        transparent
    );
    background-size: 20px 20px;
    animation: progress-stripes 1s linear infinite;
    border-radius: inherit;
}

@keyframes progress-stripes {
    0% { background-position: 0 0; }
    100% { background-position: 20px 0; }
}

.progress-button .button-content {
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
}

.progress-button .progress-text {
    font-size: 0.75rem;
    margin-left: 0.5rem;
}

.progress-button.completed {
    background-color: #28a745 !important;
    border-color: #28a745 !important;
    color: white !important;
}

.progress-button.error {
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
    color: white !important;
}

.progress-button-container {
    position: relative;
}

.button-fade-out {
    animation: fadeOut 0.4s ease-out forwards;
}

.button-fade-in {
    opacity: 0;
    transform: scale(0.95) translateY(-5px);
    animation: fadeIn 0.5s ease-out forwards;
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
    to {
        opacity: 0;
        transform: scale(0.95) translateY(5px);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: scale(0.95) translateY(-5px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}
</style>

<div class="progress-button-container">
    {% if task_id %}
    <button class="btn btn-outline-{% if report_type == 'RP' %}primary{% else %}secondary{% endif %} progress-button active"
            id="progress-btn-{{ level_id }}-{{ report_type }}"
            disabled>
        <div class="progress-overlay"></div>
        <div class="progress-bar-bottom"></div>
        <div class="button-content">
            <span class="spinner-border spinner-border-sm mr-2" role="status"></span>
            <span class="button-text">Génération...</span>
            <span class="progress-text">0%</span>
        </div>
    </button>
    {% else %}
    <button class="btn btn-outline-danger progress-button" disabled>
        <div class="button-content">
            <span data-feather="alert-circle" class="feather-16 align-middle mr-1"></span>
            <span class="button-text">Erreur</span>
        </div>
    </button>
    {% endif %}
</div>

<script>
$(document).ready(function() {
    {% if task_id %}
    var progressUrl = "{% url 'celery_progress:task_status' task_id %}";
    var buttonId = '#progress-btn-{{ level_id }}-{{ report_type }}';
    var levelId = '{{ level_id }}';
    var termId = '{{ term }}';
    var reportType = '{{ report_type }}';
    var startTime = Date.now();
    var isCompleted = false;

    // Initialize feather icons
    if (typeof feather !== 'undefined') {
        feather.replace();
    }

    // Debug: Check if progress bar elements exist
    setTimeout(function() {
        var button = $(buttonId);
        var progressOverlay = button.find('.progress-overlay');
        var progressBarBottom = button.find('.progress-bar-bottom');

        console.log('Progress bar elements found:');
        console.log('Overlay element:', progressOverlay.length);
        console.log('Bottom bar element:', progressBarBottom.length);
    }, 500);

    // Custom progress handler for inline button
    function updateInlineProgress(progressBarElement, progressBarMessageElement, progress) {
        console.log('Progress update:', progress); // Debug log

        var percentage = Math.round(progress.percent || 0);
        var button = $(buttonId);
        var progressOverlay = button.find('.progress-overlay');
        var progressBarBottom = button.find('.progress-bar-bottom');
        var buttonText = button.find('.button-text');
        var progressText = button.find('.progress-text');

        // Update progress overlay and bottom bar
        console.log('Updating progress bars to:', percentage + '%'); // Debug log
        progressOverlay.css('width', percentage + '%');
        progressBarBottom.css('width', percentage + '%');
        progressText.text(percentage + '%');

        // Debug: Log the actual width values
        console.log('Overlay width:', progressOverlay.css('width'));
        console.log('Bottom bar width:', progressBarBottom.css('width'));

        // Update button text based on progress
        if (progress.description) {
            buttonText.text(progress.description);
        } else if (progress.current && progress.total) {
            buttonText.text(`${progress.current}/${progress.total}`);
        } else {
            buttonText.text('En cours...');
        }
    }

    // Success handler for inline button
    function onInlineSuccess(progressBarElement, progressBarMessageElement, result) {
        console.log('Task completed successfully:', result); // Debug log
        isCompleted = true;
        var button = $(buttonId);
        var buttonContent = button.find('.button-content');

        // Hide spinner and progress text
        button.find('.spinner-border').hide();
        button.find('.progress-text').hide();

        // Mark as completed
        button.addClass('completed');
        button.removeClass('active'); // Remove active animations
        button.find('.button-text').text('Terminé');

        // Check for generated file and replace with download link
        setTimeout(function() {
            checkForGeneratedFileAndReplace();
        }, 2000);
    }

    // Error handler for inline button
    function onInlineError(progressBarElement, progressBarMessageElement, excMessage) {
        isCompleted = true;
        var button = $(buttonId);

        // Hide spinner and progress text
        button.find('.spinner-border').hide();
        button.find('.progress-text').hide();

        // Mark as error
        button.addClass('error');
        button.find('.button-text').text('Erreur');

        // Re-enable button for retry
        setTimeout(function() {
            button.prop('disabled', false);
            button.removeClass('error');
            button.find('.button-text').text('Réessayer');
            button.find('.button-content').prepend('<span data-feather="refresh-cw" class="feather-16 align-middle mr-1"></span>');
            if (typeof feather !== 'undefined') {
                feather.replace();
            }
        }, 3000);
    }

    // Function to check for generated file and replace button with download link
    function checkForGeneratedFileAndReplace() {
        fetch('{% url "exams:get_generated_file_link" %}?level_id=' + levelId + '&term_id=' + termId + '&report_type=' + reportType)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.download_url) {
                    console.log('File ready, starting transition...'); // Debug log

                    // Start fade out animation
                    var button = $(buttonId);
                    var container = button.parent();

                    console.log('Adding fade-out class to button'); // Debug log
                    button.addClass('button-fade-out');

                    // Wait for fade out to complete, then replace with download link
                    setTimeout(function() {
                        console.log('Fade out complete, replacing with download link'); // Debug log

                        var buttonClass = reportType === 'RP' ? 'btn-outline-primary' : 'btn-outline-secondary';
                        var buttonText = reportType === 'RP' ? 'Bulletins' : 'Bulletins Complets';

                        // Create new button with initial hidden state
                        var newButtonHtml = '<a href="' + data.download_url + '" class="btn ' + buttonClass + '" style="opacity: 0; transform: scale(0.95) translateY(-5px); transition: all 0.5s ease-out;">' +
                            '<span data-feather="download" class="feather-16 align-middle mr-1"></span>' +
                            buttonText +
                            '</a>';

                        container.html(newButtonHtml);

                        // Re-initialize feather icons
                        if (typeof feather !== 'undefined') {
                            feather.replace();
                        }

                        // Trigger fade in animation
                        setTimeout(function() {
                            console.log('Starting fade in animation'); // Debug log
                            var newLink = container.find('a');
                            newLink.css({
                                'opacity': '1',
                                'transform': 'scale(1) translateY(0)'
                            });
                        }, 100);
                    }, 450); // Match the fadeOut animation duration + small buffer
                } else {
                    // File not ready yet, keep the completed button
                    console.log('File not ready yet');
                }
            })
            .catch(error => {
                console.error('Error checking file:', error);
            });
    }

    // Initialize progress tracking
    if (typeof CeleryProgressBar !== 'undefined') {
        CeleryProgressBar.initProgressBar(progressUrl, {
            onProgress: updateInlineProgress,
            onSuccess: onInlineSuccess,
            onError: onInlineError,
            progressBarElement: $(buttonId).find('.progress-overlay')[0],
            progressBarMessageElement: $(buttonId).find('.button-text')[0]
        });
    } else {
        console.error('CeleryProgressBar not found. Make sure celery-progress is loaded.');
        // Fallback: simulate progress for demo
        setTimeout(function() {
            onInlineSuccess(null, null, null);
        }, 5000);
    }
    {% endif %}
});
</script>
