{% extends 'partials/modal.html' %}
{% load widget_tweaks %}

{% block modal_title %} Historique des paiements {% endblock %}

{% block modal_body %}
<h4 class="text-muted font-weight-bold mb-3">Infos Elève</h4>
<h5>Nom et Prénoms: <span class="font-weight-bold">{{ payment.enrollment }}</span></h5>
<h4 class="text-muted font-weight-bold mb-3">Infos Versement</h4>
<div class="form-row">
    {% comment %}
    <div class="form-group col-6">
        <label for="{{ form.payment_type.id_for_label }}">{{ form.payment_type.label }}</label>
        {% render_field form.payment_type class='form-control' %}
        <span class="invalid-feedback">{{ form.payment_type.errors|first }}</span>
    </div>
    <div class="form-group col-6">
        <label for="{{ form.annexe_category.id_for_label }}">{{ form.annexe_category.label }}</label>
        {% render_field form.annexe_category class='form-control' %}
        <span class="invalid-feedback">{{ form.annexe_category.errors|first }}</span>
    </div>
    {% endcomment %}
    <div class="form-group col-4">
        <label for="{{ form.inscription.id_for_label }}">{{ form.inscription.label }}</label>
        {% render_field form.inscription class='form-control' %}
        <span class="invalid-feedback">{{ form.inscription.errors|first }}</span>
    </div>
    <div class="form-group col-4">
        <label for="{{ form.amount.id_for_label }}">{{ form.amount.label }}</label>
        {% render_field form.amount class='form-control' %}
        <span class="invalid-feedback">{{ form.amount.errors|first }}</span>
    </div>
    <div class="form-group col-4">
        <label for="{{ form.annexe.id_for_label }}">{{ form.annexe.label }}</label>
        {% render_field form.annexe class='form-control' %}
        <span class="invalid-feedback">{{ form.annexe.errors|first }}</span>
    </div>
</div>
{% endblock %}

{% block modal_footer %}
    <button type="submit" href="" class="btn btn-success">Enregistrer</button>
{% endblock %}