<!-- Staff role list view -->
<div class="tile" id="tile" hx-get="{% url 'school:staff_roles' %}?lang={{lang}}" hx-trigger="saved from:body" hx-target="#app-content">

    <div class="tile-title-w-btn">

        <div class="btn-group">

            <a class="btn btn-success" href="" 
               hx-get="{% url 'school:staff_role_create' %}" 
               hx-target="#dialog">+ Nouvel emploi/poste</a>
        </div>
    </div>
    <div class="table-responsive">
        <table class="table table-striped table-sm" id="datatable">

            <thead class="bg-primary text-white">
            <tr>
                <th>Emploi</th>
                <th>Code</th>
                <th>Membres</th>
                <th>Actions</th>
            </tr>
            </thead>
            <tbody>
                {% for role in staff_roles %}
                <tr>
                    <td class="align-middle">{{ role.name }}</td>
                    <td class="align-middle">{{ role.code }}</td>
                    <td class="align-middle">{{ role.staff_count }}</td>
                    <td class="align-middle">
                        {% if role.school %}
                        <a class="btn btn-primary btn-sm" href="" 
                           hx-get="{% url 'school:staff_role_edit' role.id %}" 
                           hx-target="#dialog"><span data-feather="edit" class="feather-16"></span></a>
                        <a class="btn btn-danger btn-sm" href="" 
                           hx-delete="" 
                           hx-target="#tile">
                           <span data-feather="trash" class="feather-16"></span>
                        </a>
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>