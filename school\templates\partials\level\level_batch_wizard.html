{% load widget_tweaks %}

<div class="card">
  <div class="card-header">
    <h5 class="card-title">{{ form_title }}</h5>
    <p class="card-text text-muted">{{ form_description }}</p>
  </div>
  <div class="card-body">
    <form method="post" hx-post="{{ request.path }}" hx-target="#app-content">
      {% csrf_token %}
      {{ wizard.management_form }}
      
      {% if wizard.steps.current == 'input' %}
        <!-- Input step -->
        <div class="form-row">
          {% for field in form %}
            <div class="form-group {{ col_width|default:'col-md-6' }}">
              <label for="{{ field.id_for_label }}">{{ field.label }}</label>
              {% render_field field class="form-control" %}
              {% if field.help_text %}
                <small class="form-text text-muted">{{ field.help_text }}</small>
              {% endif %}
              {% if field.errors %}
                <div class="invalid-feedback d-block">
                  {% for error in field.errors %}
                    {{ error }}
                  {% endfor %}
                </div>
              {% endif %}
            </div>
          {% endfor %}
        </div>
      {% elif wizard.steps.current == 'confirm' %}
        <!-- Confirmation step -->
        <div class="table-responsive">
          <table class="table table-bordered">
            <thead>
              <tr>
                <th>Classes</th>
              </tr>
            </thead>
            <tbody>
              {% for level in levels_preview %}
              <tr>
                <td>{{ level.name }}</td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
        
        <div class="alert alert-primary">
          <strong><i class="icon-info"></i> Résumé:</strong> 
          <span>{{ levels_preview|length }} classe(s) seront créée(s).</span>
        </div>
        
        {% if subschool %}
        <div class="alert alert-info">
          Ces classes seront créées pour l'établissement: <strong>{{ subschool.name }}</strong>
        </div>
        {% endif %}
      {% endif %}
      
      <div class="d-flex justify-content-between mt-3">
        {% if wizard.steps.prev %}
          <button name="wizard_goto_step" type="submit" value="{{ wizard.steps.prev }}" class="btn btn-outline-secondary">
            <i data-feather="chevron-left" class="feather-16 align-middle"></i> Retour
          </button>
        {% else %}
          <a href="{% url 'school:levels' %}?education={{ education|lower }}" class="btn btn-outline-secondary">
            <i data-feather="x" class="feather-16 align-middle"></i> Annuler
          </a>
        {% endif %}
        
        {% if wizard.steps.current == 'confirm' %}
          <button type="submit" class="btn btn-primary">
            <i class="feather-16 align-middle" data-feather="check"></i> Confirmer la création
          </button>
        {% else %}
          <button type="submit" class="btn btn-primary">
            <i class="feather-16 align-middle" data-feather="chevron-right"></i> Continuer
          </button>
        {% endif %}
      </div>
    </form>
  </div>
</div>

<script>
    if (typeof feather !== 'undefined') {
        feather.replace();
    }   
</script>
