{% if not perms.exams.manage_all_grades %}
    {% include "partials/breadcrumb.html" with title="Espace enseignant" icon="home" subtitle="Mes classes" %}
{% else %}
    {% include "partials/breadcrumb.html" with title="Accueil" icon="home" subtitle=subtitle %}
{% endif %}
<div class="tile {% if not perms.exams.manage_all_grades %} row {% endif %}" id="tile">
    {% if perms.exams.manage_all_grades %}
        <div class="input-group mb-3">
            <input type="search" class="form-control" id="search" name="search" 
                placeholder="Rechercher un élève par nom ou par matricule" 
                hx-get="{% if user.role == 'CS' %}{% url 'school:active_students' %}{% else %}{% url 'school:students' %}{% endif %}"
                hx-target="#app-content"
                hx-push-url="{% if user.role == 'CS' %}{% url 'school:active_students' %}{% else %}{% url 'school:students' %}{% endif %}" >
            <div class="input-group-append">
                <button class="btn btn-secondary" type="button" hx-get="{% if user.role == 'CS' %}{% url 'school:active_students' %}{% else %}{% url 'school:students' %}{% endif %}"
                hx-target="#app-content"
                hx-push-url="{% if user.role == 'CS' %}{% url 'school:active_students' %}{% else %}{% url 'school:students' %}{% endif %}"
                hx-include="[name=search]">
                <span data-feather="search"></span>
                </button>
            </div>
        </div>
      <h4 class="mb-2 mt-3 text-muted">Effectifs de la classe</h4>
      <div class="row">
        {% include "partials/card.html" with label="Garçons" value=data.boys color="info" %}
        {% include "partials/card.html" with label="Filles" value=data.girls color="danger" %}
        {% include "partials/card.html" with label="Total" value=data.students color="success" icon="users" %}
      </div>
    {% else %}
        {% for level in levels %}
            {% include "partials/teacher/card.html" with level=level value=level.students color="primary" %}
        {% endfor %}
    {% endif %}
</div>