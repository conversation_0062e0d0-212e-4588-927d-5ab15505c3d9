{% load widget_tweaks %}
<div class="tile">
    <div class="tab-pane active" id="profile">
        <h6>{{ page_title }}</h6>
        <hr>
        <form hx-disabled-elt="this" class="row" method="post" action="{{ request.path }}?section={{ active_nav }}" 
            hx-post="{{ request.path }}?section={{ active_nav }}"
            enctype="multipart/form-data"
            hx-on="
                htmx:beforeRequest: $('#submit-btn').html(`<span class='spinner-border spinner-border-sm mr-2' role='status' aria-hidden='true'></span>Exécution...`).prop('disabled', true);
                htmx:afterRequest: $(':submit').prop('disabled', false);  $('#submit-btn').text('Valider');
            ">
            {% csrf_token %}
            {% for field in form %}
            <div class="form-group {% if not request.GET.section == 'headers' %} col-md-3 {% else %} col-md-5 px-5 {% endif %}">
                <label for="{{ field.id_for_label }}">{{ field.label }}</label>
                {% render_field field class='form-control' %}
                <span class="invalid-feedback">{{ field.errors|first }}</span>
        
                {% if field.help_text %}
                    <span class="form-text text-muted">{{ field.help_text }}</span>
                {% endif %}
            </div>
            {% endfor %}  

            {% if request.GET.section == 'headers' %}
            <div class="container-fluid bg-info text-white my-3">
                <span class="font-weight-bold">Insérer un texte entre 4 étoiles (*) pour mettre en gras. Ex: **Union-Discipline-Travail**</span>
            </div>
            {% endif %}

            <div class="col-12">
                <button type="submit" class="btn btn-primary" id="submit-btn">Enregistrer</button>
                <button type="reset" class="btn btn-danger">Annuler</button>
            </div>
        </form>
      </div>
</div>