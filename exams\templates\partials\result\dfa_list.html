{% include "partials/breadcrumb.html" with title="Moyennes et DFA" year=year icon="users" subtitle="Résumé des Moyennes et Décisions de Fin d'Année" %}
<title>Moyennes Générales et DFA | EcolePro</title>
<div class="tile" id="tile" hx-target="#app-content">
    <form action="{% url 'exams:dfa_list' %}?education={{ education }}" hx-get="{% url 'exams:dfa_list' %}?education={{ education }}" method="get" class="mb-3" hx-target="#table_container">
        {% csrf_token %}
        
        <div class="form-row">
            <div class="col-6 col-md-3 form-group">
                <label for="generic_level">Niveau</label>
                <select name="short_name" id="generic_level" class="form-control" 
                        hx-get="{% url 'exams:sublevels' %}?lang={{education}}"
                        hx-target="#level_container">
                    <option value="">--------</option>
                    {% for level in generic_levels %}
                    <option value="{{ level.id }}">{{ level }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-6 col-md-3 form-group" id="level_container">
                <label for="level">Classe</label>
                <select name="level" id="level" class="form-control"></select>
            </div>
        </div>
        <button type="submit" class="btn btn-primary btn-sm btn-success">Valider</button>

    </form>
    <div class="table-responsive" id="table_container"></div>
</div>
