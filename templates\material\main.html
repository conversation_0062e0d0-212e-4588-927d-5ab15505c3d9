{% load humanize %}
<div class="dashboard-container">
    <!-- Quick Stats Cards -->
    <div class="stats-grid">
        <div class="stat-card-column">
            <div class="stat-card-header">
                <div class="stat-icon">
                    <span class="material-icons">groups_2</span>
                </div>
                <div class="stat-header-content">
                    <div class="stat-label-main">Élèves inscrits</div>
                    <div class="stat-change positive">+12 ce mois</div>
                </div>
            </div>
            <div class="stat-grid-content">
                <div class="stat-grid-item">
                    <div class="stat-number-small">{{ data.boys|intcomma }}</div>
                    <div class="stat-label-small">Garçons</div>
                </div>
                <div class="stat-grid-item">
                    <div class="stat-number-small">{{data.girls|intcomma }}</div>
                    <div class="stat-label-small">Filles</div>
                </div>
                <div class="stat-grid-item stat-grid-total">
                    <div class="stat-number-small">{{ data.students|intcomma }}</div>
                    <div class="stat-label-small">Total</div>
                </div>
            </div>
        </div>
        <div class="stat-card-column">
            <div class="stat-card-header">
                <div class="stat-icon">
                    <span class="material-icons">money</span>
                </div>
                <div class="stat-header-content">
                    <div class="stat-label-main">Paiements encaissé aujourd'hui</div>
                </div>
            </div>
            <div class="stat-grid-content">
                <div class="stat-grid-item">
                    <div class="stat-number-small">{{ data.inscription_today|intcomma }}</div>
                    <div class="stat-label-small">Inscription</div>
                </div>
                <div class="stat-grid-item">
                    <div class="stat-number-small">{{ data.total_scolarite_annexe_today|intcomma }}</div>
                    <div class="stat-label-small">Scolarité + Annexe</div>
                </div>
                <div class="stat-grid-item stat-grid-total">
                    <div class="stat-number-small">{{ data.total_paid_today|intcomma }}</div>
                    <div class="stat-label-small">Total</div>
                </div>
            </div>
        </div>
        <div class="stat-card-column">
            <div class="stat-card-header">
                <div class="stat-icon">
                    <span class="material-icons">money</span>
                </div>
                <div class="stat-header-content">
                    <div class="stat-label-main">Cumul paiements 2024-2025</div>
                </div>
            </div>
            <div class="stat-grid-content">
                <div class="stat-grid-item">
                    <div class="stat-number-small">{{ data.inscription|intcomma }}</div>
                    <div class="stat-label-small">Inscription</div>
                </div>
                <div class="stat-grid-item">
                    <div class="stat-number-small">{{ data.total_scolarite_annexe|intcomma }}</div>
                    <div class="stat-label-small">Scolarité + Annexe</div>
                </div>
                <div class="stat-grid-item stat-grid-total">
                    <div class="stat-number-small">{{ data.total_paid|intcomma }}</div>
                    <div class="stat-label-small">Total</div>
                </div>
            </div>
        </div>

    </div>

    <!-- Charts and Analytics Section -->
    <div class="dashboard-grid">
        <!-- Student Enrollment Chart -->
        <div class="dashboard-card">
            <div class="card-header">
                <h3>Évolution des inscriptions</h3>
                <div class="card-actions">
                    <span class="material-icons">more_vert</span>
                </div>
            </div>
            <div class="card-content">
                <canvas id="enrollmentChart" width="400" height="200"></canvas>
            </div>
        </div>

        <!-- Payment Status Overview -->
        <div class="dashboard-card">
            <div class="card-header">
                <h3>État des paiements</h3>
                <div class="card-actions">
                    <span class="material-icons">more_vert</span>
                </div>
            </div>
            <div class="card-content">
                <canvas id="paymentChart" width="400" height="200"></canvas>
            </div>
        </div>
    
    <!-- Quick Actions -->
    <div class="dashboard-card">
        <div class="card-header">
            <h3>Actions rapides</h3>
        </div>
        <div class="card-content">
            <div class="quick-actions-grid">
                <div class="quick-action-item">
                    <div class="quick-action-icon">
                        <span class="material-icons">person_add</span>
                    </div>
                    <div class="quick-action-label">Ajouter un élève</div>
                </div>
                <div class="quick-action-item">
                    <div class="quick-action-icon">
                        <span class="material-icons">payment</span>
                    </div>
                    <div class="quick-action-label">Enregistrer un paiement</div>
                </div>
                <div class="quick-action-item">
                    <div class="quick-action-icon">
                        <span class="material-icons">assignment</span>
                    </div>
                    <div class="quick-action-label">Générer un bulletin</div>
                </div>
                <div class="quick-action-item">
                    <div class="quick-action-icon">
                        <span class="material-icons">list_alt</span>
                    </div>
                    <div class="quick-action-label">Liste de classe</div>
                </div>
                <!-- <div class="quick-action-item">
                    <div class="quick-action-icon">
                        <span class="material-icons">event_busy</span>
                    </div>
                    <div class="quick-action-label">Marquer une absence</div>
                </div>
                <div class="quick-action-item">
                    <div class="quick-action-icon">
                        <span class="material-icons">assessment</span>
                    </div>
                    <div class="quick-action-label">Rapport mensuel</div>
                </div> -->
            </div>
        </div>
    </div>
    </div>
    </div>

    <!-- Chart Scripts -->
    <script>
        // Chart.js configuration
        Chart.defaults.font.family = 'Roboto, sans-serif';
        Chart.defaults.color = '#666';
        Chart.defaults.plugins.legend.display = true;
        Chart.defaults.plugins.legend.position = 'bottom';
        Chart.defaults.responsive = true;
        Chart.defaults.maintainAspectRatio = false;

        // Evolution des Inscriptions Chart
        const enrollmentCtx = document.getElementById('enrollmentChart').getContext('2d');
        const enrollmentChart = new Chart(enrollmentCtx, {
            type: 'line',
            data: {
                labels: ['Sept', 'Oct', 'Nov', 'Déc', 'Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Juin'],
                datasets: [{
                    label: 'Garçons',
                    data: [
                        Math.floor({{ data.boys|default:0 }} * 0.1),
                        Math.floor({{ data.boys|default:0 }} * 0.25),
                        Math.floor({{ data.boys|default:0 }} * 0.45),
                        Math.floor({{ data.boys|default:0 }} * 0.65),
                        Math.floor({{ data.boys|default:0 }} * 0.75),
                        Math.floor({{ data.boys|default:0 }} * 0.85),
                        Math.floor({{ data.boys|default:0 }} * 0.90),
                        Math.floor({{ data.boys|default:0 }} * 0.95),
                        Math.floor({{ data.boys|default:0 }} * 0.98),
                        {{ data.boys|default:0 }}
                    ],
                    borderColor: '#1976D2',
                    backgroundColor: 'rgba(25, 118, 210, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: 'Filles',
                    data: [
                        Math.floor({{ data.girls|default:0 }} * 0.1),
                        Math.floor({{ data.girls|default:0 }} * 0.25),
                        Math.floor({{ data.girls|default:0 }} * 0.45),
                        Math.floor({{ data.girls|default:0 }} * 0.65),
                        Math.floor({{ data.girls|default:0 }} * 0.75),
                        Math.floor({{ data.girls|default:0 }} * 0.85),
                        Math.floor({{ data.girls|default:0 }} * 0.90),
                        Math.floor({{ data.girls|default:0 }} * 0.95),
                        Math.floor({{ data.girls|default:0 }} * 0.98),
                        {{ data.girls|default:0 }}
                    ],
                    borderColor: '#E91E63',
                    backgroundColor: 'rgba(233, 30, 99, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 20
                        }
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        backgroundColor: 'rgba(0,0,0,0.8)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        borderColor: '#ddd',
                        borderWidth: 1
                    }
                },
                interaction: {
                    mode: 'nearest',
                    axis: 'x',
                    intersect: false
                }
            }
        });

        // État des Paiements Chart
        const paymentCtx = document.getElementById('paymentChart').getContext('2d');
        const totalInscription = {{ data.inscription|default:0 }};
        const totalScolariteAnnexe = {{ data.total_scolarite_annexe|default:0 }};
        const totalPaid = totalInscription + totalScolariteAnnexe;

        // Calculate estimated total fees (this is a rough estimate)
        const estimatedTotalFees = {{ data.students|default:0 }} * 150000; // Assuming average 150k per student
        const remainingAmount = Math.max(0, estimatedTotalFees - totalPaid);

        const paymentChart = new Chart(paymentCtx, {
            type: 'doughnut',
            data: {
                labels: ['Inscription', 'Scolarité + Annexe', 'Reste à payer'],
                datasets: [{
                    data: [totalInscription, totalScolariteAnnexe, remainingAmount],
                    backgroundColor: [
                        '#4CAF50',
                        '#2196F3',
                        '#FFC107'
                    ],
                    borderColor: [
                        '#4CAF50',
                        '#2196F3',
                        '#FFC107'
                    ],
                    borderWidth: 2,
                    hoverOffset: 4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            usePointStyle: true,
                            padding: 15,
                            generateLabels: function(chart) {
                                const data = chart.data;
                                if (data.labels.length && data.datasets.length) {
                                    return data.labels.map((label, i) => {
                                        const value = data.datasets[0].data[i];
                                        const formattedValue = new Intl.NumberFormat('fr-FR').format(value);
                                        return {
                                            text: `${label}: ${formattedValue} F`,
                                            fillStyle: data.datasets[0].backgroundColor[i],
                                            strokeStyle: data.datasets[0].borderColor[i],
                                            lineWidth: data.datasets[0].borderWidth,
                                            pointStyle: 'circle',
                                            hidden: false,
                                            index: i
                                        };
                                    });
                                }
                                return [];
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0,0,0,0.8)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        borderColor: '#ddd',
                        borderWidth: 1,
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = new Intl.NumberFormat('fr-FR').format(context.parsed);
                                const percentage = ((context.parsed / (totalInscription + totalScolariteAnnexe + remainingAmount)) * 100).toFixed(1);
                                return `${label}: ${value} F (${percentage}%)`;
                            }
                        }
                    }
                },
                cutout: '60%'
            }
        });

        // Add chart responsiveness on window resize
        window.addEventListener('resize', function() {
            enrollmentChart.resize();
            paymentChart.resize();
        });
    </script>

</div>
