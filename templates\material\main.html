{% load humanize %}
<div class="dashboard-container">
    <!-- Quick Stats Cards -->
    <div class="stats-grid">
        <div class="stat-card-column">
            <div class="stat-card-header">
                <div class="stat-icon">
                    <span class="material-icons">groups_2</span>
                </div>
                <div class="stat-header-content">
                    <div class="stat-label-main">Élèves inscrits</div>
                    <div class="stat-change positive">+12 ce mois</div>
                </div>
            </div>
            <div class="stat-grid-content">
                <div class="stat-grid-item">
                    <div class="stat-number-small">{{ data.boys|intcomma }}</div>
                    <div class="stat-label-small">Garçons</div>
                </div>
                <div class="stat-grid-item">
                    <div class="stat-number-small">{{data.girls|intcomma }}</div>
                    <div class="stat-label-small">Filles</div>
                </div>
                <div class="stat-grid-item stat-grid-total">
                    <div class="stat-number-small">{{ data.students|intcomma }}</div>
                    <div class="stat-label-small">Total</div>
                </div>
            </div>
        </div>
        <div class="stat-card-column">
            <div class="stat-card-header">
                <div class="stat-icon">
                    <span class="material-icons">money</span>
                </div>
                <div class="stat-header-content">
                    <div class="stat-label-main">Paiements encaissé aujourd'hui</div>
                </div>
            </div>
            <div class="stat-grid-content">
                <div class="stat-grid-item">
                    <div class="stat-number-small">{{ data.inscription_today|intcomma }}</div>
                    <div class="stat-label-small">Inscription</div>
                </div>
                <div class="stat-grid-item">
                    <div class="stat-number-small">{{ data.total_scolarite_annexe_today|intcomma }}</div>
                    <div class="stat-label-small">Scolarité + Annexe</div>
                </div>
                <div class="stat-grid-item stat-grid-total">
                    <div class="stat-number-small">{{ data.total_paid_today|intcomma }}</div>
                    <div class="stat-label-small">Total</div>
                </div>
            </div>
        </div>
        <div class="stat-card-column">
            <div class="stat-card-header">
                <div class="stat-icon">
                    <span class="material-icons">money</span>
                </div>
                <div class="stat-header-content">
                    <div class="stat-label-main">Cumul paiements 2024-2025</div>
                </div>
            </div>
            <div class="stat-grid-content">
                <div class="stat-grid-item">
                    <div class="stat-number-small">{{ data.inscription|intcomma }}</div>
                    <div class="stat-label-small">Inscription</div>
                </div>
                <div class="stat-grid-item">
                    <div class="stat-number-small">{{ data.total_scolarite_annexe|intcomma }}</div>
                    <div class="stat-label-small">Scolarité + Annexe</div>
                </div>
                <div class="stat-grid-item stat-grid-total">
                    <div class="stat-number-small">{{ data.total_paid|intcomma }}</div>
                    <div class="stat-label-small">Total</div>
                </div>
            </div>
        </div>

    </div>

    <!-- Charts and Analytics Section -->
    <div class="dashboard-grid">
        <!-- Monthly Payments Chart -->
        <div class="dashboard-card">
            <div class="card-header">
                <h3>Évolution des paiements</h3>
                <div class="card-actions">
                    <div class="dropdown">
                        <span class="material-icons dropdown-trigger" id="paymentsChartMenu">more_vert</span>
                        <div class="dropdown-content" id="paymentsChartDropdown">
                            <a href="#" class="dropdown-item" onclick="refreshChart('payments')">
                                <span class="material-icons">refresh</span>
                                Actualiser le graphique
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-content">
                <canvas id="enrollmentChart" width="400" height="200"></canvas>
            </div>
        </div>

        <!-- Payment Status Overview -->
        <div class="dashboard-card">
            <div class="card-header">
                <h3>État des paiements</h3>
                <div class="card-actions">
                    <div class="dropdown">
                        <span class="material-icons dropdown-trigger" id="statusChartMenu">more_vert</span>
                        <div class="dropdown-content" id="statusChartDropdown">
                            <a href="#" class="dropdown-item" onclick="refreshChart('status')">
                                <span class="material-icons">refresh</span>
                                Actualiser le graphique
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-content">
                <canvas id="paymentChart" width="400" height="200"></canvas>
            </div>
        </div>
    
    <!-- Quick Actions -->
    <div class="dashboard-card">
        <div class="card-header">
            <h3>Actions rapides</h3>
        </div>
        <div class="card-content">
            <div class="quick-actions-grid">
                <div class="quick-action-item">
                    <div class="quick-action-icon">
                        <span class="material-icons">person_add</span>
                    </div>
                    <div class="quick-action-label">Ajouter un élève</div>
                </div>
                <div class="quick-action-item">
                    <div class="quick-action-icon">
                        <span class="material-icons">payment</span>
                    </div>
                    <div class="quick-action-label">Enregistrer un paiement</div>
                </div>
                <div class="quick-action-item">
                    <div class="quick-action-icon">
                        <span class="material-icons">assignment</span>
                    </div>
                    <div class="quick-action-label">Générer un bulletin</div>
                </div>
                <div class="quick-action-item">
                    <div class="quick-action-icon">
                        <span class="material-icons">list_alt</span>
                    </div>
                    <div class="quick-action-label">Liste de classe</div>
                </div>
                <!-- <div class="quick-action-item">
                    <div class="quick-action-icon">
                        <span class="material-icons">event_busy</span>
                    </div>
                    <div class="quick-action-label">Marquer une absence</div>
                </div>
                <div class="quick-action-item">
                    <div class="quick-action-icon">
                        <span class="material-icons">assessment</span>
                    </div>
                    <div class="quick-action-label">Rapport mensuel</div>
                </div> -->
            </div>
        </div>
    </div>
    </div>
    </div>

    <!-- Chart Data -->
    <script>
        // Pass Django data to JavaScript
        window.chartData = {
            boys: {{ data.boys|default:0 }},
            girls: {{ data.girls|default:0 }},
            students: {{ data.students|default:0 }},
            inscription: {{ data.inscription|default:0 }},
            total_scolarite_annexe: {{ data.total_scolarite_annexe|default:0 }},
            monthly_payments: [{% for payment in data.monthly_payments %}{{ payment }}{% if not forloop.last %},{% endif %}{% endfor %}]
        };
    </script>

</div>
