/* Dark Mode Styles */

/* Variables for dark mode */

:root {
  --dark-bg-primary: #1e1e2d;
  --dark-bg-secondary: #2a2a3c;
  --dark-text-primary: #e4e6ef;
  --dark-text-secondary: #b5b5c3;
  --dark-border-color: #3f4254;
  --dark-card-bg: #2a2a3c;
  --dark-hover-bg: #2b2b40;
  --dark-selected-bg: #333363;
  --dark-input-bg: #323248;
  --dark-input-border: #3f4254;
  --dark-sidebar-bg: #1e1e2d;
  --dark-sidebar-active: #1b1b28;
  --dark-sidebar-hover: #28283e;
  --dark-header-bg: #1a1a27;
  --dark-shadow: rgba(0, 0, 0, 0.2);
  
  /* Transition settings */
  --transition-speed: 0.3s;
  --transition-timing: ease;
}

/* Base transition for all elements */
body, .app-sidebar, .app-header, .app-content, .tile, .widget-small, .card, 
.table, .form-control, .btn, .dropdown-menu, .modal-content, .app-menu__item,
.app-sidebar__user, .app-nav__item, input, select, textarea, .app-title,
.dashboard-wrapper, .table-card,
.app-header__logo {
  transition: all var(--transition-speed) var(--transition-timing);
}

/* Dark mode class applied to body */
body.dark-mode {
  background-color: var(--dark-bg-primary);
  color: var(--dark-text-primary);
}

/* Header */
body.dark-mode .app-header,
body.dark-mode .app-header__logo,
body.dark-mode .navbar
 {
  background-color: var(--dark-header-bg) !important;
  border-bottom: 1px solid var(--dark-border-color);
}

body.dark-mode .navbar,
body.dark-mode .nav-item
 {
  color: var(--dark-text-primary) !important
}
/* Sidebar */
body.dark-mode .app-sidebar {
  background-color: var(--dark-sidebar-bg);
  border-right: 1px solid var(--dark-border-color);
}

body.dark-mode .app-sidebar__user {
  border-bottom: 1px solid var(--dark-border-color);
  color: var(--dark-text-primary);
}

body.dark-mode .app-menu__item {
  color: var(--dark-text-secondary);
  border-bottom: 1px solid var(--dark-border-color);
}

body.dark-mode .app-menu__item:hover {
  background-color: var(--dark-sidebar-hover);
  color: var(--dark-text-primary);
}

body.dark-mode .app-menu__item.active {
  background-color: var(--dark-sidebar-active);
  color: #fff;
}

body.dark-mode .treeview-menu {
  background-color: var(--dark-sidebar-active);
}

body.dark-mode .treeview-item {
  color: var(--dark-text-secondary);
}

body.dark-mode .treeview-item:hover {
  color: var(--dark-text-primary);
  background-color: var(--dark-sidebar-hover);
}

/* Main content */
body.dark-mode .app-content {
  background-color: var(--dark-bg-primary);
}

body.dark-mode .app-title {
  background-color: var(--dark-bg-secondary);
  border-bottom: 1px solid var(--dark-border-color);
  color: var(--dark-text-primary);
}

/* Cards and Tiles */
body.dark-mode .tile, 
body.dark-mode .card,
body.dark-mode .widget-small,
body.dark-mode .dashboard-wrapper, 
body.dark-mode .table-card,
body.dark-mode .stat-card,
body.dark-mode .action-button,
body.dark-mode .bulk-actions,
body.dark-mode .filter-btn,
body.dark-mode .checkmark,
body.dark-mode .bs-canvas,
body.dark-mode .bs-canvas-header,
body.dark-mode .swal2-popup,
body.dark-mode .page-link,
body.dark-mode .alert,
body.dark-mode .wrap-login100,
body.dark-mode .filter-panel,
body.dark-mode .search-results-info,
body.dark-mode .nav-link.active,
body.dark-mode .bg-white,
body.dark-mode .filters-section {
  background-color: var(--dark-card-bg) !important;
  color: var(--dark-text-primary);
  border: 1px solid var(--dark-border-color);
  box-shadow: 0 2px 10px var(--dark-shadow);
}


body.dark-mode .student-row.selected, 
body.dark-mode .student-row.selected .sticky-column {
  background-color: var(--dark-selected-bg) !important;
}

body.dark-mode .tile-title,
body.dark-mode .card-title {
  color: var(--dark-text-primary);
}

body.dark-mode .tile-footer {
  background-color: var(--dark-bg-secondary);
  border-top: 1px solid var(--dark-border-color);
}

/* Tables */
body.dark-mode .table {
  color: var(--dark-text-primary);
}

body.dark-mode .table thead th {
  background-color: var(--dark-bg-secondary);
  color: var(--dark-text-primary);
  border-bottom: 2px solid var(--dark-border-color);
}

body.dark-mode .table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(255, 255, 255, 0.05);
}

body.dark-mode .table-hover tbody tr:hover {
  background-color: var(--dark-hover-bg) !important;
}

body.dark-mode table tr:nth-child(even) td.sticky-column {
  background: var(--dark-bg-secondary);
}

body.dark-mode table .sticky-header {
  background: var(--dark-bg-secondary) !important;
}

body.dark-mode table tr:nth-child(odd) td.sticky-column {
  background: rgba(255, 255, 255, 0) !important;
  opacity: 1 !important;
}

body.dark-mode .student-row:hover,
body.dark-mode .student-row:hover .sticky-column {
  background: var(--dark-selected-bg) !important;
}

body.dark-mode .table td, 
body.dark-mode .table th {
  border-top: 1px solid var(--dark-bor der-color);
}

/* Forms */
body.dark-mode .form-control {
  background-color: var(--dark-input-bg);
  border: 1px solid var(--dark-input-border);
  color: var(--dark-text-primary);
}

body.dark-mode .form-control:focus {
  background-color: var(--dark-input-bg);
  border-color: #7367f0;
  color: var(--dark-text-primary);
}

body.dark-mode .form-control::placeholder {
  color: var(--dark-text-secondary);
}

body.dark-mode .form-control:disabled,
body.dark-mode .form-control[readonly] {
  background-color: rgba(50, 50, 72, 0.5);
}

body.dark-mode label {
  color: var(--dark-text-primary);
}

/* Buttons */
body.dark-mode .btn-primary {
  background-color: #7367f0;
  border-color: #7367f0;
}

body.dark-mode .btn-secondary {
  background-color: #82868b;
  border-color: #82868b;
}

body.dark-mode .btn-success {
  background-color: #28c76f;
  border-color: #28c76f;
}

body.dark-mode .btn-info {
  background-color: #00cfe8;
  border-color: #00cfe8;
}

body.dark-mode .btn-warning {
  background-color: #ff9f43;
  border-color: #ff9f43;
}

body.dark-mode .btn-danger {
  background-color: #ea5455;
  border-color: #ea5455;
}

body.dark-mode .btn-outline-primary,
body.dark-mode .btn-outline-secondary,
body.dark-mode .btn-outline-success,
body.dark-mode .btn-outline-info,
body.dark-mode .btn-outline-warning,
body.dark-mode .btn-outline-danger {
  color: var(--dark-text-primary);
}

/* Modals */
body.dark-mode .modal-content {
  background-color: var(--dark-card-bg) !important;
  border: 1px solid var(--dark-border-color);
}

body.dark-mode .modal-header {
  border-bottom: 1px solid var(--dark-border-color);
}

body.dark-mode .modal-footer {
  border-top: 1px solid var(--dark-border-color);
}

body.dark-mode .close {
  color: var(--dark-text-primary);
}

/* Dropdowns */
body.dark-mode .dropdown-menu {
  background-color: var(--dark-card-bg);
  border: 1px solid var(--dark-border-color);
}

body.dark-mode .dropdown-item {
  color: var(--dark-text-primary);
}

body.dark-mode .dropdown-item:hover,
body.dark-mode .dropdown-item:focus {
  background-color: var(--dark-hover-bg);
  color: var(--dark-text-primary);
}

body.dark-mode .dropdown-divider {
  border-top: 1px solid var(--dark-border-color);
}

/* Alerts */
body.dark-mode .alert {
  background-color: var(--dark-card-bg);
  border: 1px solid var(--dark-border-color);
  color: var(--dark-text-primary);
}

/* Badges */
body.dark-mode .badge {
  background-color: var(--dark-bg-secondary);
}

/* Dark mode toggle button */
.dark-mode-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.dark-mode-toggle:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

body.dark-mode .dark-mode-toggle:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Icon transition */
.dark-mode-icon {
  transition: transform 0.5s ease;
}

body.dark-mode .dark-mode-icon {
  transform: rotate(360deg);
}
