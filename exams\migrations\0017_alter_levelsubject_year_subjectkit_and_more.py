# Generated by Django 4.2.4 on 2023-09-20 12:29

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('school', '0031_school_address_alter_school_director_ar'),
        ('exams', '0016_alter_combinedyearresult_options_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='levelsubject',
            name='year',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='school.year'),
        ),
        migrations.CreateModel(
            name='SubjectKit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.Char<PERSON>ield(max_length=255)),
                ('cycle', models.Char<PERSON>ield(choices=[('P', 'Primaire'), ('S', 'Secondaire')], max_length=1, null=True, verbose_name='cycle')),
                ('education', models.CharField(choices=[('A', 'Arabe'), ('F', 'Français')], max_length=1, verbose_name='éducation')),
                ('levels', models.ManyToManyField(to='school.genericlevel')),
                ('subjects', models.ManyToManyField(to='exams.levelsubject')),
            ],
            options={
                'verbose_name': 'Kit de matières',
            },
        ),
        migrations.CreateModel(
            name='CompleteCycleSubjectKit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('cycle', models.CharField(choices=[('P', 'Primaire'), ('S', 'Secondaire')], max_length=1, null=True, verbose_name='cycle')),
                ('education', models.CharField(choices=[('A', 'Arabe'), ('F', 'Français')], max_length=1, verbose_name='éducation')),
                ('subject_kits', models.ManyToManyField(to='exams.subjectkit', verbose_name='packs de matière')),
            ],
            options={
                'verbose_name': 'Kits complets par cycle',
            },
        ),
    ]
