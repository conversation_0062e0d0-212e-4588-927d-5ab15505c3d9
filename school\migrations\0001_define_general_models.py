# Generated by Django 4.2.2 on 2023-06-26 00:18

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Founder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255, verbose_name='nom et prénoms')),
                ('phone', models.CharField(max_length=255, unique=True, verbose_name='contact')),
            ],
            options={
                'verbose_name': 'fondateur',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='GenericLevel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, verbose_name='nom du niveau')),
                ('short_name', models.CharField(max_length=10, verbose_name='abbréviation')),
                ('order', models.PositiveSmallIntegerField(verbose_name='ordre')),
            ],
            options={
                'verbose_name': 'niveau',
                'verbose_name_plural': 'niveaux',
                'ordering': ['order'],
            },
        ),
        migrations.CreateModel(
            name='Location',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=255)),
                ('name', models.CharField(max_length=255, verbose_name='localité')),
            ],
            options={
                'verbose_name': 'localité',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Year',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.PositiveSmallIntegerField(help_text='Ex: 2023', unique=True, verbose_name='abbréviation')),
                ('full_name', models.PositiveSmallIntegerField(help_text='Ex: 2022-2023', unique=True, verbose_name='libellé')),
                ('active', models.BooleanField(default=False, verbose_name='en cours')),
                ('comment', models.CharField(max_length=255)),
            ],
            options={
                'verbose_name': 'année scolaire',
                'verbose_name_plural': 'années scolaires',
            },
        ),
        migrations.CreateModel(
            name='School',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255, verbose_name='nom')),
                ('translation', models.CharField(blank=True, max_length=255, null=True, verbose_name='traduction')),
                ('phone1', models.CharField(max_length=14, verbose_name='contact 1')),
                ('phone2', models.CharField(blank=True, max_length=14, null=True, verbose_name='contact 2')),
                ('code', models.CharField(blank=True, max_length=255, null=True)),
                ('education', models.CharField(choices=[('A', 'Franco-Arabe'), ('F', 'Française')], default='A', max_length=1, verbose_name="type d'école")),
                ('director_fr', models.CharField(max_length=255, verbose_name='directeur')),
                ('director_ar', models.CharField(max_length=255, verbose_name='directeur arabe')),
                ('pricing_option', models.PositiveSmallIntegerField(choices=[(0, 'Selon la classe arabe'), (1, 'Selon la classe française'), (2, 'Selon le plus haut niveau'), (3, 'Selon le plus bas niveau'), (0, 'Combiner les frais des deux classes')], default=1, verbose_name='calculer le coût de la scolarité selon')),
                ('founder', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='school.founder', verbose_name='fondateur')),
                ('location', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='school.location', verbose_name='localité')),
            ],
            options={
                'verbose_name': 'école',
                'ordering': ['name'],
            },
        ),
    ]
