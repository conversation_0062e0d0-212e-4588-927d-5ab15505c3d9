{% load widget_tweaks %}
<div class="row" id="students_list">
    <div class="col">
        <div class="tile">
            <form action="" method="post" class="mb-3">
                {% csrf_token %}
                <div class="form-row">
                    <div class="col-sm-6 col-md-3 form-group">
                        <label for="generic_level">Niveau</label>
                        <select name="short_name" id="generic_level" class="form-control" 
                                hx-get="{% url 'exams:sublevels' %}?lang={{lang}}"
                                hx-target="#level_container">
                            <option value="">--------</option>
                            {% for level in generic_levels %}
                            <option value="{{ level.id }}">{{ level }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-sm-6 col-md-3 form-group" id="level_container">
                        <label for="level">Classe</label>
                        <select name="level" id="level" class="form-control"></select>
                    </div>
                    <div class="col-sm-6 col-md-3 form-group" id="term_container">
                        <label for="id_term">Période</label>
                        <select name="term" id="id_term" class="form-control" 
                                required="required" hx-swap="outerHTML">
                        </select>
                    </div>

                </div>
                <div class="d-flex justify-content-between">
                    <button type="submit" class="btn btn-success btn-sm" hx-get="{% url 'exams:custom_marking_sheet' %}" 
                        hx-target="#table_container" hx-include="[name=level], [name=term]">Afficher</button>
                </div>

                <div class="table-responsive" id="table_container">
                      
                </div>
            </form> 
        </div>
    </div>
</div>