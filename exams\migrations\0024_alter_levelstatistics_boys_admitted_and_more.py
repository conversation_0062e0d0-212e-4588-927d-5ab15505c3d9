# Generated by Django 4.2.4 on 2023-10-11 07:31

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('exams', '0023_levelstatistics'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='levelstatistics',
            name='boys_admitted',
            field=models.PositiveSmallIntegerField(default=0),
        ),
        migrations.AlterField(
            model_name='levelstatistics',
            name='boys_perc',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=5),
        ),
        migrations.AlterField(
            model_name='levelstatistics',
            name='girls_admitted',
            field=models.PositiveSmallIntegerField(default=0),
        ),
        migrations.AlterField(
            model_name='levelstatistics',
            name='girls_perc',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=5),
        ),
        migrations.AlterField(
            model_name='levelstatistics',
            name='level_average',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=5),
        ),
        migrations.Alter<PERSON>ield(
            model_name='levelstatistics',
            name='max_average',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=5),
        ),
        migrations.AlterField(
            model_name='levelstatistics',
            name='min_average',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=5),
        ),
    ]
