from school import models
from main.utils import (
    CYCLE_PRIMARY, EDUCATION_ARABIC, 
    ROLE_TEACHER, CYCLE_BOTH, CYCLE_SECONDARY
)

def get_terms_navs(request, year, url, lang):
    navs = []
    generic_levels = models.GenericLevel.objects.for_school(request.user, year)

    subscription = request.user.get_school_subscription(
        school_id=request.user.school_id,
        year_id=request.session.get(f'{request.user.id}'))
    
    if subscription and subscription.plan == models.Subscription.PLAN_LEVEL:
        subscription_level_id = subscription.level_id
        generic_levels = generic_levels.filter(id=subscription_level_id or 0)

    school = request.user.get_school()

    if school.cycle != CYCLE_BOTH:
        navs.append({
            'description': 'PERIODES ECOLE', 
            'url': f'{url}?lang={lang}&cycle={school.cycle}', 
            'active_nav': f'{school.cycle}',
            'icon': 'square'
        })
    else:
        navs.append({
            'description': 'PRIMAIRE', 
            'url': f'{url}?lang={lang}&cycle=P', 
            'active_nav': f'{CYCLE_PRIMARY}',
        })
        navs.append({
            'description': 'SECONDAIRE', 
            'url': f'{url}?lang={lang}&cycle=S', 
            'active_nav': f'{CYCLE_SECONDARY}',
        })
    
    if lang.upper()[0] == 'A':
        for level in generic_levels:
            nav = {
                'description': f'{level.short_name}', 
                'url': f'{url}?lang={lang}&niveau={level.short_name}', 
                'active_nav': f'{level.short_name}'
            }
            navs.append(nav)
    return navs


def get_subjects_navs(url, cycle):
    cycle = 'primaire' if cycle == CYCLE_PRIMARY else 'secondaire' 
    return [
        {
            'description': 'Français', 
            'url': f'{url}?cycle={cycle}&lang=fr', 
            'active_nav': ''
        },
        {
            'description': 'Arabe', 
            'url': f'{url}?cycle={cycle}&lang=ar', 
            'active_nav': 'ar'
        },
    ]

def get_level_subjects_navs(user, year, url, lang):
    navs = []
    generic_levels = models.GenericLevel.objects.for_school(user, year)

    for level in generic_levels:
        nav = {
            'description': f'{level.short_name}', 
            'url': f'{url}?lang={lang}&niveau={level.short_name}', 
            'active_nav': f'{level.short_name}'
        }
        navs.append(nav)
    return navs


def get_grades_navs(
        user, url, lang, subject_import_url, 
        all_grades_import_url, results_update_url,
        lock_marks_url):
    nav = [
        {
            'description': 'Moyennes par période', 
            'url': f'{url}?type=tout&lang={lang}', 
            'active_nav': 'tout',
            'icon': 'columns',
        },
        {
            'description': 'Importations', 
            'url': f'{all_grades_import_url}?lang={lang}', 
            'active_nav': 'importation',
            'icon': 'arrow-down'
        },
        {
            'description': 'Calculs des moyennes', 
            'url': f'{results_update_url}?lang={lang}', 
            'active_nav': 'calculs',
            'icon': 'refresh-ccw'
        },
        {
            'description': 'Moyennes par matière', 
            'url': f'{url}?type=matiere&lang={lang}', 
            'active_nav': 'matiere',
            'icon': 'layers'
        },
        # {
        #     'description': 'Importer par matière', 
        #     'url': f'{subject_import_url}?lang={lang}', 
        #     'active_nav': 'importation_matiere',
        #     'icon': 'arrow-down'
        # },
        {
            'description': 'Verrouillage notes', 
            'url': f'{lock_marks_url}?lang={lang}', 
            'active_nav': 'verrouillage_notes',
            'icon': 'lock'
        },
    ]
    return nav

def get_results_navs(cycle_results_url, lang, option=''):
    navs = []
    navs = [
        {
            'description': f'Primaire', 
            'url': f'{cycle_results_url}?lang={lang}&type=primaire&option={option}', 
            'active_nav': f'primaire'
        },
        {
            'description': f'Secondaire', 
            'url': f'{cycle_results_url}?lang={lang}&type=secondaire&option={option}', 
            'active_nav': f'secondaire'
        },
        # {
        #     'description': f'Par classe', 
        #     'url': f'{url}?lang={lang}&type=periode', 
        #     'active_nav': f'periode'
        # },
    ]
    return navs

def get_report_navs(url, lang):
    navs = []
    navs = [
        {
            'description': f'Bulletins par élève', 
            'url': f'{url}?lang={lang}&type=periode', 
            'active_nav': f'periode'
        },
    ]
    return navs


def get_levels_actions_navs(user, url, doc_type, education=None, title=''):
    arabic_school = (user.school.education == EDUCATION_ARABIC)
    navs = []

    if not education:
        navs = [
            {
                'description': "Classes " + ('françaises' if arabic_school else ''), 
                'url': f'{url}?education=fr&doc_type={doc_type}', 
                'active_nav': 'fr'
            },
        ]

        if arabic_school:
            navs.append({
                'description': f"Classes arabes", 
                'url': f'{url}?education=ar&doc_type={doc_type}', 
                'active_nav': 'ar'
            })
    else:
        navs = [
            {
                'description': f"{title or 'Classes'}", 
                'url': f'{url}?education={education}&doc_type={doc_type}', 
                'active_nav': f'{education}'
            },
        ]
    return navs


def get_cherifla_navs(url):
    return [
        {
            'description': "CEPE", 
            'url': f'{url}?exam=cepe', 
            'active_nav': 'cepe'
        },
        {
            'description': "BEPC", 
            'url': f'{url}?exam=bepc', 
            'active_nav': 'bepc'
        },
        {
            'description': "BAC", 
            'url': f'{url}?exam=bac', 
            'active_nav': 'bac'
        },
    ]

def get_certificates_navs(url):
    return get_cherifla_navs(url)


def get_simple_navs(url, description, active_nav, icon=''):
    return [
        {
            'description': description, 
            'url': url, 
            'active_nav': active_nav,
            'icon': icon
        }
    ]


def get_custom_marking_sheet_navs(user, url, lang):
    arabic_school = (user.school.education == EDUCATION_ARABIC)
    navs = get_simple_navs(
        url=f'{url}?lang={lang}',
        description="Fiches de notation",
        active_nav=lang
    )
    return navs
