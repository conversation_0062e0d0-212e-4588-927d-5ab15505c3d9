{% load widget_tweaks %}
{% load humanize %}
  <!-- Filters Section -->
  <div class="filters-section" hx-vals='{"statut": "{{active_nav}}", "education": "{{ active_nav }}"}'>
    <!-- Quick Filters -->
    <div class="quick-filters">
      <div class="search-wrapper">
        <i data-feather="search" class="search-icon"></i>
        <input type="search" id="search" name="search" 
              class="search-input form-control" 
              placeholder="Rechercher par nom ou matricule..."
              value="{{ search }}"
              hx-get="{{ request.path }}">
      </div>
      
      <div class="filter-dropdown my-auto" title="Filtrer les données" data-toggle="tooltip">
        <button class="filter-btn" id="filterToggle">
            <span data-feather="filter"></span>
        </button>
        <div class="filter-panel" id="filterPanel">
            <div class="filter-section">
              <label class="filter-label" for="{{filter_form.generic_level_fr.id_for_label }}">Niveau:</label>
              {% render_field filter_form.generic_level_fr class='filter-select form-control form-control-sm' %}
            </div>
            <div class="filter-section">
              <label class="filter-label" for="{{filter_form.level_fr.id_for_label }}">Classe:</label>
              {% render_field filter_form.level_fr class='filter-select form-control form-control-sm' %}
            </div>
            <div class="filter-section">
              <label class="filter-label" for="{{filter_form.gender.id_for_label }}">Sexe:</label>
              {% render_field filter_form.gender class='filter-select form-control form-control-sm' %}
            </div>
            <div class="filter-section">
              <label class="filter-label" for="{{filter_form.birth_year.id_for_label }}">Né(e) le:</label>
              {% render_field filter_form.birth_year class='filter-select form-control form-control-sm' %}
            </div>
            <div class="filter-section">
              <label class="filter-label" for="{{filter_form.generic_level_ar.id_for_label }}">Niveau Arabe:</label>
              {% render_field filter_form.generic_level_ar class='filter-select form-control form-control-sm' %}
            </div>
            <div class="filter-section">
              <label class="filter-label" for="{{filter_form.level_ar.id_for_label }}">Classe Arabe:</label>
              {% render_field filter_form.level_ar class='filter-select form-control form-control-sm' %}
            </div>
            <!-- <div class="filter-section">
              <label class="filter-label">Paiement:</label>
              <select class="filter-select form-control form-control-sm">
                <option value="">Tous</option>
                <option>Soldé</option>
                <option>Non soldé</option>
                <option>Aucun paiement</option>
              </select>
            </div> -->
        </div>
      </div>

      {% if perms.school.add_enrollment and not hide_add_btn %}
      {% if school_plan != PLAN_LEVEL %}
        <a class="btn btn-primary show-on-pc" href="{% url 'school:student_add' %}" 
            hx-get="{% url 'school:student_add' %}" 
            hx-target="#dialog-xl">
              <i data-feather="user-plus" class="align-middle"></i>
      <span class="show-on-pc">Ajouter élève</span>
          </a>
      <a class="btn btn-primary show-on-phone" href="{% url 'school:student_add_wizard' %}" 
          hx-get="{% url 'school:student_add_wizard' %}" 
          hx-target="#dialog">
              <i data-feather="user-plus" class="align-middle"></i>
      <span class="show-on-pc">Ajouter élève</span>
      </a>
      {% else %}
        <div class="btn-group">
          <a class="btn btn-primary" href="{% url 'school:student_add' %}" 
              hx-get="{% url 'school:student_add' %}" 
              hx-target="#dialog-xl">
                      <i data-feather="user-plus" class="align-middle"></i>
      <span class="show-on-pc">Ajouter élève</span>
          </a>
        </div>
      {% endif %}
    {% endif %}
    </div>
  
    <!-- Active Filters -->
    <div class="filter-chips d-none d-md-block">
      {% if request.GET.generic_level_fr %}
      <div class="filter-chip" data-filter="{{ filter_form.generic_level_fr.name }}">
        Niveau: {{ generic_level_fr_filter }}
        <!-- <i data-feather="x"></i> -->
      </div>
      {% endif %}
      {% if request.GET.generic_level_ar %}
      <div class="filter-chip" data-filter="{{ filter_form.generic_level_ar.name }}">
        Niveau arabe: {{ generic_level_ar_filter }}
        <!-- <i data-feather="x"></i> -->
      </div>
      {% endif %}
      {% if request.GET.gender %}
      <div class="filter-chip">
        Genre: {% if request.GET.gender == 'F' %} Filles {% else %} Garçons {% endif %}
        <!-- <i data-feather="x"></i> -->
      </div>
      {% endif %}

      {% if request.GET.birth_year %}
      <div class="filter-chip">
        Né en: {{ request.GET.birth_year }}
        <!-- <i data-feather="x"></i> -->
      </div>
      {% endif %}
    </div>
  
    {% if result_found %}
    <div class="search-results-info">
      <div class="result-count">
        <i data-feather="filter" class="feather-16"></i>
        {{ result_boys|add:result_girls }} résultats
      </div>
      <div class="gender-split">
        <span class="gender-badge boys-badge">
          <i data-feather="user" class="feather-14"></i>
          {{ result_boys }} garçons
        </span>
        <span class="gender-badge girls-badge">
          <i data-feather="user" class="feather-14"></i>
          {{ result_girls }} filles
        </span>
      </div>
    </div>
    {% endif %}
  </div>
