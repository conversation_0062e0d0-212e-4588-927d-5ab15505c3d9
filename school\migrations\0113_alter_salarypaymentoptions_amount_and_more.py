# Generated by Django 5.1.4 on 2024-12-22 07:31

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('school', '0112_salarypaymentoptions_rate_and_more'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='salarypaymentoptions',
            name='amount',
            field=models.PositiveIntegerField(blank=True, null=True, verbose_name='montant'),
        ),
        migrations.AlterField(
            model_name='salarypaymentoptions',
            name='rate',
            field=models.PositiveIntegerField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='taux'),
        ),
    ]
