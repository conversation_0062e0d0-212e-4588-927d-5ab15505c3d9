{% load humanize %}
<div class="tile" id="tile" hx-get="{% url 'school:staff_payments' %}" hx-trigger="saved from:body" hx-target="#app-content">
    <div class="tile-title-w-btn">
        <div class="btn-group p-2">
            <span class="p-2 bg-light rounded font-weight-bold">Budget Mensuel: {{ budget|intcomma }} F CFA</span>
        </div>
        <a class="btn btn-success" href="" 
           hx-get="{% url 'school:staff_salary_decision_add' %}" 
           hx-target="#dialog-xl">+ Décision de Paie</a>
    </div>
    <div class="table-responsive">
        <table class="table table-striped table-sm table-bordered" id="datatable">
            <thead class="bg-primary text-white">
            <tr>
                <th>Nom</th>
                <th>Prénom</th>
                <th>Salaire de base</th>
                <th>SEPT</th>
                <th>OCT</th>
                <th>NOV</th>
                <th>DEC</th>
                <th>JAN</th>
                <th>FEV</th>
                <th>MARS</th>
                <th>AVR</th>
                <th>MAI</th>
                <th>JUIN</th>
                <th>JUIL</th>
                <th>AOUT</th>
            </tr>
            </thead>
            <tbody>
                {% for staff in staff_list %}
                <tr>
                    <td class="align-middle">{{ staff.last_name }}</td>
                    <td class="align-middle">{{ staff.first_name }}</td>
                    <td class="align-middle">{{ staff.salary }}</td>
                    <td class="align-middle">
                        {% if not staff.september %}
                            <a href="" class="btn btn-sm btn-danger" data-toggle="tooltip" 
                                title="Cliquer pour marquer comme payé" 
                                hx-get="{% url 'school:staff_payment_add' %}?mois=9&staff={{ staff.pk }}"
                                hx-target="#dialog">
                                <span data-feather="x" class="feather-16 text-white"></span>
                            </a>
                        {% else %}
                            <a href="{% url 'school:staff_payment_report' %}?staff={{ staff.pk}}&month=9" class="btn btn-sm btn-success" data-toggle="tooltip" title="Générer bulletin de paie"><span data-feather="file-text" class="feather-16"></span> </a>
                        {% endif %}
                    </td>
                    <td class="align-middle">
                        {% if not staff.october %}
                            <a href="" class="btn btn-sm btn-danger" data-toggle="tooltip" 
                                title="Cliquer pour marquer comme payé" 
                                hx-get="{% url 'school:staff_payment_add' %}?mois=10&staff={{ staff.pk }}"
                                hx-target="#dialog">
                                <span data-feather="x" class="feather-16 text-white"></span>
                            </a>
                        {% else %}
                            <a href="{% url 'school:staff_payment_report' %}?staff={{ staff.pk}}&month=10" class="btn btn-sm btn-success" data-toggle="tooltip" title="Générer bulletin de paie"><span data-feather="file-text" class="feather-16"></span> </a>
                        {% endif %}
                    </td>
                    <td class="align-middle">
                        {% if not staff.november %}
                            <a href="" class="btn btn-sm btn-danger" data-toggle="tooltip" 
                                title="Cliquer pour marquer comme payé" 
                                hx-get="{% url 'school:staff_payment_add' %}?mois=11&staff={{ staff.pk }}"
                                hx-target="#dialog">
                                <span data-feather="x" class="feather-16 text-white"></span>
                            </a>
                        {% else %}
                            <a href="{% url 'school:staff_payment_report' %}?staff={{ staff.pk}}&month=11" class="btn btn-sm btn-success" data-toggle="tooltip" title="Générer bulletin de paie"><span data-feather="file-text" class="feather-16"></span> </a>
                        {% endif %}
                    </td>
                    <td class="align-middle">
                        {% if not staff.december %}
                            <a href="" class="btn btn-sm btn-danger" data-toggle="tooltip" 
                                title="Cliquer pour marquer comme payé" 
                                hx-get="{% url 'school:staff_payment_add' %}?mois=12&staff={{ staff.pk }}"
                                hx-target="#dialog">
                                <span data-feather="x" class="feather-16 text-white"></span>
                            </a>
                        {% else %}
                            <a href="{% url 'school:staff_payment_report' %}?staff={{ staff.pk}}&month=12" class="btn btn-sm btn-success" data-toggle="tooltip" title="Générer bulletin de paie"><span data-feather="file-text" class="feather-16"></span> </a>
                        {% endif %}
                    </td>
                    <td class="align-middle">
                        {% if not staff.january %}
                            <a href="" class="btn btn-sm btn-danger" data-toggle="tooltip" 
                                title="Cliquer pour marquer comme payé" 
                                hx-get="{% url 'school:staff_payment_add' %}?mois=1&staff={{ staff.pk }}"
                                hx-target="#dialog">
                                <span data-feather="x" class="feather-16 text-white"></span>
                            </a>
                        {% else %}
                            <a href="{% url 'school:staff_payment_report' %}?staff={{ staff.pk}}&month=1" class="btn btn-sm btn-success" data-toggle="tooltip" title="Générer bulletin de paie"><span data-feather="file-text" class="feather-16"></span> </a>
                        {% endif %}
                    </td>
                    <td class="align-middle">
                        {% if not staff.february %}
                            <a href="" class="btn btn-sm btn-danger" data-toggle="tooltip" 
                                title="Cliquer pour marquer comme payé" 
                                hx-get="{% url 'school:staff_payment_add' %}?mois=2&staff={{ staff.pk }}"
                                hx-target="#dialog">
                                <span data-feather="x" class="feather-16 text-white"></span>
                            </a>
                        {% else %}
                            <a href="{% url 'school:staff_payment_report' %}?staff={{ staff.pk}}&month=2" class="btn btn-sm btn-success" data-toggle="tooltip" title="Générer bulletin de paie"><span data-feather="file-text" class="feather-16"></span> </a>
                        {% endif %}
                    </td>
                    <td class="align-middle">
                        {% if not staff.march %}
                            <a href="" class="btn btn-sm btn-danger" data-toggle="tooltip" 
                                title="Cliquer pour marquer comme payé" 
                                hx-get="{% url 'school:staff_payment_add' %}?mois=3&staff={{ staff.pk }}"
                                hx-target="#dialog">
                                <span data-feather="x" class="feather-16 text-white"></span>
                            </a>
                        {% else %}
                            <a href="{% url 'school:staff_payment_report' %}?staff={{ staff.pk}}&month=3" class="btn btn-sm btn-success" data-toggle="tooltip" title="Générer bulletin de paie"><span data-feather="file-text" class="feather-16"></span> </a>
                        {% endif %}
                    </td>
                    <td class="align-middle">
                        {% if not staff.april %}
                            <a href="" class="btn btn-sm btn-danger" data-toggle="tooltip" 
                                title="Cliquer pour marquer comme payé" 
                                hx-get="{% url 'school:staff_payment_add' %}?mois=4&staff={{ staff.pk }}"
                                hx-target="#dialog">
                                <span data-feather="x" class="feather-16 text-white"></span>
                            </a>
                        {% else %}
                            <a href="{% url 'school:staff_payment_report' %}?staff={{ staff.pk}}&month=4" class="btn btn-sm btn-success" data-toggle="tooltip" title="Générer bulletin de paie"><span data-feather="file-text" class="feather-16"></span> </a>
                        {% endif %}
                    </td>
                    <td class="align-middle">
                        {% if not staff.may %}
                            <a href="" class="btn btn-sm btn-danger" data-toggle="tooltip" 
                                title="Cliquer pour marquer comme payé" 
                                hx-get="{% url 'school:staff_payment_add' %}?mois=5&staff={{ staff.pk }}"
                                hx-target="#dialog">
                                <span data-feather="x" class="feather-16 text-white"></span>
                            </a>
                        {% else %}
                            <a href="{% url 'school:staff_payment_report' %}?staff={{ staff.pk}}&month=5" class="btn btn-sm btn-success" data-toggle="tooltip" title="Générer bulletin de paie"><span data-feather="file-text" class="feather-16"></span> </a>
                        {% endif %}
                    </td>
                    <td class="align-middle">
                        {% if not staff.june %}
                            <a href="" class="btn btn-sm btn-danger" data-toggle="tooltip" 
                                title="Cliquer pour marquer comme payé" 
                                hx-get="{% url 'school:staff_payment_add' %}?mois=6&staff={{ staff.pk }}"
                                hx-target="#dialog">
                                <span data-feather="x" class="feather-16 text-white"></span>
                            </a>
                        {% else %}
                            <a href="{% url 'school:staff_payment_report' %}?staff={{ staff.pk}}&month=6" class="btn btn-sm btn-success" data-toggle="tooltip" title="Générer bulletin de paie"><span data-feather="file-text" class="feather-16"></span> </a>
                        {% endif %}
                    </td>
                    <td class="align-middle">
                        {% if not staff.july %}
                            <a href="" class="btn btn-sm btn-danger" data-toggle="tooltip" 
                                title="Cliquer pour marquer comme payé" 
                                hx-get="{% url 'school:staff_payment_add' %}?mois=7&staff={{ staff.pk }}"
                                hx-target="#dialog">
                                <span data-feather="x" class="feather-16 text-white"></span>
                            </a>
                        {% else %}
                            <a href="{% url 'school:staff_payment_report' %}?staff={{ staff.pk}}&month=7" class="btn btn-sm btn-success" data-toggle="tooltip" title="Générer bulletin de paie"><span data-feather="file-text" class="feather-16"></span> </a>
                        {% endif %}
                    </td>
                    <td class="align-middle">
                        {% if not staff.august %}
                            <a href="" class="btn btn-sm btn-danger" data-toggle="tooltip" 
                                title="Cliquer pour marquer comme payé" 
                                hx-get="{% url 'school:staff_payment_add' %}?mois=8&staff={{ staff.pk }}"
                                hx-target="#dialog">
                                <span data-feather="x" class="feather-16 text-white"></span>
                            </a>
                        {% else %}
                            <a href="{% url 'school:staff_payment_report' %}?staff={{ staff.pk}}&month=8" class="btn btn-sm btn-success" data-toggle="tooltip" title="Générer bulletin de paie"><span data-feather="file-text" class="feather-16"></span> </a>
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
<script>
    $(function () {
        $('[data-toggle="tooltip"]').tooltip()
    })
</script>