# Generated by Django 5.0.2 on 2024-03-07 08:01

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('exams', '0049_alter_studentsubjectgroupaverage_group_and_more'),
        ('school', '0056_pdffile'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='pdffile',
            options={'verbose_name': 'Fichier PDF', 'verbose_name_plural': 'Fichiers PDF'},
        ),
        migrations.AlterField(
            model_name='pdffile',
            name='category',
            field=models.CharField(choices=[('RS', 'Matrice des moyennes'), ('R2', 'Matrice des moyennes simplifiée'), ('RP', 'Relevé de notes'), ('MS', 'Fiche de notation'), ('FT', 'Fiche de table'), ('CL', 'Liste de classe'), ('RG', "Liste d'appel"), ('DR', "Liste d'appel journalier")], max_length=2),
        ),
        migrations.AlterField(
            model_name='pdffile',
            name='term',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='exams.term'),
        ),
    ]
