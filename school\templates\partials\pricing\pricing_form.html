{% extends 'partials/modal.html' %}
{% load widget_tweaks %}

{% block modal_title %} Frais d'écolage {% endblock %}

{% block modal_body %}
    <div class="form-row">
        {% for field in form %}
        <div class="form-group {% if request.GET.education == 'ar' and field.name == 'student_status' %} d-none {% elif request.GET.cycle == 'P' %} {% if field.name == 'student_status' %} d-none {% elif field.name == 'education' or field.name == 'cycle' %} col-6 {% else %} col-4 {% endif %} {% else %} col-4  {% endif %}">
            <label for="{{ field.id_for_label }}">{{ field.label }}</label>
            {% render_field field class='form-control' %}
            <span class="invalid-feedback">{{ field.errors|first }}</span>
            {% if field.name == 'student_status' %}
            <p class="form-text text-muted mb-0">
                Veuillez laisser vide pour le primaire
            </p>
            {% endif %}
        </div>
        {% endfor %}
    </div>
{% endblock %}

{% block modal_footer %} 
    <button type="submit" class="btn btn-success">Enregistrer</button>
{% endblock %}