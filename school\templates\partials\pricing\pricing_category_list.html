<div class="tile" id="tile" hx-get="{% url 'school:pricing_categories' %}" hx-trigger="saved from:body" hx-target="#app-content" %>
    <div class="tile-title-w-btn">
        <div class="btn-group">
            <a class="btn btn-success" href="" 
               hx-get="{% url 'school:pricing_category_create' %}" 
               hx-target="#dialog">+ Ajouter une rubrique</a>
        </div>
    </div>
    <div class="table-responsive">
        <table class="table table-striped table-sm" id="datatable">
            <thead class="bg-primary text-white">
            <tr>
                <th>Libellé</th>
                <th class="text-right">Actions</th>
            </tr>
            </thead>
            <tbody>
            {% for category in categories %}
            <tr>
                <td class="align-middle">{{ category.label }}</td>
                <td class="align-middle">
                    <div class="dropdown text-right">
                        <button class="btn btn-sm dropdown" style="background-color: inherit;" type="button" data-toggle="dropdown" aria-expanded="false">
                            <i data-feather="more-vertical" class="feather-16"></i>
                        </button>
                        <div class="dropdown-menu">
                          <a class="dropdown-item" href="#"
                          hx-get="{% url 'school:pricing_category_edit' category.id %}"
                          hx-target="#dialog">Modifier</a>
                        </div>
                      </div>
                </td>
            </tr>
            {% endfor %}
            </tbody>
        </table>
    </div>
</div>