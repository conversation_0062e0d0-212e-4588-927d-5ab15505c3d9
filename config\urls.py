from django.conf import settings
from django.conf.urls.static import static
from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path('admin/', admin.site.urls),
    path('accounts/', include('users.urls')),
    path('accounts/', include('django.contrib.auth.urls')),
    path('', include('school.urls')),
    path('', include('pwa.urls')),
    path('examens/', include('exams.urls')),
    path('__debug__/', include('debug_toolbar.urls')),
    path('celery-progress/', include('celery_progress.urls')),
    path('grappelli/', include('grappelli.urls')),
]

urlpatterns += static(settings.MEDIA_URL, 
    document_root=settings.MEDIA_ROOT)

# Custom error pages
handler404 = 'exams.views.custom_404'
handler500 = 'exams.views.custom_500'